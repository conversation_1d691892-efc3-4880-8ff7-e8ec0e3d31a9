import { getRedisClient } from '../utils/redis';

interface FreePlaySession {
  userId: string;
  temporaryTokens: number;
  roundsPlayed: number;
  startTime: number;
  active: boolean;
}

export class FreePlayManager {
  private static readonly TEMP_TOKENS = 100; // Amount of temporary tokens for free play
  private static readonly MAX_ROUNDS = 5; // Maximum number of free play rounds
  private static readonly SESSION_EXPIRY = 3600; // 1 hour in seconds

  static async startFreePlaySession(userId: string): Promise<FreePlaySession> {
    const redis = await getRedisClient();
    const session: FreePlaySession = {
      userId,
      temporaryTokens: this.TEMP_TOKENS,
      roundsPlayed: 0,
      startTime: Date.now(),
      active: true
    };

    await redis.setex(
      `free_play:${userId}`,
      this.SESSION_EXPIRY,
      JSON.stringify(session)
    );

    return session;
  }

  static async getFreePlaySession(userId: string): Promise<FreePlaySession | null> {
    const redis = await getRedisClient();
    const session = await redis.get(`free_play:${userId}`);
    return session ? JSON.parse(session) : null;
  }

  static async updateSession(userId: string, updates: Partial<FreePlaySession>): Promise<FreePlaySession | null> {
    const redis = await getRedisClient();
    const session = await this.getFreePlaySession(userId);
    
    if (!session) return null;

    const updatedSession = {
      ...session,
      ...updates
    };

    await redis.setex(
      `free_play:${userId}`,
      this.SESSION_EXPIRY,
      JSON.stringify(updatedSession)
    );

    return updatedSession;
  }

  static async incrementRounds(userId: string): Promise<FreePlaySession | null> {
    const session = await this.getFreePlaySession(userId);
    if (!session || !session.active) return null;

    const roundsPlayed = session.roundsPlayed + 1;
    const active = roundsPlayed < this.MAX_ROUNDS;

    return this.updateSession(userId, { roundsPlayed, active });
  }

  static async endFreePlaySession(userId: string): Promise<void> {
    const redis = await getRedisClient();
    await redis.del(`free_play:${userId}`);
  }

  static async deductTokens(userId: string, amount: number): Promise<FreePlaySession | null> {
    const session = await this.getFreePlaySession(userId);
    if (!session || !session.active) return null;

    const temporaryTokens = Math.max(0, session.temporaryTokens - amount);
    const active = temporaryTokens > 0;

    return this.updateSession(userId, { temporaryTokens, active });
  }

  static async isInFreePlay(userId: string): Promise<boolean> {
    const session = await this.getFreePlaySession(userId);
    return !!(session?.active);
  }
} 