import React from 'react';

// Types
interface PlayerStatsData {
  gamesPlayed: number;
  gamesWon: number;
  tokensEarned: number;
  loomCoinsEarned: number;
  winRate: number;
  currentStreak: number;
  bestStreak: number;
  totalTimeSpent: number; // in minutes
  favoriteGameMode: string;
  rank: number;
  level: number;
  experience: number;
  nextLevelExp: number;
}

interface PlayerStatsProps {
  stats: PlayerStatsData;
  playerName?: string;
  playerIcon?: string;
}

const PlayerStats: React.FC<PlayerStatsProps> = ({ 
  stats, 
  playerName = "Player", 
  playerIcon 
}) => {
  const formatTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const getProgressPercentage = (): number => {
    return (stats.experience / stats.nextLevelExp) * 100;
  };

  return (
    <div className="player-stats">
      {/* Player Header */}
      <div className="player-stats-header">
        {playerIcon && (
          <img 
            src={playerIcon} 
            alt={`${playerName}'s avatar`} 
            className="player-stats-avatar"
          />
        )}
        <div className="player-stats-info">
          <h3 className="player-stats-name">{playerName}</h3>
          <div className="player-stats-level">
            Level {stats.level} • Rank #{stats.rank}
          </div>
        </div>
      </div>

      {/* Experience Progress */}
      <div className="experience-section">
        <div className="experience-bar-container">
          <div className="experience-label">
            {stats.experience} / {stats.nextLevelExp} XP
          </div>
          <div className="experience-bar">
            <div 
              className="experience-progress"
              style={{ width: `${getProgressPercentage()}%` }}
            />
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="stats-grid">
        <div className="stat-item">
          <div className="stat-value">{stats.gamesPlayed}</div>
          <div className="stat-label">Games Played</div>
        </div>
        
        <div className="stat-item">
          <div className="stat-value">{stats.gamesWon}</div>
          <div className="stat-label">Games Won</div>
        </div>
        
        <div className="stat-item">
          <div className="stat-value">{(stats.winRate * 100).toFixed(1)}%</div>
          <div className="stat-label">Win Rate</div>
        </div>
        
        <div className="stat-item">
          <div className="stat-value">{stats.tokensEarned.toLocaleString()}</div>
          <div className="stat-label">Tokens Earned</div>
        </div>
        
        <div className="stat-item">
          <div className="stat-value">{stats.loomCoinsEarned}</div>
          <div className="stat-label">LoomCoins</div>
        </div>
        
        <div className="stat-item">
          <div className="stat-value">{stats.currentStreak}</div>
          <div className="stat-label">Current Streak</div>
        </div>
        
        <div className="stat-item">
          <div className="stat-value">{stats.bestStreak}</div>
          <div className="stat-label">Best Streak</div>
        </div>
        
        <div className="stat-item">
          <div className="stat-value">{formatTime(stats.totalTimeSpent)}</div>
          <div className="stat-label">Time Played</div>
        </div>
      </div>

      {/* Favorite Game Mode */}
      <div className="favorite-mode-section">
        <div className="favorite-mode-label">Favorite Game Mode</div>
        <div className="favorite-mode-value">{stats.favoriteGameMode}</div>
      </div>

      <style>{`
        .player-stats {
          background-color: rgba(255, 255, 255, 0.05);
          border-radius: 12px;
          padding: 20px;
          color: #fff;
          max-width: 500px;
          margin: 0 auto;
        }

        .player-stats-header {
          display: flex;
          align-items: center;
          margin-bottom: 20px;
          gap: 15px;
        }

        .player-stats-avatar {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          object-fit: cover;
          border: 2px solid #ff8c00;
        }

        .player-stats-name {
          margin: 0;
          font-size: 1.4em;
          color: #ffeb3b;
        }

        .player-stats-level {
          color: #aaa;
          font-size: 0.9em;
        }

        .experience-section {
          margin-bottom: 25px;
        }

        .experience-bar-container {
          background-color: rgba(255, 255, 255, 0.1);
          border-radius: 10px;
          padding: 10px;
        }

        .experience-label {
          text-align: center;
          font-size: 0.9em;
          margin-bottom: 8px;
          color: #ccc;
        }

        .experience-bar {
          height: 8px;
          background-color: rgba(255, 255, 255, 0.2);
          border-radius: 4px;
          overflow: hidden;
        }

        .experience-progress {
          height: 100%;
          background: linear-gradient(90deg, #ff8c00, #ffeb3b);
          transition: width 0.3s ease;
        }

        .stats-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 15px;
          margin-bottom: 20px;
        }

        .stat-item {
          text-align: center;
          background-color: rgba(255, 255, 255, 0.05);
          padding: 15px 10px;
          border-radius: 8px;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-value {
          font-size: 1.5em;
          font-weight: bold;
          color: #ff8c00;
          margin-bottom: 5px;
        }

        .stat-label {
          font-size: 0.8em;
          color: #aaa;
          text-transform: uppercase;
        }

        .favorite-mode-section {
          text-align: center;
          background-color: rgba(255, 235, 59, 0.1);
          padding: 15px;
          border-radius: 8px;
          border: 1px solid rgba(255, 235, 59, 0.3);
        }

        .favorite-mode-label {
          font-size: 0.8em;
          color: #aaa;
          text-transform: uppercase;
          margin-bottom: 5px;
        }

        .favorite-mode-value {
          font-size: 1.1em;
          font-weight: bold;
          color: #ffeb3b;
        }

        @media (max-width: 480px) {
          .stats-grid {
            grid-template-columns: 1fr;
          }
          
          .stat-value {
            font-size: 1.3em;
          }
        }
      `}</style>
    </div>
  );
};

export default PlayerStats;
