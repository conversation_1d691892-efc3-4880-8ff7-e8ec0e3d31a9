#!/bin/bash

# Load environment variables
source ../.env.production

# Set backup directory
BACKUP_DIR="/opt/loomloot/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Backup PostgreSQL database
echo "Backing up PostgreSQL database..."
docker exec loomloot_db_1 pg_dump -U $DB_USER $DB_NAME | gzip > "$BACKUP_DIR/db_backup_$DATE.sql.gz"

# Backup Redis data
echo "Backing up Redis data..."
docker exec loomloot_redis_1 redis-cli save
docker cp loomloot_redis_1:/data/dump.rdb "$BACKUP_DIR/redis_backup_$DATE.rdb"

# Backup application files
echo "Backing up application files..."
tar -czf "$BACKUP_DIR/app_backup_$DATE.tar.gz" \
    --exclude="node_modules" \
    --exclude=".next" \
    --exclude="*.log" \
    ../

# Cleanup old backups (keep last 7 days)
echo "Cleaning up old backups..."
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +7 -delete
find $BACKUP_DIR -name "redis_backup_*.rdb" -mtime +7 -delete
find $BACKUP_DIR -name "app_backup_*.tar.gz" -mtime +7 -delete

# Create backup report
echo "Creating backup report..."
echo "Backup completed at $(date)" > "$BACKUP_DIR/backup_report_$DATE.txt"
echo "Database backup: db_backup_$DATE.sql.gz" >> "$BACKUP_DIR/backup_report_$DATE.txt"
echo "Redis backup: redis_backup_$DATE.rdb" >> "$BACKUP_DIR/backup_report_$DATE.txt"
echo "Application backup: app_backup_$DATE.tar.gz" >> "$BACKUP_DIR/backup_report_$DATE.txt"

# Optional: Upload to remote storage (uncomment and configure as needed)
# aws s3 sync $BACKUP_DIR s3://your-bucket/loomloot-backups/

echo "Backup completed successfully!" 