import crypto from 'crypto';

export class GameLogic {
  // Secure random number generation
  static generateSecureRandom(min: number, max: number): number {
    const range = max - min + 1;
    const bytes = crypto.randomBytes(4);
    const value = bytes.readUInt32BE(0);
    return min + (value % range);
  }

  // Lottery number generation
  static generateLotteryNumbers(): { numbers: number[]; megaNumber: number } {
    const numbers = new Set<number>();
    while (numbers.size < 5) {
      numbers.add(this.generateSecureRandom(1, 49));
    }
    const megaNumber = this.generateSecureRandom(1, 25);

    return {
      numbers: Array.from(numbers).sort((a, b) => a - b),
      megaNumber
    };
  }

  // Check lottery ticket matches
  static checkLotteryWin(
    ticket: number[],
    ticketMega: number,
    winning: number[],
    winningMega: number
  ): { matches: number; megaMatch: boolean; prize: number } {
    const matches = ticket.filter(num => winning.includes(num)).length;
    const megaMatch = ticketMega === winningMega;

    // Prize structure based on Oregon Megabucks
    const prize = this.calculateLotteryPrize(matches, megaMatch);

    return { matches, megaMatch, prize };
  }

  // Calculate lottery prize
  private static calculateLotteryPrize(matches: number, megaMatch: boolean): number {
    if (matches === 5 && megaMatch) return 1000000; // Jackpot
    if (matches === 5) return 50000;
    if (matches === 4 && megaMatch) return 5000;
    if (matches === 4) return 500;
    if (matches === 3 && megaMatch) return 50;
    if (matches === 3) return 10;
    if (matches === 2 && megaMatch) return 5;
    if (megaMatch) return 2;
    return 0;
  }

  // 1v1 Game outcome determination
  static determineGameWinner(gameId: string, player1: string, player2: string): string {
    // Use gameId as seed for deterministic but unpredictable outcome
    const seed = crypto.createHash('sha256').update(gameId).digest();
    const randomValue = seed.readUInt32BE(0) / 0xffffffff; // Normalize to 0-1

    return randomValue < 0.5 ? player1 : player2;
  }

  // Raffle winner selection
  static selectRaffleWinner(tickets: string[]): string {
    if (!tickets.length) throw new Error('No tickets available');
    const winningIndex = this.generateSecureRandom(0, tickets.length - 1);
    return tickets[winningIndex];
  }
} 