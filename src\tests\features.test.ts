import { GenericContainer, StartedTestContainer } from 'testcontainers';
import { PrismaClient } from '@prisma/client';
import { createMocks } from 'node-mocks-http';
import addFeatureHandler from '../pages/api/admin/readmerules.mdc/addfeatures.mdc';
import getFeaturesHandler from '../pages/api/admin/readmerules.mdc/getfeatures.mdc';

describe('Feature Management API', () => {
  let container: StartedTestContainer;
  let prisma: PrismaClient;

  beforeAll(async () => {
    // Start PostgreSQL container
    container = await new GenericContainer('postgres:14')
      .withExposedPorts(5432)
      .withEnvironment({
        POSTGRES_USER: 'test',
        POSTGRES_PASSWORD: 'test',
        POSTGRES_DB: 'testdb'
      })
      .start();

    // Initialize Prisma client with container connection
    const connectionString = `postgresql://test:test@${container.getHost()}:${container.getMappedPort(5432)}/testdb`;
    prisma = new PrismaClient({
      datasources: {
        db: {
          url: connectionString
        }
      }
    });

    // Run migrations
    await prisma.$executeRaw`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`;
    // Add your schema migrations here
  });

  afterAll(async () => {
    await prisma.$disconnect();
    await container.stop();
  });

  beforeEach(async () => {
    // Clean up database before each test
    await prisma.platformFeature.deleteMany();
  });

  describe('POST /api/admin/readmerules.mdc/addfeatures.mdc', () => {
    it('should create an admin feature when user is admin', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        body: {
          title: 'Test Admin Feature',
          description: 'Test Description',
          category: 'Admin',
          isAdminFeature: true,
          priority: 1
        }
      });

      // Mock authentication
      jest.spyOn(require('next-auth/next'), 'getServerSession')
        .mockResolvedValue({
          user: { email: '<EMAIL>', isAdmin: true }
        });

      await addFeatureHandler(req, res);

      expect(res._getStatusCode()).toBe(201);
      const data = JSON.parse(res._getData());
      expect(data.title).toBe('Test Admin Feature');
      expect(data.isAdminFeature).toBe(true);
    });

    it('should create an onboarding feature', async () => {
      const { req, res } = createMocks({
        method: 'POST',
        body: {
          title: 'Complete Profile',
          description: 'Add your information',
          category: 'Onboarding',
          onboardingStep: 1,
          requiredForProgress: true
        }
      });

      // Mock authentication
      jest.spyOn(require('next-auth/next'), 'getServerSession')
        .mockResolvedValue({
          user: { email: '<EMAIL>' }
        });

      await addFeatureHandler(req, res);

      expect(res._getStatusCode()).toBe(201);
      const data = JSON.parse(res._getData());
      expect(data.onboardingStep).toBe(1);
      expect(data.requiredForProgress).toBe(true);
    });
  });

  describe('GET /api/admin/readmerules.mdc/getfeatures.mdc', () => {
    beforeEach(async () => {
      // Add test data
      await prisma.platformFeature.createMany({
        data: [
          {
            title: 'Admin Feature',
            description: 'Test',
            category: 'Admin',
            isAdminFeature: true,
            createdBy: '<EMAIL>'
          },
          {
            title: 'User Feature',
            description: 'Test',
            category: 'Onboarding',
            onboardingStep: 1,
            createdBy: '<EMAIL>'
          }
        ]
      });
    });

    it('should return filtered features based on query parameters', async () => {
      const { req, res } = createMocks({
        method: 'GET',
        query: {
          category: 'Onboarding'
        }
      });

      // Mock authentication
      jest.spyOn(require('next-auth/next'), 'getServerSession')
        .mockResolvedValue({
          user: { email: '<EMAIL>' }
        });

      await getFeaturesHandler(req, res);

      expect(res._getStatusCode()).toBe(200);
      const data = JSON.parse(res._getData());
      expect(data.length).toBe(1);
      expect(data[0].category).toBe('Onboarding');
    });
  });
}); 