import { Phone<PERSON>hain } from '../services/blockchain/phoneChain';
import { PrismaClient } from '@prisma/client';
import { redis } from '../utils/redis';
import WebSocket from 'ws';

const prisma = new PrismaClient();

describe('PhoneChain', () => {
  let phoneChain: PhoneChain;
  let mockSocket: WebSocket;

  beforeAll(async () => {
    phoneChain = PhoneChain.getInstance();
    await redis.flushall();
  });

  beforeEach(() => {
    mockSocket = new WebSocket('ws://localhost:6001');
  });

  afterEach(async () => {
    mockSocket.close();
    await redis.flushall();
  });

  afterAll(async () => {
    await prisma.$disconnect();
    await redis.quit();
  });

  describe('Node Management', () => {
    it('should successfully add a new node to the network', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+1234567890',
          isVerified: true
        }
      });

      const nodeData = {
        userId: testUser.id,
        deviceId: 'test-device-1',
        publicKey: 'test-public-key',
        storage: 2 * 1024 * 1024 * 1024, // 2GB
        bandwidth: 2000000 // 2Mbps
      };

      await new Promise<void>((resolve) => {
        mockSocket.on('open', () => {
          mockSocket.send(JSON.stringify({
            type: 'JOIN_NETWORK',
            node: nodeData
          }));
        });

        mockSocket.on('message', (data) => {
          const message = JSON.parse(data.toString());
          if (message.type === 'NEW_NODE') {
            expect(message.node).toBe(nodeData.deviceId);
            resolve();
          }
        });
      });
    });

    it('should reject node with insufficient resources', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+1234567891',
          isVerified: true
        }
      });

      const nodeData = {
        userId: testUser.id,
        deviceId: 'test-device-2',
        publicKey: 'test-public-key',
        storage: 100 * 1024 * 1024, // 100MB (insufficient)
        bandwidth: 100000 // 100Kbps (insufficient)
      };

      await new Promise<void>((resolve) => {
        mockSocket.on('open', () => {
          mockSocket.send(JSON.stringify({
            type: 'JOIN_NETWORK',
            node: nodeData
          }));
        });

        mockSocket.on('close', () => {
          resolve();
        });
      });
    });
  });

  describe('Data Storage', () => {
    it('should successfully store and validate data', async () => {
      const testData = {
        type: 'USER_PROFILE',
        data: {
          userId: 'test-user',
          name: 'Test User',
          preferences: { theme: 'dark' }
        }
      };

      await new Promise<void>((resolve) => {
        mockSocket.on('open', () => {
          mockSocket.send(JSON.stringify({
            type: 'STORE_DATA',
            data: testData
          }));
        });

        mockSocket.on('message', (data) => {
          const message = JSON.parse(data.toString());
          if (message.type === 'VALIDATE_BLOCK') {
            expect(message.block.data).toEqual(testData);
            expect(message.validators.length).toBeGreaterThan(0);
            resolve();
          }
        });
      });
    });

    it('should maintain data consistency across nodes', async () => {
      const testData = {
        type: 'GAME_STATE',
        data: {
          gameId: 'test-game',
          state: { score: 100 }
        }
      };

      const mockSocket2 = new WebSocket('ws://localhost:6001');

      await new Promise<void>((resolve) => {
        mockSocket.on('open', () => {
          mockSocket.send(JSON.stringify({
            type: 'STORE_DATA',
            data: testData
          }));
        });

        mockSocket2.on('message', (data) => {
          const message = JSON.parse(data.toString());
          if (message.type === 'NEW_BLOCK') {
            expect(message.block.data).toEqual(testData);
            mockSocket2.close();
            resolve();
          }
        });
      });
    });
  });

  describe('Block Validation', () => {
    it('should validate blocks with correct hash', async () => {
      const testBlock = {
        index: 1,
        timestamp: Date.now(),
        data: { test: 'data' },
        previousHash: '0000000000000000',
        hash: '',
        nonce: 0,
        validator: 'test-validator'
      };

      await new Promise<void>((resolve) => {
        mockSocket.on('open', () => {
          mockSocket.send(JSON.stringify({
            type: 'VALIDATE_BLOCK',
            block: testBlock
          }));
        });

        mockSocket.on('message', (data) => {
          const message = JSON.parse(data.toString());
          if (message.type === 'NEW_BLOCK') {
            expect(message.block.hash).toMatch(/^0{4}/); // Check difficulty
            resolve();
          }
        });
      });
    });

    it('should reject invalid blocks', async () => {
      const invalidBlock = {
        index: 1,
        timestamp: Date.now(),
        data: { test: 'data' },
        previousHash: 'invalid',
        hash: 'invalid',
        nonce: 0,
        validator: 'test-validator'
      };

      await new Promise<void>((resolve) => {
        mockSocket.on('open', () => {
          mockSocket.send(JSON.stringify({
            type: 'VALIDATE_BLOCK',
            block: invalidBlock
          }));
        });

        mockSocket.on('message', (data) => {
          const message = JSON.parse(data.toString());
          if (message.type === 'BLOCK_REJECTED') {
            expect(message.reason).toBe('Invalid block hash');
            resolve();
          }
        });
      });
    });
  });

  describe('Chain Synchronization', () => {
    it('should sync chain state for new nodes', async () => {
      await new Promise<void>((resolve) => {
        mockSocket.on('open', () => {
          mockSocket.send(JSON.stringify({
            type: 'SYNC_REQUEST'
          }));
        });

        mockSocket.on('message', (data) => {
          const message = JSON.parse(data.toString());
          if (message.type === 'CHAIN_DATA') {
            expect(Array.isArray(message.chain)).toBe(true);
            expect(message.chain[0].index).toBe(0); // Genesis block
            resolve();
          }
        });
      });
    });

    it('should handle network partitions and reconnections', async () => {
      const mockSocket2 = new WebSocket('ws://localhost:6001');

      await new Promise<void>((resolve) => {
        mockSocket.close(); // Simulate network partition

        setTimeout(() => {
          const newSocket = new WebSocket('ws://localhost:6001');
          newSocket.on('open', () => {
            newSocket.send(JSON.stringify({
              type: 'SYNC_REQUEST'
            }));
          });

          newSocket.on('message', (data) => {
            const message = JSON.parse(data.toString());
            if (message.type === 'CHAIN_DATA') {
              expect(message.chain.length).toBeGreaterThan(0);
              newSocket.close();
              mockSocket2.close();
              resolve();
            }
          });
        }, 1000);
      });
    });
  });
}); 