#!/bin/bash

# Load environment variables
source ../.env.production

# Set colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Function to check configuration files
check_config() {
    local file=$1
    local name=$2
    
    if [ -f "$file" ]; then
        echo -e "${GREEN}✓${NC} $name configuration found"
        return 0
    else
        echo -e "${RED}✗${NC} $name configuration missing"
        return 1
    fi
}

# Function to check nginx configuration
check_nginx_config() {
    echo -e "\n${YELLOW}Checking Nginx configuration...${NC}"
    docker exec loomloot_nginx_1 nginx -t
}

# Function to check SSL certificates
check_ssl() {
    echo -e "\n${YELLOW}Checking SSL certificates...${NC}"
    if [ -f "/etc/nginx/ssl/fullchain.pem" ] && [ -f "/etc/nginx/ssl/privkey.pem" ]; then
        echo -e "${G<PERSON><PERSON>}✓${NC} SSL certificates found"
        # Check expiration
        expiry_date=$(openssl x509 -enddate -noout -in /etc/nginx/ssl/fullchain.pem | cut -d= -f2)
        echo "Certificate expires: $expiry_date"
    else
        echo -e "${RED}✗${NC} SSL certificates missing"
    fi
}

# Function to check environment variables
check_env_vars() {
    echo -e "\n${YELLOW}Checking environment variables...${NC}"
    required_vars=(
        "DATABASE_URL"
        "REDIS_URL"
        "NEXTAUTH_URL"
        "NEXTAUTH_SECRET"
        "BTC_API_KEY"
        "BTC_NETWORK"
        "DB_USER"
        "DB_PASSWORD"
        "DB_NAME"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            echo -e "${RED}✗${NC} $var is not set"
        else
            echo -e "${GREEN}✓${NC} $var is set"
        fi
    done
}

# Main verification process
echo "Starting configuration verification..."

# Check configuration files
echo -e "\n${YELLOW}Checking configuration files...${NC}"
check_config "../config/security.conf" "Security"
check_config "../config/cache.conf" "Cache"
check_config "../public/404.html" "404 error page"
check_config "../public/500.html" "500 error page"
check_config "../nginx.conf" "Nginx"
check_config "../docker-compose.prod.yml" "Docker Compose Production"
check_config "../ecosystem.config.js" "PM2"

# Check Nginx configuration
check_nginx_config

# Check SSL certificates
check_ssl

# Check environment variables
check_env_vars

# Check Docker services
echo -e "\n${YELLOW}Checking Docker services...${NC}"
docker-compose -f ../docker-compose.prod.yml ps

# Check disk space
echo -e "\n${YELLOW}Checking disk space...${NC}"
df -h

# Final summary
echo -e "\n${YELLOW}Verification complete!${NC}"
echo "Please review any errors or warnings above." 