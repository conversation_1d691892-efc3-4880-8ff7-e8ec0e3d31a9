import { BackupService } from '@/services/backup';
import cron from 'node-cron';
import winston from 'winston';

const backupService = BackupService.getInstance();

// Configure logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'backup-script' },
  transports: [
    new winston.transports.File({ filename: 'logs/backup-script-error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/backup-script.log' }),
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// Schedule daily incremental backups at 3 AM
cron.schedule('0 3 * * *', async () => {
  logger.info('Starting daily incremental backup...');
  try {
    await backupService.performBackup('incremental');
    logger.info('Daily incremental backup completed successfully');
  } catch (error) {
    logger.error('Daily incremental backup failed:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
  }
});

// Schedule weekly full backups on Sunday at 2 AM
cron.schedule('0 2 * * 0', async () => {
  logger.info('Starting weekly full backup...');
  try {
    await backupService.performBackup('full');
    logger.info('Weekly full backup completed successfully');
  } catch (error) {
    logger.error('Weekly full backup failed:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
  }
});

// Handle process termination
process.on('SIGTERM', async () => {
  logger.info('Received SIGTERM signal. Cleaning up...');
  try {
    // Perform any necessary cleanup
    await backupService.performBackup('incremental');
    logger.info('Final backup completed successfully');
  } catch (error) {
    logger.error('Final backup failed:', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  } finally {
    process.exit(0);
  }
});

process.on('SIGINT', async () => {
  logger.info('Received SIGINT signal. Cleaning up...');
  try {
    // Perform any necessary cleanup
    await backupService.performBackup('incremental');
    logger.info('Final backup completed successfully');
  } catch (error) {
    logger.error('Final backup failed:', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  } finally {
    process.exit(0);
  }
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception:', {
    error: error.message,
    stack: error.stack
  });
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason) => {
  logger.error('Unhandled promise rejection:', {
    reason: reason instanceof Error ? reason.message : reason
  });
  process.exit(1);
});

logger.info('Backup scheduler started. Waiting for scheduled times...'); 