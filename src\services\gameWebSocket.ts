import { Server as HTTPServer } from 'http';
import { Server as WebSocketServer } from 'ws';
import { verifyToken } from '../utils/auth';
import { redis } from '../utils/redis';

interface GameMessage {
  type: 'JOIN' | 'LEAVE' | 'READY' | 'GAME_ACTION' | 'CHAT';
  roomId: string;
  userId: string;
  payload?: any;
}

export class GameWebSocketService {
  private wss: WebSocketServer;
  private rooms: Map<string, Set<WebSocket>>;

  constructor(server: HTTPServer) {
    this.wss = new WebSocketServer({ server });
    this.rooms = new Map();

    this.wss.on('connection', async (ws: WebSocket, req) => {
      try {
        // Verify authentication
        const token = new URL(req.url!, 'http://localhost').searchParams.get('token');
        if (!token) {
          ws.close(1008, 'Unauthorized');
          return;
        }

        const auth = await verifyToken(token);
        if (!auth.success) {
          ws.close(1008, 'Unauthorized');
          return;
        }

        // Handle messages
        ws.on('message', async (data: string) => {
          try {
            const message: GameMessage = JSON.parse(data);
            await this.handleMessage(ws, message, auth.userId);
          } catch (error) {
            console.error('WebSocket Message Error:', error);
            ws.send(JSON.stringify({
              type: 'ERROR',
              message: 'Invalid message format'
            }));
          }
        });

        // Handle disconnection
        ws.on('close', () => {
          this.handleDisconnection(ws, auth.userId);
        });
      } catch (error) {
        console.error('WebSocket Connection Error:', error);
        ws.close(1011, 'Internal server error');
      }
    });
  }

  private async handleMessage(ws: WebSocket, message: GameMessage, userId: string) {
    const { type, roomId, payload } = message;

    switch (type) {
      case 'JOIN':
        await this.joinRoom(ws, roomId);
        break;

      case 'LEAVE':
        await this.leaveRoom(ws, roomId, userId);
        break;

      case 'READY':
        await this.handlePlayerReady(roomId, userId);
        break;

      case 'GAME_ACTION':
        await this.handleGameAction(roomId, userId, payload);
        break;

      case 'CHAT':
        await this.broadcastToRoom(roomId, {
          type: 'CHAT',
          userId,
          message: payload.message
        });
        break;
    }
  }

  private async joinRoom(ws: WebSocket, roomId: string) {
    if (!this.rooms.has(roomId)) {
      this.rooms.set(roomId, new Set());
    }
    this.rooms.get(roomId)!.add(ws);
  }

  private async leaveRoom(ws: WebSocket, roomId: string, userId: string) {
    const room = this.rooms.get(roomId);
    if (room) {
      room.delete(ws);
      if (room.size === 0) {
        this.rooms.delete(roomId);
      }
      await GameRoomService.leaveRoom(roomId, userId);
    }
  }

  private async handlePlayerReady(roomId: string, userId: string) {
    const room = await GameRoomService.getRoom(roomId);
    if (!room) return;

    const playerIndex = room.players.findIndex(p => p.id === userId);
    if (playerIndex === -1) return;

    room.players[playerIndex].ready = true;
    await redis.set(`gameroom:${roomId}`, JSON.stringify(room));

    // Check if all players are ready
    if (room.players.every(p => p.ready)) {
      await this.startGame(roomId);
    }

    await this.broadcastToRoom(roomId, {
      type: 'PLAYER_READY',
      userId
    });
  }

  private async handleGameAction(roomId: string, userId: string, action: any) {
    // Handle game-specific actions
    const room = await GameRoomService.getRoom(roomId);
    if (!room) return;

    // Broadcast action to all players
    await this.broadcastToRoom(roomId, {
      type: 'GAME_ACTION',
      userId,
      action
    });
  }

  private async broadcastToRoom(roomId: string, message: any) {
    const room = this.rooms.get(roomId);
    if (room) {
      const messageStr = JSON.stringify(message);
      room.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
          client.send(messageStr);
        }
      });
    }
  }

  private async startGame(roomId: string) {
    const room = await GameRoomService.getRoom(roomId);
    if (!room) return;

    room.status = 'IN_PROGRESS';
    await redis.set(`gameroom:${roomId}`, JSON.stringify(room));

    await this.broadcastToRoom(roomId, {
      type: 'GAME_START',
      gameType: room.gameType,
      players: room.players
    });
  }

  private handleDisconnection(ws: WebSocket, userId: string) {
    this.rooms.forEach((clients, roomId) => {
      if (clients.has(ws)) {
        this.leaveRoom(ws, roomId, userId);
      }
    });
  }
} 