# Dependencies
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Next.js build output
.next
out

# Environment files
.env
.env.*
!.env.example

# Version control
.git
.gitignore

# IDE files
.idea
.vscode
*.swp
*.swo

# Testing
coverage
.nyc_output
__tests__
*.test.js
*.spec.js

# Temporary files
*.log
.DS_Store
Thumbs.db

# Documentation
README.md
CHANGELOG.md
docs

# Development configs
.eslintrc
.prettierrc
.editorconfig
jest.config.js
tsconfig.tsbuildinfo

# Backup files
*.bak
*.backup

# Docker files
Dockerfile*
docker-compose*
.docker

# Build files
dist
build
*.tsbuildinfo

# Debug files
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# System Files
.DS_Store
Thumbs.db 