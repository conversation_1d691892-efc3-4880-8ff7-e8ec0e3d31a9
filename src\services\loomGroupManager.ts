import { Redis } from '@upstash/redis';
import { 
  GroupType, 
  GroupTier, 
  SpotPosition, 
  LoomGroup, 
  LoomSpots,
  GroupStatus,
  PlayerMove,
  GroupReward,
  GroupStats 
} from '../types/loom';
import { getRedisClient } from '../utils/redis';
import { LoomWalletManager } from './loomWalletManager';

interface GroupParameters {
  moveInterval: number;
  maxPlayersPerGroup: number;
  splitThreshold: number;
  mergeThreshold: number;
}

interface CreateGroupOptions {
  entryFee: GroupTier;
  maxPlayers: number;
  maxWaitingPlayers: number;
  type: GroupType;
  minPlayersToStart: number;
  autoMergeThreshold: number;
  splitThreshold: number;
}

export class LoomGroupManager {
  private static MOVE_INTERVAL = 30000; // 30 seconds
  private static readonly TRAFFIC_THRESHOLD_HIGH = 20;  // players per minute
  private static readonly TRAFFIC_THRESHOLD_MEDIUM = 10;
  private static readonly TIMER_DURATION_LOW = 60000;   // 60 seconds
  private static readonly TIMER_DURATION_MEDIUM = 30000; // 30 seconds
  private static readonly TIMER_DURATION_HIGH = 15000;  // 15 seconds
  private static readonly TRAFFIC_WINDOW = 60000;       // 1 minute window
  private static readonly MAX_GENERATION = 3;           // Limits split depth
  private static readonly MIN_PLAYERS_FOR_SPLIT = 15;    // Min players per new group
  private static readonly HOUSE_FEE_PERCENTAGE = 12.5;  // 1/8th of winnings
  private static readonly OUTER_RING_SIZE = 8;
  private static readonly WAITING_SPOTS_SIZE = 8;
  private static readonly MAX_WAITING_PLAYERS = 8;    // Maximum waiting players

  private static readonly GAME_TYPE_CONFIG = {
    [GroupType.FOUR_JUMPS]: {
      allowedTiers: [
        GroupTier.STARTER,
        GroupTier.BASIC,
        GroupTier.BRONZE,
        GroupTier.SILVER,
        GroupTier.GOLD,
        GroupTier.PLATINUM
      ],
      houseFeePercentage: 12.5,
      minPlayersToStart: 4,
      autoMergeThreshold: 3,
      splitThreshold: 15,
      tutorialRequired: true
    },
    [GroupType.FULL_RIDE]: {
      allowedTiers: [
        GroupTier.STARTER,
        GroupTier.BASIC,
        GroupTier.BRONZE,
        GroupTier.SILVER,
        GroupTier.GOLD,
        GroupTier.PLATINUM
      ],
      houseFeePercentage: 12.5,
      minPlayersToStart: 8,
      autoMergeThreshold: 6,
      splitThreshold: 12,
      tutorialRequired: true
    }
  };

  private static redis = new Redis({
    url: process.env.UPSTASH_REDIS_REST_URL!,
    token: process.env.UPSTASH_REDIS_REST_TOKEN!,
  });

  private static groupParameters: GroupParameters = {
    moveInterval: 5000, // 5 seconds
    maxPlayersPerGroup: 100,
    splitThreshold: 90, // 90% capacity
    mergeThreshold: 30  // 30% capacity
  };

  static async createGroup(tier: GroupTier, type: GroupType): Promise<LoomGroup>;
  static async createGroup(options: CreateGroupOptions): Promise<LoomGroup>;
  static async createGroup(tierOrOptions: GroupTier | CreateGroupOptions, type?: GroupType): Promise<LoomGroup> {
    let options: CreateGroupOptions;
    
    if (typeof tierOrOptions === 'number') {
      if (!type) throw new Error('Type is required when creating group with tier');
      options = {
        entryFee: tierOrOptions,
        maxPlayers: this.groupParameters.maxPlayersPerGroup,
        type,
        minPlayersToStart: Math.ceil(this.groupParameters.maxPlayersPerGroup * 0.1),
        autoMergeThreshold: this.groupParameters.mergeThreshold,
        splitThreshold: this.groupParameters.splitThreshold
      };
    } else {
      options = tierOrOptions;
    }

    if (!this.GAME_TYPE_CONFIG[options.type].allowedTiers.includes(options.entryFee)) {
      throw new Error(`${GroupTier[options.entryFee]} tier is not allowed for ${options.type} games`);
    }

    const config = this.GAME_TYPE_CONFIG[options.type];
    const now = Date.now();

    const group: LoomGroup = {
      id: `loom_${options.entryFee}_${options.type}_${now}`,
      entryFee: options.entryFee,
      spots: {
        CENTER: null,
        INNER: Array(2).fill(null),
        MIDDLE: Array(4).fill(null),
        OUTER: Array(8).fill(null),
        WAITING: Array(8).fill({ userId: null, joinedAt: null })
      },
      currentPlayers: 0,
      maxPlayers: options.maxPlayers,
      isActive: false,
      lastMoveTimestamp: now,
      timerDuration: this.TIMER_DURATION_LOW,
      playerTraffic: 0,
      parentId: null,
      childIds: [],
      generation: 0,
      maxGeneration: this.MAX_GENERATION,
      houseFeePercentage: config.houseFeePercentage,
      currentPrizePool: 0,
      type: options.type,
      createdAt: now,
      lastUpdatedAt: now,
      status: GroupStatus.WAITING,
      minPlayersToStart: options.minPlayersToStart,
      autoMergeThreshold: options.autoMergeThreshold,
      splitThreshold: options.splitThreshold,
      waitingQueue: []
    };

    const redis = await getRedisClient();
    await redis.set(`group:${group.id}`, JSON.stringify(group));
    await this.updateGroupStats(group);
    return group;
  }

  static async getOrCreateAvailableGroup(tier: GroupTier): Promise<LoomGroup> {
    const redis = await getRedisClient();
    const groups = await redis.hgetall('loom_groups');
    
    // Find an available group in the specified tier
    const availableGroup = Object.values(groups || {})
      .map(g => JSON.parse(g as string))
      .find((group: LoomGroup) => 
        group.entryFee === tier &&
        group.currentPlayers < LoomGroupManager.groupParameters.maxPlayersPerGroup &&
        group.status === GroupStatus.ACTIVE
      );

    if (availableGroup) {
      return availableGroup;
    }

    // Create a new group if none available
    return await this.createGroup({
      entryFee: tier,
      maxPlayers: this.groupParameters.maxPlayersPerGroup,
      type: GroupType.FOUR_JUMPS,
      minPlayersToStart: Math.ceil(this.groupParameters.maxPlayersPerGroup * 0.1),
      autoMergeThreshold: this.groupParameters.mergeThreshold,
      splitThreshold: this.groupParameters.splitThreshold
    });
  }

  static async joinGroup(userId: string, groupId: string): Promise<LoomGroup> {
    const redis = await getRedisClient();
    const group = await this.getGroup(groupId);

    if (!group) {
      throw new Error('Group not found');
    }

    if (group.currentPlayers >= group.maxPlayers) {
      throw new Error('Group is full');
    }

    if (group.status === GroupStatus.COMPLETED || group.status === GroupStatus.PAUSED) {
      throw new Error('Group is not accepting new players');
    }

    // Check if user has enough tokens/balance
    await this.validateUserBalance(userId, group.entryFee);

    // Find first available spot in OUTER ring
    const outerIndex = group.spots.OUTER.indexOf(null);
    if (outerIndex === -1) {
      throw new Error('No available spots');
    }

    // Deduct entry fee and add to prize pool
    await this.processEntryFee(userId, group);

    // Add player to group
    group.spots.OUTER[outerIndex] = userId;
    group.currentPlayers++;
    group.lastUpdatedAt = Date.now();

    // Update group status
    if (group.currentPlayers >= group.minPlayersToStart) {
      group.status = GroupStatus.ACTIVE;
      group.isActive = true;
    }

    // Track activity and update timer
    await this.trackPlayerActivity(groupId, userId);
    await this.updateTimerBasedOnTraffic(groupId);

    // Record the move
    await this.recordPlayerMove({
      userId,
      groupId: group.id,
      fromPosition: SpotPosition.OUTER,
      toPosition: SpotPosition.OUTER,
      timestamp: Date.now()
    });

    await redis.set(`group:${group.id}`, JSON.stringify(group));
    await this.updateGroupStats(group);

    return group;
  }

  static async movePlayersUp(groupId: string): Promise<void> {
    const redis = await getRedisClient();
    const group = await this.getGroup(groupId);
    
    if (!group || group.status !== GroupStatus.ACTIVE) return;

    const now = Date.now();
    if (now - group.lastMoveTimestamp < group.timerDuration) return;

    // Handle center player rewards
    if (group.spots.CENTER) {
      await this.handleCenterReward(group);
      group.spots.CENTER = null;
    }

    // Record moves and update positions
    const moves: PlayerMove[] = [];

    // Move INNER to CENTER
    if (group.spots.INNER.some(spot => spot !== null)) {
      const player = group.spots.INNER[0];
      if (player) {
        moves.push({
          userId: player,
          groupId,
          fromPosition: SpotPosition.INNER,
          toPosition: SpotPosition.CENTER,
          timestamp: now
        });
      }
      group.spots.CENTER = player;
      group.spots.INNER = group.spots.INNER.map(() => null);
    }

    // Move MIDDLE to INNER
    const middlePlayers = group.spots.MIDDLE.filter(Boolean);
    middlePlayers.slice(0, 2).forEach((player, index) => {
      if (player) {
        moves.push({
          userId: player,
          groupId,
          fromPosition: SpotPosition.MIDDLE,
          toPosition: SpotPosition.INNER,
          timestamp: now
        });
      }
    });
    group.spots.INNER = middlePlayers.slice(0, 2);
    group.spots.MIDDLE = group.spots.MIDDLE.map(() => null);

    // Move OUTER to MIDDLE
    const outerPlayers = group.spots.OUTER.filter(Boolean);
    outerPlayers.slice(0, 4).forEach((player, index) => {
      if (player) {
        moves.push({
          userId: player,
          groupId,
          fromPosition: SpotPosition.OUTER,
          toPosition: SpotPosition.MIDDLE,
          timestamp: now
        });
      }
    });
    group.spots.MIDDLE = outerPlayers.slice(0, 4);
    group.spots.OUTER = group.spots.OUTER.map(() => null);

    // Record all moves
    await Promise.all(moves.map(move => this.recordPlayerMove(move)));

    // Update group state
    group.lastMoveTimestamp = now;
    group.lastUpdatedAt = now;
    group.currentPlayers = this.countPlayers(group.spots);

    if (group.currentPlayers === 0) {
      group.status = GroupStatus.COMPLETED;
      group.isActive = false;
    }

    await redis.set(`group:${group.id}`, JSON.stringify(group));
    await this.updateGroupStats(group);
  }

  private static async handleCenterReward(group: LoomGroup): Promise<void> {
    const redis = await getRedisClient();
    const centerPlayer = group.spots.CENTER;
    
    if (!centerPlayer) return;

    const reward: GroupReward = {
      userId: centerPlayer,
      groupId: group.id,
      amount: this.calculateReward(group),
      timestamp: Date.now(),
      type: group.type,
      position: SpotPosition.CENTER
    };

    await redis.rpush('rewards', JSON.stringify(reward));
    await this.processReward(reward);
  }

  private static calculateReward(group: LoomGroup): number {
    const totalPrize = group.currentPrizePool;
    const houseFee = (totalPrize * group.houseFeePercentage) / 100;
    return totalPrize - houseFee;
  }

  private static async processReward(reward: GroupReward): Promise<void> {
    // Implement reward distribution logic
    // This could involve updating user balances, creating transactions, etc.
  }

  private static async processEntryFee(userId: string, group: LoomGroup): Promise<void> {
    // Implement entry fee processing logic
    // This could involve deducting from user balance, creating transactions, etc.
  }

  private static async validateUserBalance(userId: string, amount: number): Promise<void> {
    // Implement balance validation logic
  }

  private static async recordPlayerMove(move: PlayerMove): Promise<void> {
    const redis = await getRedisClient();
    await redis.rpush('player_moves', JSON.stringify(move));
  }

  private static async updateGroupStats(group: LoomGroup): Promise<void> {
    const redis = await getRedisClient();
    const statsKey = `stats:${group.type}:${group.entryFee}`;
    
    // Implement stats update logic
    // This could involve updating various metrics like total players, prize pools, etc.
  }

  static async getGroup(groupId: string): Promise<LoomGroup | null> {
    const redis = await getRedisClient();
    const data = await redis.get(`group:${groupId}`);
    return data ? JSON.parse(data) : null;
  }

  private static async getGroupsByEntryFee(entryFee: number): Promise<LoomGroup[]> {
    const redis = await getRedisClient();
    const keys = await redis.keys(`group:loom_${entryFee}_*`);
    const groups: LoomGroup[] = [];
    
    for (const key of keys) {
      const data = await redis.get(key);
      if (data) groups.push(JSON.parse(data));
    }

    return groups;
  }

  private static countPlayers(spots: LoomSpots): number {
    return (spots.CENTER ? 1 : 0) +
           spots.INNER.filter(Boolean).length +
           spots.MIDDLE.filter(Boolean).length +
           spots.OUTER.filter(Boolean).length;
  }

  private static getAllPlayers(spots: LoomSpots): string[] {
    return [
      spots.CENTER,
      ...spots.INNER,
      ...spots.MIDDLE,
      ...spots.OUTER
    ].filter((player): player is string => player !== null);
  }

  static async updateTimerBasedOnTraffic(groupId: string): Promise<void> {
    const redis = await getRedisClient();
    const group = await this.getGroup(groupId);
    if (!group) return;

    // Get recent traffic data from Redis
    const recentJoins = await this.getRecentTraffic(groupId);
    const trafficPerMinute = recentJoins.length;

    // Determine new timer duration based on traffic thresholds
    let newDuration: number;
    if (trafficPerMinute >= this.TRAFFIC_THRESHOLD_HIGH) {
      newDuration = this.TIMER_DURATION_HIGH; // 15 seconds for high traffic
    } else if (trafficPerMinute >= this.TRAFFIC_THRESHOLD_MEDIUM) {
      newDuration = this.TIMER_DURATION_MEDIUM; // 30 seconds for medium traffic
    } else {
      newDuration = this.TIMER_DURATION_LOW; // 60 seconds for low traffic
    }

    // Update group timer if changed
    if (newDuration !== group.timerDuration) {
      group.timerDuration = newDuration;
      group.lastUpdatedAt = Date.now();
      await redis.set(`group:${group.id}`, JSON.stringify(group));
      
      // Broadcast timer update to all players in group
      await this.broadcastTimerUpdate(group);
    }
  }

  private static async broadcastTimerUpdate(group: LoomGroup): Promise<void> {
    const redis = await getRedisClient();
    const players = this.getAllPlayers(group.spots);
    
    // Publish timer update event
    const updateEvent = {
      type: 'TIMER_UPDATE',
      groupId: group.id,
      newDuration: group.timerDuration,
      timestamp: Date.now()
    };
    
    await redis.publish('group_events', JSON.stringify(updateEvent));
  }

  static async trackPlayerActivity(groupId: string, userId: string): Promise<void> {
    const redis = await getRedisClient();
    const timestamp = Date.now();
    await redis.zadd(
      `traffic:${groupId}`,
      timestamp,
      `${userId}:${timestamp}`
    );
    // Clean up old traffic data
    await redis.zremrangebyscore(
      `traffic:${groupId}`,
      0,
      timestamp - this.TRAFFIC_WINDOW
    );
  }

  private static async getRecentTraffic(groupId: string): Promise<string[]> {
    const redis = await getRedisClient();
    const now = Date.now();
    return await redis.zrangebyscore(
      `traffic:${groupId}`,
      now - this.TRAFFIC_WINDOW,
      now
    );
  }

  static async progressGroup(groupId: string): Promise<void> {
    const redis = await getRedisClient();
    const group = await this.getGroup(groupId);
    
    if (!group || group.status !== GroupStatus.ACTIVE) return;

    const now = Date.now();
    if (now - group.lastMoveTimestamp < group.timerDuration) return;

    // Handle center player rewards and auto-rejoin
    if (group.spots.CENTER) {
      await this.handleCenterReward(group);
      await this.handleAutoRejoin(group.spots.CENTER);
      group.spots.CENTER = null;
    }

    // Record moves and update positions
    const moves: PlayerMove[] = [];

    // Move INNER to CENTER (take first non-null player)
    const innerPlayer = group.spots.INNER.find(spot => spot !== null);
    if (innerPlayer) {
      moves.push({
        userId: innerPlayer,
        groupId,
        fromPosition: SpotPosition.INNER,
        toPosition: SpotPosition.CENTER,
        timestamp: now
      });
      group.spots.CENTER = innerPlayer;
      group.spots.INNER = group.spots.INNER.map(() => null);
    }

    // Move MIDDLE to INNER
    const middlePlayers = group.spots.MIDDLE.filter(Boolean);
    middlePlayers.slice(0, 2).forEach((player, index) => {
      if (player) {
        moves.push({
          userId: player,
          groupId,
          fromPosition: SpotPosition.MIDDLE,
          toPosition: SpotPosition.INNER,
          timestamp: now
        });
        group.spots.INNER[index] = player;
      }
    });
    group.spots.MIDDLE = group.spots.MIDDLE.map(() => null);

    // Move OUTER to MIDDLE
    const outerPlayers = group.spots.OUTER.filter(Boolean);
    outerPlayers.slice(0, 4).forEach((player, index) => {
      if (player) {
        moves.push({
          userId: player,
          groupId,
          fromPosition: SpotPosition.OUTER,
          toPosition: SpotPosition.MIDDLE,
          timestamp: now
        });
        group.spots.MIDDLE[index] = player;
      }
    });
    group.spots.OUTER = group.spots.OUTER.map(() => null);

    // Record all moves
    await Promise.all(moves.map(move => this.recordPlayerMove(move)));

    // Update group state
    group.lastMoveTimestamp = now;
    group.lastUpdatedAt = now;
    group.currentPlayers = this.countPlayers(group.spots);

    // Check if group needs to be split or merged
    if (group.currentPlayers >= group.splitThreshold) {
      await this.splitGroup(group.id);
    } else if (group.currentPlayers <= group.autoMergeThreshold) {
      await this.mergeGroupsIfNeeded(group.id);
    }

    // Update group status
    if (group.currentPlayers === 0) {
      group.status = GroupStatus.COMPLETED;
      group.isActive = false;
    }

    await redis.set(`group:${group.id}`, JSON.stringify(group));
    await this.updateGroupStats(group);
    await this.broadcastGroupUpdate(group);
  }

  private static async handleAutoRejoin(userId: string): Promise<void> {
    const redis = await getRedisClient();
    const userKey = `user:${userId}`;
    const userData = await redis.get(userKey);
    
    if (!userData) return;
    
    const user = JSON.parse(userData);
    if (!user.isPremium) return;

    // Find suitable group for auto-rejoin
    const groups = await this.getAvailableGroups(user.lastGroupTier);
    const targetGroup = groups.find(g => g.currentPlayers < g.maxPlayers);
    
    if (targetGroup) {
      await this.joinGroup(userId, targetGroup.id);
    }
  }

  private static async broadcastGroupUpdate(group: LoomGroup): Promise<void> {
    const redis = await getRedisClient();
    const updateEvent = {
      type: 'GROUP_UPDATE',
      group,
      timestamp: Date.now()
    };
    
    await redis.publish('group_events', JSON.stringify(updateEvent));
  }

  private static async getAvailableGroups(tier: GroupTier): Promise<LoomGroup[]> {
    const redis = await getRedisClient();
    const keys = await redis.keys(`group:*`);
    const groups: LoomGroup[] = [];
    
    for (const key of keys) {
      const data = await redis.get(key);
      if (data) {
        const group = JSON.parse(data);
        if (group.entryFee === tier && group.status === GroupStatus.ACTIVE) {
          groups.push(group);
        }
      }
    }
    
    return groups;
  }

  static async getGroupsByTier(tier: GroupTier): Promise<LoomGroup[]> {
    const groups = await this.redis.hgetall('loom:groups');
    return Object.values(groups)
      .map(group => JSON.parse(group as string))
      .filter(group => group.entryFee === tier && group.isActive);
  }

  static async updateGroupParameters(params: Partial<GroupParameters>): Promise<void> {
    LoomGroupManager.groupParameters = {
      ...LoomGroupManager.groupParameters,
      ...params
    };

    const redis = await getRedisClient();
    await redis.set('group_parameters', JSON.stringify(LoomGroupManager.groupParameters));
  }

  static async optimizeGroupDistribution(): Promise<void> {
    const redis = await getRedisClient();
    const groups = Object.values(await redis.hgetall('loom_groups'))
      .map(g => JSON.parse(g));

    for (const group of groups) {
      const capacity = group.currentPlayers / group.maxPlayers * 100;

      // Split overloaded groups
      if (capacity >= LoomGroupManager.groupParameters.splitThreshold) {
        await LoomGroupManager.splitGroup(group.id);
      }
      // Merge underutilized groups
      else if (capacity <= LoomGroupManager.groupParameters.mergeThreshold) {
        const similarGroups = groups.filter(g => 
          g.id !== group.id &&
          g.entryFee === group.entryFee &&
          g.type === group.type &&
          (g.currentPlayers / g.maxPlayers * 100) <= LoomGroupManager.groupParameters.mergeThreshold
        );

        if (similarGroups.length > 0) {
          await LoomGroupManager.mergeGroups(group.id, similarGroups[0].id);
        }
      }
    }
  }

  private static async splitGroup(groupId: string): Promise<void> {
    const group = await this.getGroup(groupId);
    if (!group) return;

    const newGroup1 = await this.createGroup({
      entryFee: group.entryFee,
      maxPlayers: group.maxPlayers,
      type: group.type,
      minPlayersToStart: group.minPlayersToStart,
      autoMergeThreshold: group.autoMergeThreshold,
      splitThreshold: group.splitThreshold
    });

    const newGroup2 = await this.createGroup({
      entryFee: group.entryFee,
      maxPlayers: group.maxPlayers,
      type: group.type,
      minPlayersToStart: group.minPlayersToStart,
      autoMergeThreshold: group.autoMergeThreshold,
      splitThreshold: group.splitThreshold
    });

    // Distribute players between new groups
    const players = await this.getAllPlayers(group.spots);
    const midPoint = Math.floor(players.length / 2);

    await Promise.all([
      ...players.slice(0, midPoint).map(playerId => 
        LoomGroupManager.joinGroup(playerId, newGroup1.id)
      ),
      ...players.slice(midPoint).map(playerId => 
        LoomGroupManager.joinGroup(playerId, newGroup2.id)
      )
    ]);

    // Mark original group as completed
    await this.updateGroupStatus(groupId, GroupStatus.COMPLETED);
  }

  private static async mergeGroupsIfNeeded(groupId: string): Promise<void> {
    const redis = await getRedisClient();
    const group = await this.getGroup(groupId);
    if (!group || !group.parentId) return;

    // Get sibling group
    const parent = await this.getGroup(group.parentId);
    if (!parent || parent.childIds.length !== 2) return;

    const siblingId = parent.childIds.find(id => id !== group.id);
    if (!siblingId) return;
    const sibling = await this.getGroup(siblingId);
    if (!sibling) return;

    // Check if groups should be merged (low player count)
    const totalPlayers = group.currentPlayers + sibling.currentPlayers;
    if (totalPlayers <= this.MIN_PLAYERS_FOR_SPLIT) {
      // Merge players back to parent
      const allPlayers = [
        ...this.getAllPlayers(group.spots),
        ...this.getAllPlayers(sibling.spots)
      ];

      await this.addPlayersToGroup(parent.id, allPlayers);

      // Deactivate child groups
      group.isActive = false;
      sibling.isActive = false;
      parent.childIds = [];
      
      await Promise.all([
        redis.set(`group:${group.id}`, JSON.stringify(group)),
        redis.set(`group:${sibling.id}`, JSON.stringify(sibling)),
        redis.set(`group:${parent.id}`, JSON.stringify(parent))
      ]);
    }
  }

  private static async addPlayersToGroup(groupId: string, players: string[]): Promise<void> {
    const redis = await getRedisClient();
    const group = await this.getGroup(groupId);
    if (!group) return;

    // Reset spots
    group.spots = {
      CENTER: null,
      INNER: Array(2).fill(null),
      MIDDLE: Array(4).fill(null),
      OUTER: Array(8).fill(null),
      WAITING: Array(8).fill({ userId: null, joinedAt: null })
    };

    // Add players to outer ring first
    let remainingPlayers = [...players];
    for (let i = 0; i < group.spots.OUTER.length && remainingPlayers.length > 0; i++) {
      group.spots.OUTER[i] = remainingPlayers.shift() || null;
    }

    group.currentPlayers = players.length;
    group.isActive = true;
    await redis.set(`group:${group.id}`, JSON.stringify(group));
  }

  private static async updateGroupStatus(groupId: string, status: GroupStatus): Promise<void> {
    const redis = await getRedisClient();
    const group = await this.getGroup(groupId);
    if (!group) return;

    group.status = status;
    group.isActive = status === GroupStatus.ACTIVE;
    group.lastUpdatedAt = Date.now();

    await redis.set(`group:${groupId}`, JSON.stringify(group));
    await this.updateGroupStats(group);
  }

  static async joinWaitingList(userId: string, groupId: string): Promise<void> {
    const redis = await getRedisClient();
    const group = await this.getGroup(groupId);

    if (!group) {
      throw new Error('Group not found');
    }

    // Initialize waiting spots if not exists
    if (!group.spots.WAITING) {
      group.spots.WAITING = Array(this.WAITING_SPOTS_SIZE).fill({ userId: null, joinedAt: null });
    }

    // Check if user is already in any position
    if (this.isUserInGroup(userId, group)) {
      throw new Error('User is already in this group');
    }

    // Find first available waiting spot
    const availableSpotIndex = group.spots.WAITING.findIndex(spot => !spot.userId);
    if (availableSpotIndex === -1) {
      throw new Error('No waiting spots available');
    }

    // Add user to waiting spot with timestamp
    const now = Date.now();
    group.spots.WAITING[availableSpotIndex] = {
      userId,
      joinedAt: now
    };
    
    // Add to waiting queue for FIFO tracking
    group.waitingQueue.push(userId);

    await redis.set(`group:${group.id}`, JSON.stringify(group));
    await this.updateGroupStats(group);
  }

  private static isUserInGroup(userId: string, group: LoomGroup): boolean {
    return Boolean(
      group.spots.CENTER === userId ||
      group.spots.INNER.includes(userId) ||
      group.spots.MIDDLE.includes(userId) ||
      group.spots.OUTER.includes(userId) ||
      group.spots.WAITING.some(spot => spot.userId === userId)
    );
  }

  static async promoteWaitingPlayers(groupId: string): Promise<void> {
    const redis = await getRedisClient();
    const group = await this.getGroup(groupId);
    
    if (!group || group.status !== GroupStatus.ACTIVE) return;

    // Sort waiting players by join time (FIFO)
    const waitingPlayers = group.spots.WAITING
      .filter(spot => spot.userId)
      .sort((a, b) => (a.joinedAt || 0) - (b.joinedAt || 0));

    // Find empty spots in outer ring
    const emptyOuterSpots = group.spots.OUTER
      .map((spot, index) => ({ spot, index }))
      .filter(({ spot }) => spot === null);

    // Promote waiting players to outer ring
    for (let i = 0; i < Math.min(emptyOuterSpots.length, waitingPlayers.length); i++) {
      const player = waitingPlayers[i];
      const emptySpot = emptyOuterSpots[i];
      
      if (player.userId) {
        // Move player to outer ring
        group.spots.OUTER[emptySpot.index] = player.userId;
        
        // Remove from waiting spot
        const waitingIndex = group.spots.WAITING.findIndex(spot => spot.userId === player.userId);
        if (waitingIndex !== -1) {
          group.spots.WAITING[waitingIndex] = { userId: null, joinedAt: null };
        }
        
        // Remove from waiting queue
        group.waitingQueue = group.waitingQueue.filter(id => id !== player.userId);
      }
    }

    group.currentPlayers = this.countActivePlayers(group.spots);
    await redis.set(`group:${group.id}`, JSON.stringify(group));
    await this.updateGroupStats(group);
  }

  private static countActivePlayers(spots: LoomSpots): number {
    return (spots.CENTER ? 1 : 0) +
           spots.INNER.filter(Boolean).length +
           spots.MIDDLE.filter(Boolean).length +
           spots.OUTER.filter(Boolean).length;
  }

  private static countWaitingPlayers(spots: LoomSpots): number {
    return spots.WAITING.filter(spot => spot.userId !== null).length;
  }

  static async findOrCreateAvailableGroup(tier: GroupTier): Promise<LoomGroup> {
    const redis = await getRedisClient();
    const groups = await this.getGroupsByTier(tier);

    // First, try to find a group with waiting spots available
    const groupWithWaitingSpots = groups.find(group => 
      this.countWaitingPlayers(group.spots) < this.WAITING_SPOTS_SIZE
    );

    if (groupWithWaitingSpots) {
      return groupWithWaitingSpots;
    }

    // If no waiting spots available, create a new group
    return await this.createGroup({
      entryFee: tier,
      maxPlayers: 15,
      maxWaitingPlayers: 8,
      type: GroupType.FOUR_JUMPS,
      minPlayersToStart: 7,
      autoMergeThreshold: 7, // Merge when less than half full
      splitThreshold: 15     // Split when full (15 players)
    });
  }
} 