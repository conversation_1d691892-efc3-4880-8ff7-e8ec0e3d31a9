# Redis configuration file

# Basic configuration
port 6379
bind 0.0.0.0
protected-mode yes

# Memory management
maxmemory 256mb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000

# Security
requirepass ""  # No password in development

# Logging
loglevel notice
logfile ""  # Log to stdout

# General
daemonize no
pidfile /var/run/redis_6379.pid

# Client Connection
timeout 0
tcp-keepalive 300
maxclients 10000

# Performance Tuning
databases 16
rdbcompression yes
rdbchecksum yes

# Persistence
dir /data
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# Memory Management
maxmemory-samples 5

# Performance Tuning
save 300 10
save 60 10000
rdbcompression yes
rdbchecksum yes

# Security
requirepass ""  # No password in development 