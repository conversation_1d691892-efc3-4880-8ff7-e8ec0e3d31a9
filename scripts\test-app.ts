import { PrismaClient } from '@prisma/client';
import { redis } from '@/utils/redis';
import { BackupService } from '@/services/backup';
import { PlatformAI } from '@/services/ai/platformAI';

const prisma = new PrismaClient();
const backupService = BackupService.getInstance();
const platformAI = PlatformAI.getInstance();

async function testDatabaseConnection() {
  try {
    await prisma.$connect();
    console.log('✅ Database connection successful');
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    throw error;
  }
}

async function testRedisConnection() {
  try {
    await redis.ping();
    console.log('✅ Redis connection successful');
  } catch (error) {
    console.error('❌ Redis connection failed:', error);
    throw error;
  }
}

async function testBackupSystem() {
  try {
    await backupService.performBackup('incremental');
    console.log('✅ Backup system test successful');
  } catch (error) {
    console.error('❌ Backup system test failed:', error);
    throw error;
  }
}

async function testPlatformAI() {
  try {
    const metrics = await platformAI.getPlatformMetrics();
    console.log('✅ Platform AI test successful');
    console.log('Current metrics:', metrics);
  } catch (error) {
    console.error('❌ Platform AI test failed:', error);
    throw error;
  }
}

async function testUserAuthentication() {
  try {
    // Test user creation
    const user = await prisma.user.create({
      data: {
        phoneNumber: '+1234567890',
        isVerified: true
      }
    });
    console.log('✅ User creation successful');

    // Test session creation
    await redis.set(`session:test_${user.id}`, JSON.stringify({
      userId: user.id,
      createdAt: new Date()
    }));
    console.log('✅ Session creation successful');

    // Cleanup
    await prisma.user.delete({ where: { id: user.id } });
    await redis.del(`session:test_${user.id}`);
  } catch (error) {
    console.error('❌ User authentication test failed:', error);
    throw error;
  }
}

async function testGameLogic() {
  try {
    // Test group creation
    const group = await prisma.loomGroup.create({
      data: {
        type: 'FOUR_JUMPS',
        tier: 10,
        entryFee: 10,
        maxPlayers: 15,
        currentPlayers: 0,
        prizePool: 0
      }
    });
    console.log('✅ Group creation successful');

    // Test transaction creation
    const transaction = await prisma.transaction.create({
      data: {
        type: 'GAME_WIN',
        amount: 100,
        status: 'COMPLETED',
        userId: 'test',
        groupId: group.id
      }
    });
    console.log('✅ Transaction creation successful');

    // Cleanup
    await prisma.transaction.delete({ where: { id: transaction.id } });
    await prisma.loomGroup.delete({ where: { id: group.id } });
  } catch (error) {
    console.error('❌ Game logic test failed:', error);
    throw error;
  }
}

async function runAllTests() {
  console.log('🚀 Starting application tests...\n');

  try {
    await testDatabaseConnection();
    await testRedisConnection();
    await testBackupSystem();
    await testPlatformAI();
    await testUserAuthentication();
    await testGameLogic();

    console.log('\n✨ All tests completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('\n💥 Tests failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    await redis.quit();
  }
}

// Run tests
runAllTests(); 