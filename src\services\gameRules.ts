import { redis } from '../utils/redis';
import { UserService } from './user';

export interface GameResult {
  winners: string[];
  losers: string[];
  payouts: Record<string, number>;
}

export class GameRulesService {
  static async validateBet(userId: string, amount: number): Promise<boolean> {
    const user = await UserService.getUser(userId);
    return user.balance >= amount;
  }

  static async processBets(roomId: string, results: GameResult): Promise<void> {
    const room = await redis.get(`gameroom:${roomId}`);
    if (!room) throw new Error('Room not found');
    
    const gameRoom = JSON.parse(room);
    
    // Process winners
    for (const winnerId of results.winners) {
      const payout = results.payouts[winnerId];
      await UserService.updateBalance(winnerId, payout);
      await this.recordGameHistory(roomId, winnerId, 'WIN', payout);
    }

    // Process losers
    for (const loserId of results.losers) {
      await UserService.updateBalance(loserId, -gameRoom.betAmount);
      await this.recordGameHistory(roomId, loserId, 'LOSS', -gameRoom.betAmount);
    }
  }

  static async recordGameHistory(
    roomId: string,
    userId: string,
    result: 'WIN' | 'LOSS',
    amount: number
  ): Promise<void> {
    const history = {
      roomId,
      userId,
      result,
      amount,
      timestamp: Date.now()
    };

    await redis.lpush(`user:${userId}:game_history`, JSON.stringify(history));
    await redis.lpush('games:history', JSON.stringify(history));
  }

  // Game-specific rule implementations
  static CrashGame = {
    validateCrashPoint(point: number): boolean {
      return point >= 1 && point <= 100;
    },

    calculatePayout(betAmount: number, crashPoint: number): number {
      return betAmount * crashPoint;
    },

    generateCrashPoint(): number {
      // House edge of 1%
      const houseEdge = 0.99;
      const random = Math.random();
      return Math.floor(100 / (random * houseEdge)) / 100;
    }
  };

  static DiceGame = {
    validateBet(prediction: 'OVER' | 'UNDER', number: number): boolean {
      return number >= 1 && number <= 100;
    },

    calculatePayout(betAmount: number, prediction: 'OVER' | 'UNDER', number: number): number {
      const probability = prediction === 'OVER' ? (100 - number) / 100 : number / 100;
      // House edge of 1%
      return betAmount * (0.99 / probability);
    },

    generateResult(): number {
      return Math.floor(Math.random() * 100) + 1;
    }
  };

  static RouletteGame = {
    validateBet(betType: string, number?: number): boolean {
      const validBetTypes = ['straight', 'split', 'street', 'corner', 'line', 'dozen', 'column', 'red', 'black', 'even', 'odd', '1-18', '19-36'];
      return validBetTypes.includes(betType);
    },

    calculatePayout(betAmount: number, betType: string): number {
      const payoutMultipliers = {
        straight: 35,
        split: 17,
        street: 11,
        corner: 8,
        line: 5,
        dozen: 2,
        column: 2,
        red: 1,
        black: 1,
        even: 1,
        odd: 1,
        '1-18': 1,
        '19-36': 1
      };

      // House edge applied in the result generation
      return betAmount * payoutMultipliers[betType as keyof typeof payoutMultipliers];
    },

    generateResult(): number {
      return Math.floor(Math.random() * 37); // 0-36
    }
  };
} 