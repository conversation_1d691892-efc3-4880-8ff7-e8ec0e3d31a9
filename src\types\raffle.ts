export enum RaffleStatus {
  PENDING = 'PENDING',
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export enum TicketSource {
  PURCHASED = 'PURCHASED',
  TOKEN_EXCHANGE = 'TOKEN_EXCHANGE'
}

export enum PrizeSource {
  BRAND_PARTNER = 'BRAND_PARTNER',
  AMAZON_AFFILIATE = 'AMAZON_AFFILIATE',
  CUSTOM = 'CUSTOM'
}

export interface BrandPartner {
  id: string;
  name: string;
  email: string;
  company: string;
  website?: string;
  logo?: string;
  verified: boolean;
  createdAt: number;
  lastLogin: number;
  notificationPreferences: {
    email: boolean;
    dashboard: boolean;
    sales: boolean;
    raffleCompletion: boolean;
  };
  salesStats: {
    totalSales: number;
    totalRaffles: number;
    successRate: number;
    revenue: number;
  };
}

export type RaffleTier = 'BRONZE' | 'SILVER' | 'GOLD' | 'PLATINUM' | 'JACKPOT';

export interface RafflePrize {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
  tier: RaffleTier;
  value: number;
  maxTickets: number;
  ticketsSold: number;
  ticketPrice: number;
  endDate: Date;
}

export interface RaffleTicket {
  id: string;
  prizeId: string;
  userId: string;
  purchaseDate: Date;
  ticketNumber: number;
}

export interface TierInfo {
  name: RaffleTier;
  ticketCost: number;
  maxTickets: number;
  minPrizeValue: number;
  maxPrizeValue: number;
  description: string;
  backgroundColor: string;
  textColor: string;
}

export const TIER_INFO: Record<RaffleTier, TierInfo> = {
  BRONZE: {
    name: 'BRONZE',
    ticketCost: 10,
    maxTickets: 500,
    minPrizeValue: 5,
    maxPrizeValue: 50,
    description: 'Gift cards, small gadgets, and accessories',
    backgroundColor: '#CD7F32',
    textColor: '#FFFFFF'
  },
  SILVER: {
    name: 'SILVER',
    ticketCost: 50,
    maxTickets: 1000,
    minPrizeValue: 50,
    maxPrizeValue: 200,
    description: 'Branded goods and medium-sized electronics',
    backgroundColor: '#C0C0C0',
    textColor: '#000000'
  },
  GOLD: {
    name: 'GOLD',
    ticketCost: 100,
    maxTickets: 5000,
    minPrizeValue: 200,
    maxPrizeValue: 1000,
    description: 'High-quality electronics and premium items',
    backgroundColor: '#FFD700',
    textColor: '#000000'
  },
  PLATINUM: {
    name: 'PLATINUM',
    ticketCost: 500,
    maxTickets: 10000,
    minPrizeValue: 1000,
    maxPrizeValue: 5000,
    description: 'Luxury watches and high-end electronics',
    backgroundColor: '#E5E4E2',
    textColor: '#000000'
  },
  JACKPOT: {
    name: 'JACKPOT',
    ticketCost: 1000,
    maxTickets: 50000,
    minPrizeValue: 5000,
    maxPrizeValue: 100000,
    description: 'Luxury vacations and exclusive experiences',
    backgroundColor: '#000000',
    textColor: '#FFD700'
  }
};

export interface Raffle {
  id: string;
  title: string;
  description: string;
  prize: RafflePrize;
  status: RaffleStatus;
  startTime: number;
  endTime?: number;
  ticketPrice: number;
  tokenEntryPrice: number;
  totalTickets: number;
  soldTickets: number;
  remainingTickets: number;
  winningTicketNumber?: number;
  winnerId?: string;
  participants: {
    userId: string;
    tickets: number[];
  }[];
  rules: string[];
  featured: boolean;
  createdBy: string;
  createdAt: number;
  updatedAt: number;
  stats: {
    totalRevenue: number;
    tokenEntries: number;
    paidEntries: number;
    conversionRate: number;
  };
}

export interface RaffleAnalytics {
  totalRevenue: number;
  activeRaffles: number;
  completedRaffles: number;
  totalParticipants: number;
  averageTicketsSold: number;
  popularPrizes: {
    prizeId: string;
    participantCount: number;
    revenue: number;
  }[];
  brandPartnerPerformance: {
    partnerId: string;
    rafflesHosted: number;
    totalRevenue: number;
    averageParticipation: number;
  }[];
  tokenUsageStats: {
    totalTokensSpent: number;
    averageTokensPerEntry: number;
    tokenToTicketRatio: number;
  };
} 