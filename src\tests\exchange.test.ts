import { CryptoExchange } from '../services/exchange/cryptoExchange';
import { PrismaClient } from '@prisma/client';
import { redis } from '../utils/redis';

const prisma = new PrismaClient();

describe('CryptoExchange', () => {
  let exchange: CryptoExchange;

  beforeAll(async () => {
    exchange = CryptoExchange.getInstance();
    await redis.flushall();
  });

  afterEach(async () => {
    await redis.flushall();
  });

  afterAll(async () => {
    await prisma.$disconnect();
    await redis.quit();
  });

  describe('Exchange Rates', () => {
    it('should provide current exchange rates', async () => {
      const rates = await exchange.getExchangeRates();
      
      expect(rates.loomCoinUSD).toBeGreaterThan(0);
      expect(rates.bitcoinUSD).toBeGreaterThan(0);
      expect(rates.loomCoinBTC).toBeGreaterThan(0);
      expect(rates.lastUpdated).toBeLessThanOrEqual(Date.now());
    });

    it('should update rates periodically', async () => {
      const rates1 = await exchange.getExchangeRates();
      
      // Wait for rate update
      await new Promise(resolve => setTimeout(resolve, 61000));
      
      const rates2 = await exchange.getExchangeRates();
      expect(rates2.lastUpdated).toBeGreaterThan(rates1.lastUpdated);
    });
  });

  describe('Trading Operations', () => {
    it('should allow buying LoomCoin with Bitcoin', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+1234567890',
          isVerified: true,
          wallet: {
            create: {
              bitcoinBalance: 1.0,
              loomCoinBalance: 0
            }
          }
        }
      });

      const btcAmount = 0.1;
      const transaction = await exchange.buyLoomCoin(testUser.id, btcAmount);

      expect(transaction.status).toBe('COMPLETED');
      expect(transaction.fromCurrency).toBe('BTC');
      expect(transaction.toCurrency).toBe('LOOM');
      expect(transaction.fromAmount).toBe(btcAmount);

      const wallet = await prisma.wallet.findUnique({
        where: { userId: testUser.id }
      });

      expect(wallet?.bitcoinBalance).toBe(0.9);
      expect(wallet?.loomCoinBalance).toBeGreaterThan(0);
    });

    it('should allow selling LoomCoin for Bitcoin', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+1234567891',
          isVerified: true,
          wallet: {
            create: {
              bitcoinBalance: 0,
              loomCoinBalance: 1000
            }
          }
        }
      });

      const loomAmount = 500;
      const transaction = await exchange.sellLoomCoin(testUser.id, loomAmount);

      expect(transaction.status).toBe('COMPLETED');
      expect(transaction.fromCurrency).toBe('LOOM');
      expect(transaction.toCurrency).toBe('BTC');
      expect(transaction.fromAmount).toBe(loomAmount);

      const wallet = await prisma.wallet.findUnique({
        where: { userId: testUser.id }
      });

      expect(wallet?.loomCoinBalance).toBe(500);
      expect(wallet?.bitcoinBalance).toBeGreaterThan(0);
    });

    it('should prevent trading with insufficient balance', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+1234567892',
          isVerified: true,
          wallet: {
            create: {
              bitcoinBalance: 0.1,
              loomCoinBalance: 100
            }
          }
        }
      });

      await expect(
        exchange.buyLoomCoin(testUser.id, 0.2)
      ).rejects.toThrow();

      await expect(
        exchange.sellLoomCoin(testUser.id, 200)
      ).rejects.toThrow();
    });
  });

  describe('Transaction History', () => {
    it('should track transaction history', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+1234567893',
          isVerified: true,
          wallet: {
            create: {
              bitcoinBalance: 1.0,
              loomCoinBalance: 1000
            }
          }
        }
      });

      // Perform multiple transactions
      await exchange.buyLoomCoin(testUser.id, 0.1);
      await exchange.sellLoomCoin(testUser.id, 500);

      const history = await exchange.getTransactionHistory(testUser.id);
      expect(history).toHaveLength(2);
      expect(history[0].type).toBe('SELL');
      expect(history[1].type).toBe('BUY');
    });

    it('should provide detailed transaction information', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+1234567894',
          isVerified: true,
          wallet: {
            create: {
              bitcoinBalance: 1.0,
              loomCoinBalance: 1000
            }
          }
        }
      });

      const btcAmount = 0.1;
      const transaction = await exchange.buyLoomCoin(testUser.id, btcAmount);

      const history = await exchange.getTransactionHistory(testUser.id);
      const recordedTx = history[0];

      expect(recordedTx.id).toBe(transaction.id);
      expect(recordedTx.fromAmount).toBe(btcAmount);
      expect(recordedTx.timestamp).toBeLessThanOrEqual(Date.now());
    });
  });

  describe('Market Statistics', () => {
    it('should provide market statistics', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+1234567895',
          isVerified: true,
          wallet: {
            create: {
              bitcoinBalance: 1.0,
              loomCoinBalance: 1000
            }
          }
        }
      });

      // Create some market activity
      await exchange.buyLoomCoin(testUser.id, 0.1);
      await exchange.sellLoomCoin(testUser.id, 200);

      const stats = await exchange.getMarketStats();

      expect(stats.currentPrice.usd).toBeGreaterThan(0);
      expect(stats.currentPrice.btc).toBeGreaterThan(0);
      expect(stats.volume24h).toBeGreaterThan(0);
      expect(stats.trades24h).toBe(2);
      expect(stats.high24h).toBeGreaterThan(stats.low24h);
    });

    it('should calculate price based on market activity', async () => {
      const users = await Promise.all([
        prisma.user.create({
          data: {
            phoneNumber: '+1234567896',
            isVerified: true,
            wallet: {
              create: {
                bitcoinBalance: 1.0,
                loomCoinBalance: 1000
              }
            }
          }
        }),
        prisma.user.create({
          data: {
            phoneNumber: '+1234567897',
            isVerified: true,
            wallet: {
              create: {
                bitcoinBalance: 1.0,
                loomCoinBalance: 1000
              }
            }
          }
        })
      ]);

      // Create market activity
      await Promise.all([
        exchange.buyLoomCoin(users[0].id, 0.2),
        exchange.sellLoomCoin(users[1].id, 300)
      ]);

      const stats1 = await exchange.getMarketStats();
      
      // Wait for price update
      await new Promise(resolve => setTimeout(resolve, 61000));
      
      // More market activity
      await Promise.all([
        exchange.buyLoomCoin(users[1].id, 0.3),
        exchange.sellLoomCoin(users[0].id, 400)
      ]);

      const stats2 = await exchange.getMarketStats();

      // Price should reflect market activity
      expect(Math.abs(stats2.currentPrice.usd - stats1.currentPrice.usd)).toBeGreaterThan(0);
    });
  });
}); 