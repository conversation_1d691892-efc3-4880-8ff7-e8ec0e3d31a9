declare module 'yaml-language-server-parser' {
  export interface CompletionItem {
    label: string;
    kind: number;
    detail?: string;
    documentation?: {
      value: string;
    };
    insertText?: string;
  }

  export interface CompletionList {
    items: CompletionItem[];
  }

  export class AutoComplete {
    constructor();
    addSchema(schema: any, schemaId: string): void;
    doComplete(content: string, position: { line: number; character: number }, schema: any): CompletionList;
  }
} 