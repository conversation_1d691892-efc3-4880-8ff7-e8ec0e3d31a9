import React, { useState } from 'react';
import { TermsOfUse } from './src/components/common/TermsOfUse';

const ProfileSetup: React.FC = () => {
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [photo, setPhoto] = useState('');
  const [error, setError] = useState('');
  const [showTerms, setShowTerms] = useState(false);
  const [acceptedTerms, setAcceptedTerms] = useState(false);

  const handleUsernameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUsername(e.target.value);
    setError("");
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    setError("");
  };

  const handlePhotoUploadChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if(e.target.files && e.target.files.length > 0) {
      setPhoto(URL.createObjectURL(e.target.files[0]));
    }
  }

  const handleSubmit = () => {
    if (!username) {
      setError("Username is required");
      return;
    }
    if (!email) {
      setError("Email is required");
      return;
    }
    if (!acceptedTerms) {
      setError("You must accept the Terms of Use to continue");
      return;
    }
    //For Now
    console.log("Username", username);
    console.log("Email", email);
    console.log("Photo:", photo);
    console.log("Terms accepted:", acceptedTerms);
  };

  return (
    <div className="min-h-screen bg-navy-900 flex items-center justify-center p-6">
      <div className="w-full max-w-md bg-navy-800 rounded-lg shadow-xl p-8">
        <h1 className="text-2xl font-bold text-white mb-6 text-center">Create Your Profile</h1>
        
        <div className="space-y-6">
          <div>
            <label className="block text-yellow-500 font-bold mb-2" htmlFor="profilePhoto">
              Add Profile Photo
            </label>
            <input
              type="file"
              name="profilePhoto"
              id="profilePhoto"
              onChange={handlePhotoUploadChange}
              className="block w-full text-sm text-gray-400
                file:mr-4 file:py-2 file:px-4
                file:rounded-full file:border-0
                file:text-sm file:font-semibold
                file:bg-yellow-500 file:text-white
                hover:file:bg-yellow-600
                cursor-pointer"
            />
          </div>

          <div>
            <label className="block text-yellow-500 font-bold mb-2" htmlFor="username">
              Enter Name
            </label>
            <input
              type="text"
              name="username"
              id="username"
              value={username}
              onChange={handleUsernameChange}
              className="block w-full rounded-lg border-gray-700 bg-navy-900 text-white px-4 py-2
                focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
              placeholder="Your name"
            />
          </div>

          <div>
            <label className="block text-yellow-500 font-bold mb-2" htmlFor="email">
              Enter Email ID
            </label>
            <input
              type="email"
              name="email"
              id="email"
              value={email}
              onChange={handleEmailChange}
              className="block w-full rounded-lg border-gray-700 bg-navy-900 text-white px-4 py-2
                focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
              placeholder="Your email"
            />
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="terms"
              checked={acceptedTerms}
              onChange={(e) => setAcceptedTerms(e.target.checked)}
              className="rounded border-gray-700 bg-navy-900 text-yellow-500 focus:ring-yellow-500"
            />
            <label htmlFor="terms" className="text-gray-400">
              I accept the{' '}
              <button
                type="button"
                onClick={() => setShowTerms(true)}
                className="text-yellow-500 hover:text-yellow-400 underline focus:outline-none"
              >
                Terms of Use
              </button>
            </label>
          </div>

          {error && <p className="text-red-500 text-sm">{error}</p>}

          <button
            className="w-full bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-2 px-4 rounded-lg
              transform transition-all duration-200 hover:scale-105"
            type="button"
            onClick={handleSubmit}
          >
            Create Profile
          </button>
        </div>
      </div>

      {/* Terms of Use Modal */}
      {showTerms && (
        <div className="fixed inset-0 z-50 overflow-y-auto bg-black/50 flex items-center justify-center p-4">
          <div className="relative bg-navy-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <button
              onClick={() => setShowTerms(false)}
              className="absolute top-4 right-4 text-gray-400 hover:text-white"
              aria-label="Close Terms of Use"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            <div className="p-6">
              <TermsOfUse />
              <div className="mt-6 flex justify-end">
                <button
                  onClick={() => {
                    setAcceptedTerms(true);
                    setShowTerms(false);
                  }}
                  className="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-2 px-6 rounded-lg"
                >
                  Accept Terms
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileSetup;