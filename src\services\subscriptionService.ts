import { redis } from '../utils/redis';
import <PERSON><PERSON> from 'stripe';

interface PremiumFeatures {
  lightMode: boolean;
  privateGroups: boolean;
  customColors: {
    buttons: string;
    theme: 'DEFAULT' | 'LIGHT' | 'DARK' | 'CUSTOM';
    customScheme?: {
      primary: string;
      secondary: string;
      accent: string;
    };
  };
  newsFeed: {
    updates: boolean;
    raffleWinners: boolean;
    leaderboards: boolean;
  };
}

interface Subscription {
  id: string;
  userId: string;
  status: 'ACTIVE' | 'CANCELLED' | 'EXPIRED';
  startDate: number;
  endDate: number;
  features: PremiumFeatures;
  stripeSubscriptionId?: string;
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2023-10-16',
  typescript: true,
});

export class SubscriptionService {
  private static readonly MONTHLY_PRICE = 1999; // $19.99

  static async createSubscription(
    userId: string,
    paymentMethodId: string
  ): Promise<Subscription> {
    // Create Stripe subscription
    const stripeSubscription = await this.stripe.subscriptions.create({
      customer: await this.getOrCreateStripeCustomer(userId),
      items: [{ price: process.env.STRIPE_PREMIUM_PRICE_ID }],
      payment_method: paymentMethodId,
      expand: ['latest_invoice.payment_intent']
    });

    const subscription: Subscription = {
      id: `sub_${Date.now()}`,
      userId,
      status: 'ACTIVE',
      startDate: Date.now(),
      endDate: Date.now() + 30 * 24 * 60 * 60 * 1000, // 30 days
      stripeSubscriptionId: stripeSubscription.id,
      features: {
        lightMode: true,
        privateGroups: true,
        customColors: {
          buttons: '#000000',
          theme: 'DEFAULT'
        },
        newsFeed: {
          updates: true,
          raffleWinners: true,
          leaderboards: true
        }
      }
    };

    await redis.set(
      `subscription:${userId}`,
      JSON.stringify(subscription)
    );
    await redis.sadd('premium:users', userId);

    return subscription;
  }

  static async updateFeatures(
    userId: string,
    features: Partial<PremiumFeatures>
  ): Promise<PremiumFeatures> {
    const subscription = await this.getSubscription(userId);
    if (!subscription) throw new Error('No active subscription');

    subscription.features = {
      ...subscription.features,
      ...features
    };

    await redis.set(
      `subscription:${userId}`,
      JSON.stringify(subscription)
    );

    return subscription.features;
  }

  static async cancelSubscription(userId: string): Promise<void> {
    const subscription = await this.getSubscription(userId);
    if (!subscription) throw new Error('No active subscription');

    if (subscription.stripeSubscriptionId) {
      await this.stripe.subscriptions.cancel(subscription.stripeSubscriptionId);
    }

    subscription.status = 'CANCELLED';
    await redis.set(
      `subscription:${userId}`,
      JSON.stringify(subscription)
    );
    await redis.srem('premium:users', userId);
  }

  static async getSubscription(userId: string): Promise<Subscription | null> {
    const data = await redis.get(`subscription:${userId}`);
    return data ? JSON.parse(data) : null;
  }

  static async isPremium(userId: string): Promise<boolean> {
    return await redis.sismember('premium:users', userId);
  }

  private static async getOrCreateStripeCustomer(
    userId: string
  ): Promise<string> {
    const existingCustomerId = await redis.get(`stripe:customer:${userId}`);
    if (existingCustomerId) return existingCustomerId;

    const customer = await this.stripe.customers.create({
      metadata: { userId }
    });

    await redis.set(`stripe:customer:${userId}`, customer.id);
    return customer.id;
  }
} 