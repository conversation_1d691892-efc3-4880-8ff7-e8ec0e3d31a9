import React, { useState } from 'react';

const PhoneNumberEntry: React.FC = () => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');


  const handleVerificationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPhoneNumber(e.target.value);
    setError('');
    setMessage('');
  };


  const handleVerify = () => {
    if (!phoneNumber) {
      setError('Phone number is required');
      return;
    }
    console.log("Phone Number Entered:", phoneNumber);
        setMessage("Phone Number Entered!");
  };

  return (
    <div className="p-6">
        <label
           className="block text-gray-700 font-bold mb-2"
            htmlFor="phoneNumber"
         >
             Enter Phone Number
        </label>
         <div className="relative mt-2 rounded-md shadow-sm">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">

          </div>
          <input
            type="tel"
            name="phoneNumber"
            id="phoneNumber"
           value={phoneNumber}
             onChange={handleVerificationChange}
           className="block w-full rounded-md border-0 py-1.5 pl-4 pr-20 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          placeholder="******-123-4567"
          />
        </div>
        {error && <p className="text-red-500 text-sm mt-2">{error}</p>}
        {message && <p className="text-green-500 text-sm mt-2">{message}</p>}
        <button className="mt-4 bg-yellow-400 hover:bg-yellow-500 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline" type="button" onClick={handleVerify}>
           Get Started
        </button>
      </div>
   );
};

export default PhoneNumberEntry;