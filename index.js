const express = require('express');
const cors = require('cors');
const app = express();
const { generateId, sendErrorResponse } = require('./utils');

// Enable Cross-Origin Resource Sharing
app.use(cors());
app.use(express.json());

// In-memory users data
const users = [
  {
     id: generateId(),
      loomTokens: 400,
      loomCoin: 0,
      joinedLoomId: null,
      isPremium: false,
      blocked: false,
      verified: false
    },
   {
      id: generateId(),
      loomTokens: 100,
      loomCoin: 0,
       joinedLoomId: null,
        isPremium: false,
      blocked: false,
        verified: false
    },
    {
       id: generateId(),
      loomTokens: 1000,
      loomCoin: 20,
       joinedLoomId: null,
        isPremium: true,
      blocked: false,
      verified: true
     }
];

// In-memory raffles data
const raffles = [
  {
     id: generateId(),
    name: "iPhone 15 Pro Max",
     image: "link-to-image",
    description: "High-end phone",
     ticketCost: 100,
     totalTickets: 1000,
      ticketsSold: 0,
       prizeType: "Jackpot",
      previousWinners: []
    },
      {
        id: generateId(),
        name: "Airpods Pro 2",
        image: "link-to-image",
        description: "Amazing wireless earbuds",
        ticketCost: 50,
       totalTickets: 5000,
        ticketsSold: 0,
         prizeType: "Gold",
         previousWinners: []
      },
];

// In-memory loom group data
let loomGroups = [
    {
      id: generateId(),
      name: "Loom Group A",
      entryFee: 10,
    spots: {
          "OUTER": [null, null, null, null, null, null, null, null],
         "MIDDLE": [null, null, null, null],
         "INNER": [null, null],
           "CENTER": null
        },
       progressTimer: 30,
        maxPlayers: 15,
      currentPlayers: 0,
       rewardMultiplier: 2,
       type: "token",
        freePlay: false
     },
   {
       id: generateId(),
      name: "Loom Group B",
      entryFee: 10,
       spots: {
         "OUTER": [null, null, null, null, null, null, null, null],
           "MIDDLE": [null, null, null, null],
            "INNER": [null, null],
         "CENTER": null
         },
         progressTimer: 30,
         maxPlayers: 15,
        currentPlayers: 0,
        rewardMultiplier: 2,
          type: "token",
        freePlay: false
  },
  {
      id: generateId(),
        name: "Loom Group C",
          entryFee: 0,
         spots: {
           "OUTER": [null, null, null, null, null, null, null, null],
            "MIDDLE": [null, null, null, null],
              "INNER": [null, null],
              "CENTER": null
         },
          progressTimer: 30,
       maxPlayers: 15,
        currentPlayers: 0,
       rewardMultiplier: 2,
       type: "free",
        freePlay: true
      }
   ];

// GET list of users
app.get('/api/users', (req, res) => {
    res.json(users);
  });

// GET single user by id
  app.get('/api/users/:id', (req, res) => {
        const userId = req.params.id;
       const user = users.find(u => u.id === userId);
         if(!user) {
             return sendErrorResponse(res, 404, "User not found");
           }
        res.json(user);
  });


// GET list of raffles
app.get('/api/raffles', (req, res) => {
 res.json(raffles);
});

// GET single raffle by ID
app.get('/api/raffles/:id', (req, res) => {
    const raffleId = req.params.id;
    const raffle = raffles.find(r => r.id === raffleId);

    if (!raffle){
         return sendErrorResponse(res, 404, 'Raffle not found');
      }

    res.json(raffle);
});


  // POST - Handle ticket purchase
  app.post('/api/raffles/:id/buy', (req, res) => {
    const raffleId = req.params.id;
   const { userId, ticketsToBuy } = req.body;


     const raffle = raffles.find(r => r.id === raffleId);
        if (!raffle) {
           return sendErrorResponse(res, 404, 'Raffle not found');
          }
        if (!userId || !ticketsToBuy || ticketsToBuy <= 0 || isNaN(ticketsToBuy)) {
          return sendErrorResponse(res, 400, 'Invalid user ID or ticket count.');
        }

      if (raffle.ticketsSold + ticketsToBuy > raffle.totalTickets) {
            return sendErrorResponse(res, 400, "Not enough tickets left");
         }

    raffle.ticketsSold += ticketsToBuy;
    res.json({message: "Tickets Bought", raffle: raffle});
});

// GET list of loom groups
app.get('/api/loom-groups', (req, res) => {
 const type = req.query.type;
    if (type) {
         const filteredGroups = loomGroups.filter(group => group.type === type)
          res.json(filteredGroups);
     } else {
         res.json(loomGroups);
   }
});


// Get loom group by id
app.get('/api/loom-groups/:id', (req, res) => {
    const groupId = req.params.id;
    const group = loomGroups.find(g => g.id === groupId);

   if (!group){
        return sendErrorResponse(res, 404, 'LoomGroup not found');
    }
    res.json(group);
});


// POST to join the loom group
app.post('/api/loom-groups/:id/join', (req, res) => {
     const groupId = req.params.id;
      const { userId } = req.body;

   const group = loomGroups.find(group => group.id === groupId);
       if (!group) {
          return sendErrorResponse(res, 404, 'Loom Group not found');
        }
       if(group.currentPlayers >= group.maxPlayers) {
           return sendErrorResponse(res, 400, 'Loom is full');
        }

         const user = users.find(u => u.id === userId);
          if (!user) {
              return sendErrorResponse(res, 404, 'User not found');
           }

      let assignedSpot = null;
      if (group.spots.OUTER.includes(null)) {
          const nullIndex = group.spots.OUTER.indexOf(null);
            group.spots.OUTER[nullIndex] = userId;
          assignedSpot = "OUTER"
    } else if(group.spots.MIDDLE.includes(null)) {
           const nullIndex = group.spots.MIDDLE.indexOf(null);
        group.spots.MIDDLE[nullIndex] = userId;
            assignedSpot = "MIDDLE"
       }
      else if(group.spots.INNER.includes(null)) {
         const nullIndex = group.spots.INNER.indexOf(null);
           group.spots.INNER[nullIndex] = userId;
           assignedSpot = "INNER"
    }
       else if(group.spots.CENTER === null){
          group.spots.CENTER = userId
            assignedSpot = "CENTER"
      }

        group.currentPlayers += 1;
      user.joinedLoomId = groupId;

    res.json({ message: 'Successfully joined loom', updatedGroup: group, updatedUser: user, assignedSpot: assignedSpot});
});

// POST to create a user
app.post('/api/users/create', (req, res) => {
   const {phoneNumber, username, email, photo, idUpload} = req.body;

       // check for duplicates
    if (users.find(u => u.username === username || u.email === email)) {
           return sendErrorResponse(res, 400, "User already exists");
    }

   const newUser = {
         id: generateId(),
         phoneNumber: phoneNumber,
        username: username,
         email: email,
        photo: photo,
          idUpload: idUpload,
         loomTokens: 0,
         loomCoin: 0,
         joinedLoomId: null,
        isPremium: false,
          blocked: false,
           verified: false
       }
       users.push(newUser);
      return res.json({message : "Created a new user", user: newUser})
  });

 // POST to block a user
app.post('/api/users/:id/block', (req, res) => {
   const userId = req.params.id;
    const { blocked } = req.body;

  const user = users.find(u => u.id === userId);
       if (!user) {
            return sendErrorResponse(res, 404, "User not found");
       }
       user.blocked = blocked;
        return res.json({message: "User Blocked", user: user});
    });


 // POST to Verify a user
 app.post('/api/users/:id/verify', (req, res) => {
       const userId = req.params.id;
    const { verified } = req.body;

    const user = users.find(u => u.id === userId);
      if (!user) {
           return sendErrorResponse(res, 404, "User not found");
      }
      user.verified = verified;
      return res.json({message: "User Verified", user: user});
   });


app.get('/', (req, res) => {
    res.send('<h1>This is the LoomLoot API</h1>');
  });


const port = 3001;
app.listen(port, () => {
    console.log(`Server running on port ${port}`);
});