# LoomLoot Development Plan

## Phase 1: Core Infrastructure Setup
[ ] Database & Authentication
    - PostgreSQL database setup
    - Redis caching layer
    - User authentication with 2FA
    - ID verification system
    - Phone verification system

[ ] Token System
    - Token wallet implementation
    - Payment processing integration
    - Transaction tracking
    - Balance management

[ ] Game Core
    - Round mechanics
    - Win/loss calculation
    - Token distribution
    - Basic leaderboard

[ ] Group System
    - Tier-based groups
    - Balance-based access
    - Board management
    - Player matching

## Phase 2: Game Implementation
[ ] Free Play Mode
    - 5 rounds limit
    - Practice environment
    - Tutorial system
    - Skip option

[ ] Main Game Mode
    - Real token gameplay
    - Multiple board support
    - Round synchronization
    - Anti-cheat measures

[ ] Wallet Integration
    - Direct deposit setup
    - Withdrawal system
    - Transaction history
    - Balance tracking

## Required Information Checklist

### Game Mechanics
- [ ] Round duration
- [ ] Number of players per board
- [ ] Token entry amounts per tier
- [ ] Win calculation formulas
- [ ] House fee percentage
- [ ] Minimum/maximum bets
- [ ] Tier requirements
- [ ] Winning spot selection method

### Payment System
- [ ] Supported payment methods
- [ ] Minimum deposit amount
- [ ] Maximum withdrawal limit
- [ ] Processing timeframes
- [ ] KYC requirements
- [ ] Fee structure
- [ ] Payout methods
- [ ] Currency handling

### User Verification
- [ ] Accepted ID types
- [ ] Verification process steps
- [ ] Age restrictions
- [ ] Location restrictions
- [ ] Phone verification method
- [ ] Document storage policy
- [ ] Verification timeframe
- [ ] Appeal process

### Group System
- [ ] Number of tiers
- [ ] Entry requirements per tier
- [ ] Maximum players per group
- [ ] Board creation rules
- [ ] Player matching criteria
- [ ] Tier progression rules
- [ ] Group restrictions
- [ ] Special tier features

### Security Requirements
- [ ] Session management
- [ ] Rate limiting rules
- [ ] IP restriction policy
- [ ] Device verification
- [ ] Transaction monitoring
- [ ] Fraud prevention measures
- [ ] Data encryption standards
- [ ] Backup procedures

## Technical Stack
- Backend: Node.js with TypeScript
- Frontend: Next.js with TypeScript
- Database: PostgreSQL
- Caching: Redis
- Containerization: Docker
- CI/CD: GitHub Actions
- Monitoring: Prometheus & Grafana
- Testing: Jest & Cypress

## Development Phases

### Phase 1: Core Infrastructure (Current)
[X] Initial Planning
[ ] Database Schema Implementation
[ ] Authentication System
[ ] Token System
[ ] Basic Game Logic

### Phase 2: Game Features
[ ] Free Play Implementation
[ ] Main Game Mode
[ ] Group System
[ ] Wallet Integration

### Phase 3: Testing & Security
[ ] Unit Tests
[ ] Integration Tests
[ ] Security Audit
[ ] Performance Testing

### Phase 4: Deployment
[ ] Staging Environment
[ ] Production Setup
[ ] Monitoring Implementation
[ ] Backup Systems

## Questions for Each Phase

### Database & Authentication
1. What user data needs to be stored beyond basic info?
2. What authentication methods should be supported?
3. What are the specific KYC requirements?
4. How should user sessions be handled?

### Token System
1. What is the token to currency conversion rate?
2. What payment providers need to be integrated?
3. What are the withdrawal thresholds?
4. How should failed transactions be handled?

### Game Core
1. What are the exact game rules?
2. How should ties be handled?
3. What anti-cheat measures are needed?
4. How should disconnections be handled?

### Group System
1. What are the tier thresholds?
2. How should player matching work?
3. What happens when a tier is underpopulated?
4. How are boards created and managed?

## Next Steps
1. Review and answer questions for current phase
2. Implement core database schema
3. Set up authentication system
4. Begin token system implementation

Would you like to start by answering the questions for the Database & Authentication phase?

## MVP Phase (Core Gaming Features)
[X] Initial Planning
[ ] Core Features Implementation

### Required Features
1. Landing Page & Freeplay
   - Homepage with freeplay option
   - 5 rounds limit for non-registered users
   - Skip freeplay button
   - Basic game mechanics

2. User Authentication & Verification
   - Basic registration/login
   - ID verification
   - Phone verification
   - Essential user profile
   - Basic account settings

3. Token System
   - Buy tokens page
   - Basic token wallet
   - Token balance tracking
   - Payment processing

4. Group System
   - Tier-based groups
   - Balance-based access control
   - Group boards listing
   - Basic board joining mechanics

5. Game Core
   - Basic round mechanics
   - Win/loss tracking
   - Token distribution
   - Basic leaderboard

6. Basic Wallet & Cashout
   - Direct deposit info
   - Basic withdrawal system
   - Transaction history
   - Balance management

### MVP Flow
1. User lands on homepage
2. Options:
   - Play 5 free rounds
   - Skip to registration
3. Registration/verification
4. Token purchase
5. Enter lobby
6. Join tier-based group
7. Select board
8. Play rounds
9. Cash out winnings

## Update 1: Social & Engagement Features
[ ] Planning Phase
[ ] Implementation

1. 1-on-1 Games
   - Challenge system
   - Private game rooms
   - Custom game settings

2. Raffle System
   - Ticket purchase
   - Drawing mechanics
   - Prize distribution

3. Messaging System
   - Direct messaging
   - Group chats
   - Notifications

## Update 2: Advanced Economy
[ ] Planning Phase
[ ] Implementation

1. LoomCoin Implementation
   - Smart contract development
   - Wallet integration
   - Exchange system

2. Advanced Wallet Features
   - Multi-currency support
   - Transaction analytics
   - Enhanced security features
   - Automated payouts

## Update 3: Platform Enhancement
[ ] Planning Phase
[ ] Implementation

1. Social Features
   - User profiles
   - Friend system
   - Activity feed

2. Tournament System
   - Brackets
   - Scheduling
   - Prize pools

3. Achievement System
   - Badges
   - Rewards
   - Progress tracking

4. Enhanced Analytics
   - Player statistics
   - Game analytics
   - Performance metrics

## Technical Requirements

### MVP Containers
1. User Service
   - Authentication
   - Profile management
   - Verification

2. Game Service
   - Game logic
   - Round management
   - Score tracking

3. Token Service
   - Balance management
   - Transaction processing
   - Basic wallet functions

4. Group Service
   - Tier management
   - Board organization
   - Access control

5. Payment Service
   - Payment processing
   - Withdrawal management
   - Transaction security

### Database Schema
1. Users
   - Authentication info
   - Profile data
   - Verification status

2. Games
   - Round data
   - Scores
   - Game history

3. Tokens
   - Balances
   - Transactions
   - Pricing

4. Groups
   - Tier info
   - Board data
   - Member management

5. Payments
   - Payment records
   - Withdrawal info
   - Transaction logs

### API Endpoints
1. /api/auth/*
2. /api/games/*
3. /api/tokens/*
4. /api/groups/*
5. /api/payments/*

## Progress Tracking
- [ ] MVP Development
  - [ ] Core Features
  - [ ] Testing
  - [ ] Deployment

- [ ] Update 1
  - [ ] Social Features
  - [ ] Testing
  - [ ] Deployment

- [ ] Update 2
  - [ ] Economy Features
  - [ ] Testing
  - [ ] Deployment

- [ ] Update 3
  - [ ] Enhancement Features
  - [ ] Testing
  - [ ] Deployment

## LoomLoot Development Plan - Requirements

## Database & Authentication (ANSWERED)
✓ User Data Storage:
- Basic: Name, Email, Username, Phone Number
- Verification: Government ID, Date of Birth, Location
- Financial: Wallet Address, Transaction History
- Game: Game History & Stats, Tokens Balance
- Profile: Verification Status, Account Status, Profile Picture
- Referral: Referral Code

✓ Authentication Methods:
- Email/Password
- Google Login
- Facebook Login
- Apple ID
- Full KYC required for deposits/withdrawals

✓ ID Verification:
- Government-issued ID (Passport, Driver's License, National ID)
- Selfie verification
- Utility Bill (for recovery & address verification)

✓ Session Management:
- 24-hour session expiration unless actively playing
- Single active session per user (MVP)
- New device verification via email/SMS

## Token System (ANSWERED)
✓ Token Economics:
- 1 Loom Token = $1 in-app value
- Cashout Rate: 2 Loom Tokens = $1 USD
- LoomCoin: Floating market value (+1% per purchase)

✓ Payment Methods:
- Credit/Debit Cards (Visa, Mastercard)
- PayPal
- Future: Crypto (USDT, ETH, BTC, LoomCoin)
- Future: Bank Transfers

✓ Transaction Limits:
- Minimum Deposit: $10
- Minimum Withdrawal: $100 (200 tokens)
- Daily Withdrawal: $10,000
- Weekly Withdrawal: $50,000
- Monthly Withdrawal: $250,000
- First Cashout: 2-week waiting period

✓ Fees:
- House Fee: 12.5% (1/8) per round
- Deposit Fees: 0% for cards/stripe, 1% crypto
- Withdrawal Fees: 2% bank, 1% crypto

## Game Core (ANSWERED)
✓ Round Mechanics:
- Duration: 60 seconds (MVP), 15/30/60 based on activity
- Players per board: 15 active + 8 waiting = 23 total
- First In, First Out movement
- Automatic 60-second advancement

✓ Winning:
- Center position wins 8x entry fee minus house fee
- Automatic payout calculation
- Winners must have 15 active players for payout

✓ Disconnection Handling:
- Position remains but player is skipped for payment
- Assets locked and forfeited if quit/disconnect
- Up to 5 simultaneous group joins allowed

## Group System (ANSWERED)
✓ Tier Structure:
- 7 Public Tiers: 10, 20, 50, 100, 300, 500, 1000 tokens
- Private Groups: Up to 10,000 tokens (Premium)
- Verified account required for all tiers

✓ Board Management:
- Automatic entry system
- Instant join on open spots
- Boards merge when underpopulated
- New boards created when others full

✓ Restrictions:
- 1 active board per tier
- 5 active boards total if different tiers
- Must win in lower tier to access higher tiers

## Security Requirements (ANSWERED)
✓ Access Control:
- 24-hour inactivity logout
- 5 login attempts per hour
- Multi-account detection
- Device verification required
- USA users only initially

✓ Fraud Prevention:
- AI transaction monitoring
- 2FA for withdrawals
- AES-256 encryption
- Daily encrypted backups

## Premium Features (ANSWERED)
✓ Additional Features:
- Light Mode theme
- Private Groups
- Team Visibility
- Custom Invitations
- News Feed
- Raffle Updates
- Leaderboards
- Custom Color Schemes

Next Steps:
1. Begin database schema implementation with these specifications
2. Set up authentication system with specified providers
3. Implement token system with defined rates and limits
4. Create group system with tier structure

Would you like me to start implementing any specific component with these requirements? 