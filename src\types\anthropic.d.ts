declare module '@anthropic-ai/sdk' {
  export class Claude {
    constructor(options: { apiKey: string });
    
    messages: {
      create(params: {
        model: string;
        max_tokens: number;
        temperature: number;
        messages: Array<{
          role: 'user' | 'assistant' | 'system';
          content: string;
        }>;
        system?: string;
      }): Promise<{
        content: Array<{ text: string }>;
        usage: {
          input_tokens: number;
          output_tokens: number;
        };
      }>;
    };
  }
} 