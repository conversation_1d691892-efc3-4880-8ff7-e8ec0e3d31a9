import { redis } from '../utils/redis';
import { v4 as uuidv4 } from 'uuid';

interface ReferralProgram {
  isActive: boolean;
  commissionRate: number; // 50% of house fees
  expiryDays: number;    // 30 days by default
}

interface Referral {
  id: string;
  referrerId: string;
  referredId: string;
  code: string;
  createdAt: number;
  expiresAt: number;
  isActive: boolean;
  totalCommission: number;
}

export class ReferralService {
  private static readonly DEFAULT_EXPIRY_DAYS = 30;
  
  static async generateReferralCode(userId: string): Promise<string> {
    const code = `${userId.slice(0, 6)}${Math.random().toString(36).slice(2, 8)}`;
    await redis.set(`referral:code:${code}`, userId);
    return code;
  }

  static async createReferralLink(userId: string): Promise<string> {
    const code = await this.generateReferralCode(userId);
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://loomloot.com';
    return `${baseUrl}/ref/${code}`;
  }

  static async trackReferral(
    referrerId: string,
    referredId: string,
    code: string
  ): Promise<Referral> {
    const referral: Referral = {
      id: uuidv4(),
      referrerId,
      referredId,
      code,
      createdAt: Date.now(),
      expiresAt: Date.now() + (this.DEFAULT_EXPIRY_DAYS * 24 * 60 * 60 * 1000),
      isActive: true,
      totalCommission: 0
    };

    await redis.set(`referral:${referral.id}`, JSON.stringify(referral));
    await redis.set(`user:${referredId}:referrer`, referrerId);
    
    return referral;
  }

  static async addCommission(
    referralId: string,
    houseFee: number
  ): Promise<number> {
    const referralData = await redis.get(`referral:${referralId}`);
    if (!referralData) throw new Error('Referral not found');

    const referral: Referral = JSON.parse(referralData);
    if (!referral.isActive || Date.now() > referral.expiresAt) {
      return 0;
    }

    const commission = houseFee * 0.5; // 50% of house fee
    referral.totalCommission += commission;

    await redis.set(`referral:${referralId}`, JSON.stringify(referral));
    await redis.zincrby(
      `user:${referral.referrerId}:commissions`,
      commission,
      referralId
    );

    return commission;
  }

  static async getReferralStats(userId: string): Promise<{
    totalReferrals: number;
    activeReferrals: number;
    totalCommission: number;
    recentCommissions: Array<{ amount: number; timestamp: number; }>
  }> {
    const referrals = await redis.smembers(`user:${userId}:referrals`);
    const commissions = await redis.zrange(
      `user:${userId}:commissions`,
      0,
      -1,
      'WITHSCORES'
    );

    const activeReferrals = await Promise.all(
      referrals.map(async (refId) => {
        const data = await redis.get(`referral:${refId}`);
        return data ? JSON.parse(data) : null;
      })
    );

    return {
      totalReferrals: referrals.length,
      activeReferrals: activeReferrals.filter(
        ref => ref && ref.isActive && Date.now() <= ref.expiresAt
      ).length,
      totalCommission: commissions.reduce(
        (sum, score) => sum + parseFloat(score),
        0
      ),
      recentCommissions: commissions.slice(-10).map(([id, amount]) => ({
        amount: parseFloat(amount),
        timestamp: Date.now()
      }))
    };
  }

  static async deactivateReferral(referralId: string): Promise<void> {
    const referralData = await redis.get(`referral:${referralId}`);
    if (!referralData) throw new Error('Referral not found');

    const referral: Referral = JSON.parse(referralData);
    referral.isActive = false;
    
    await redis.set(`referral:${referralId}`, JSON.stringify(referral));
  }
} 