import React, { useState } from 'react';
import { SOUNDS } from '../../constants';
import { playSound } from '../utils/SoundManager';

// Types
interface Message {
  id: string;
  sender: string;
  text: string;
  timestamp: string;
  isOwn: boolean;
}

interface ChatUser {
  name: string;
  iconUrl: string;
}

interface ChatDetailViewProps {
  chatId: string;
  onBack: () => void;
}

// Dummy data for a single message thread (replace later)
const dummyMessages: Message[] = [
  { id: 'm1', sender: '<PERSON>', text: 'OMG!!! Yummy 😍', timestamp: '11:00 AM', isOwn: false },
  { id: 'm2', sender: 'You', text: 'Right? So good!', timestamp: '11:01 AM', isOwn: true },
  { id: 'm3', sender: '<PERSON>', text: 'Where did you get it?', timestamp: '11:01 AM', isOwn: false },
  { id: 'm4', sender: 'You', text: 'Local bakery downtown!', timestamp: '11:02 AM', isOwn: true },
];

const ChatDetailView: React.FC<ChatDetailViewProps> = ({ chatId, onBack }) => {
  const [messageText, setMessageText] = useState<string>('');
  
  // In a real app, fetch chat details and messages based on chatId
  const chatUser: ChatUser = { 
    name: 'Sally Rooney', 
    iconUrl: 'https://play.rosebud.ai/assets/ASSET%20FACE%202.png?JRmr' 
  };

  const handleBack = () => {
    playSound(SOUNDS.UI_CLICK_BUTTON_01);
    onBack();
  };

  const handleSendMessage = () => {
    if (messageText.trim()) {
      playSound(SOUNDS.UI_CLICK_BUTTON_02);
      // TODO: Implement send message functionality
      console.log('Sending message:', messageText);
      setMessageText('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  return (
    <div className="chat-detail-view">
      {/* Header */}
      <div className="chat-detail-header">
        <button className="chat-back-button" onClick={handleBack}>
          ←
        </button>
        <div className="chat-user-info">
          <img 
            src={chatUser.iconUrl} 
            alt={chatUser.name} 
            className="chat-user-icon" 
          />
          <span className="chat-user-name">{chatUser.name}</span>
        </div>
      </div>

      {/* Message List */}
      <div className="chat-message-list">
        {dummyMessages.map(msg => (
          <div
            key={msg.id}
            className={`message-bubble ${msg.isOwn ? 'message-bubble-own' : 'message-bubble-other'}`}
          >
            {msg.text}
          </div>
        ))}
      </div>

      {/* Input Area */}
      <div className="chat-input-area">
        <input 
          type="text" 
          placeholder="Type a message..." 
          className="chat-input"
          value={messageText}
          onChange={(e) => setMessageText(e.target.value)}
          onKeyPress={handleKeyPress}
        />
        <button 
          className="chat-send-button"
          onClick={handleSendMessage}
        >
          ➤
        </button>
      </div>
    </div>
  );
};

export default ChatDetailView;
