export interface User {
  id: string;
  email: string;
  isPremium: boolean;
  isAdmin: boolean;
  onboarding: OnboardingState;
}

export interface OnboardingState {
  completed: boolean;
  currentStep: string;
  completedSteps: string[];
  lastUpdated: Date;
  hasRevisited: boolean;
}

export interface UserProfile {
  id: string;
  userId: string;
  balance: {
    available: number;
    locked: number;
    bonus: number;
    totalDeposited: number;
    totalWithdrawn: number;
  };
  stats: {
    gamesPlayed: number;
    totalWagered: number;
    totalWon: number;
    biggestWin: number;
    winStreak: number;
    currentStreak: number;
    favoriteGame: string;
  };
  achievements: Array<Achievement>;
  level: {
    current: number;
    xp: number;
    nextLevel: number;
  };
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  progress: number;
  completed: boolean;
  unlockedAt?: number;
  reward?: number;
} 