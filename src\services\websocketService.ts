import { Server as HTTPServer } from 'http';
import { Server as WebSocketServer } from 'ws';
import { verifyAuth } from '../utils/auth';

interface WebSocketMessage {
  type: 'DRAWING_RESULT' | 'GAME_UPDATE' | 'WINNER_NOTIFICATION';
  data: any;
}

export class WebSocketService {
  private static wss: WebSocketServer;
  private static clients: Map<string, WebSocket> = new Map();

  static initialize(server: HTTPServer) {
    this.wss = new WebSocketServer({ server });

    this.wss.on('connection', async (ws, req) => {
      try {
        // Verify authentication
        const token = new URL(req.url!, 'http://localhost').searchParams.get('token');
        if (!token) {
          ws.close();
          return;
        }

        const auth = await verifyAuth({ headers: { authorization: token } });
        if (!auth.success) {
          ws.close();
          return;
        }

        // Store client connection
        this.clients.set(auth.userId, ws);

        ws.on('close', () => {
          this.clients.delete(auth.userId);
        });

      } catch (error) {
        console.error('WebSocket Connection Error:', error);
        ws.close();
      }
    });
  }

  static broadcastDrawingResult(result: any) {
    const message: WebSocketMessage = {
      type: 'DRAWING_RESULT',
      data: result
    };

    this.broadcast(message);
  }

  static notifyWinner(userId: string, data: any) {
    const client = this.clients.get(userId);
    if (client) {
      const message: WebSocketMessage = {
        type: 'WINNER_NOTIFICATION',
        data
      };

      client.send(JSON.stringify(message));
    }
  }

  private static broadcast(message: WebSocketMessage) {
    this.clients.forEach(client => {
      client.send(JSON.stringify(message));
    });
  }
} 