import { redis } from '../utils/redis';
import { DrawingService } from './drawingService';
import { NotificationService } from './notificationService';

export class SchedulerService {
  // Schedule types and their intervals
  private static schedules = {
    LOTTERY: {
      days: [3, 6], // Wednesday and Saturday
      hour: 19,     // 7 PM
      minute: 30    // 30 minutes
    },
    DAILY_RAFFLE: {
      hour: 0,      // Midnight
      minute: 0
    },
    WEEKLY_RAFFLE: {
      day: 0,       // Sunday
      hour: 0,
      minute: 0
    },
    MONTHLY_RAFFLE: {
      date: 1,      // First of month
      hour: 0,
      minute: 0
    }
  };

  static async initializeScheduler() {
    // Check and perform drawings
    setInterval(async () => {
      try {
        await this.checkAndPerformDrawings();
      } catch (error) {
        console.error('Scheduler Error:', error);
      }
    }, 60000); // Check every minute
  }

  private static async checkAndPerformDrawings() {
    const now = new Date();

    // Check lottery drawings
    if (this.isLotteryDrawingTime(now)) {
      const result = await DrawingService.performLotteryDrawing();
      await NotificationService.notifyWinners(result);
    }

    // Check raffle drawings
    await this.checkRaffleDrawings(now);
  }

  private static isLotteryDrawingTime(now: Date): boolean {
    const { days, hour, minute } = this.schedules.LOTTERY;
    return days.includes(now.getDay()) &&
           now.getHours() === hour &&
           now.getMinutes() === minute;
  }

  private static async checkRaffleDrawings(now: Date) {
    const activeRaffles = await redis.smembers('active_raffles');

    for (const raffleId of activeRaffles) {
      const raffleData = await redis.get(`raffle:${raffleId}`);
      if (!raffleData) continue;

      const raffle = JSON.parse(raffleData);
      if (this.isRaffleDrawingTime(now, raffle.type)) {
        const result = await DrawingService.performRaffleDrawing(raffleId);
        await NotificationService.notifyWinners(result);
      }
    }
  }

  private static isRaffleDrawingTime(now: Date, type: string): boolean {
    const schedule = this.schedules[type as keyof typeof this.schedules];
    if (!schedule) return false;

    return (
      (!schedule.day || now.getDay() === schedule.day) &&
      (!schedule.date || now.getDate() === schedule.date) &&
      now.getHours() === schedule.hour &&
      now.getMinutes() === schedule.minute
    );
  }
} 