version: '3.8'

services:
  app:
    build: 
      context: .
      target: development
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
      - "9229:9229" # Node.js debugging port
    environment:
      - DATABASE_URL=**************************************/loomloot_dev
      - REDIS_URL=redis://redis:6379
      - NODE_ENV=development
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=dev_secret
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    volumes:
      - .:/app
      - /app/node_modules
      - app_data:/app/data
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      ai:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  ai:
    build:
      context: .
      dockerfile: docker/ai/Dockerfile
    ports:
      - "8000:8000"
    environment:
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - MAX_TOKENS=200000
      - CONTEXT_WINDOW=200000
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./models:/app/models
      - ai_data:/app/data
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  db:
    image: postgres:14-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=loomloot_dev
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d loomloot_dev"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  adminer:
    image: adminer
    ports:
      - "8080:8080"
    depends_on:
      db:
        condition: service_healthy

  redis-commander:
    image: rediscommander/redis-commander:latest
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      redis:
        condition: service_healthy

volumes:
  postgres_data:
    name: loomloot_postgres_dev
  redis_data:
    name: loomloot_redis_dev
  app_data:
    name: loomloot_app_dev
  ai_data:
    name: loomloot_ai_dev 