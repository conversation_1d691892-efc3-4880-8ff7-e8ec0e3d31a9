// Keep only the essential sounds used in the game logic and UI interactions
export const SOUNDS = {
  // UI Clicks
  UI_CLICK_BUTTON_01: 'https://play.rosebud.ai/assets/ui_click_button_01%20-%20Copy.wav?9Ul2',
  UI_CLICK_BUTTON_02: 'https://play.rosebud.ai/assets/ui_click_button_02.ogg?phoP',
  UI_CLICK_TOKEN_BUY: 'https://play.rosebud.ai/assets/ui_click_token_buy.wav?gbEW',
  UI_CLICK_MENU_SELECT: 'https://play.rosebud.ai/assets/ui_click_menu_select.wav?j6Bo',
  // UI Hovers
  UI_HOVER_BUTTON_01: 'https://play.rosebud.ai/assets/ui_hover_button_01.wav?fhOz',
  UI_HOVER_MENU_01: 'https://play.rosebud.ai/assets/ui_hover_menu_01.wav?fTK9',
  UI_HOVER_TOKEN_01: 'https://play.rosebud.ai/assets/ui_hover_button_01.wav?fhOz', // Added for token hover
  // Game Actions
  MOVE: 'https://play.rosebud.ai/assets/game_move_piece_01.wav?rND2',
  WIN: 'https://play.rosebud.ai/assets/game_win_round_01.wav?ZjxJ',
  ROUND_END: 'https://play.rosebud.ai/assets/game_timer_end_01.mp3?avh1',
  TIMER_URGENT: 'https://play.rosebud.ai/assets/game_timer_urgent_01.mp3?jfb5',
  TIMER_TICK: 'https://play.rosebud.ai/assets/game_timer_tick_01.mp3?nB3a',
  BOARD_SPLIT: 'https://play.rosebud.ai/assets/fx_trans_round_01.wav?3Sel',
  // Purchase sounds
  UI_SUCCESS_PURCHASE_01: 'https://play.rosebud.ai/assets/ui_click_token_buy.wav?gbEW', // Added for purchase success
};

// Available player icons
export const PLAYER_ICONS = [
  'https://play.rosebud.ai/assets/ASSET FACE 2.png?JRmr',
  'https://play.rosebud.ai/assets/ASSET FACE 4.png?i4TK',
  'https://play.rosebud.ai/assets/ASSET FACE 5.png?dNC6',
];

// Placeholder player data - replace with actual data later
export const initialPlayers: any[] = [];

// Board layout and slot data
export const boardData = {
  type: 'board',
  slots: [
    // Orange Join Slots (Outside of Blue ring)
    { id: 'orange-1', color: 'orange', img: 'https://rosebud.ai/assets/slot_orange_1.png.png?At2f' },
    { id: 'orange-2', color: 'orange', img: 'https://rosebud.ai/assets/slot_orange_2.png?zsdi' },
    { id: 'orange-3', color: 'orange', img: 'https://rosebud.ai/assets/slot_orange_3.png?GCWU' },
    { id: 'orange-4', color: 'orange', img: 'https://rosebud.ai/assets/slot_orange_4.png?HiNA' },
    { id: 'orange-5', color: 'orange', img: 'https://rosebud.ai/assets/slot_orange_5.png?zbYG' },
    { id: 'orange-6', color: 'orange', img: 'https://rosebud.ai/assets/slot_orange_6.png?rysp' },
    { id: 'orange-7', color: 'orange', img: 'https://rosebud.ai/assets/slot_orange_7.png?ZcEy' },
    { id: 'orange-8', color: 'orange', img: 'https://rosebud.ai/assets/slot_orange_8.png?B6ny' },
    // Blue Slots (Outer Ring of Active Players)
    { id: 'blue-1', color: 'blue', img: 'https://rosebud.ai/assets/slot_blue_1.png?XrAP' },
    { id: 'blue-2', color: 'blue', img: 'https://rosebud.ai/assets/slot_blue_2.png?ZaGv' },
    { id: 'blue-3', color: 'blue', img: 'https://rosebud.ai/assets/slot_blue_3.png?aEKX' },
    { id: 'blue-4', color: 'blue', img: 'https://rosebud.ai/assets/slot_blue_4.png?Fksg' },
    { id: 'blue-5', color: 'blue', img: 'https://rosebud.ai/assets/slot_blue_5.png?aMKN' },
    { id: 'blue-6', color: 'blue', img: 'https://rosebud.ai/assets/slot_blue_6.png?dIaN' },
    { id: 'blue-7', color: 'blue', img: 'https://rosebud.ai/assets/slot_blue_7.png?iDg5' },
    { id: 'blue-8', color: 'blue', img: 'https://rosebud.ai/assets/slot_blue_8.png?NlU2' },
    // Green Slots (Middle Ring)
    { id: 'green-1', color: 'green', img: 'https://rosebud.ai/assets/slot_green_1.png?s32O' },
    { id: 'green-2', color: 'green', img: 'https://rosebud.ai/assets/slot_green_2.png-.png?Ygi9' },
    { id: 'green-3', color: 'green', img: 'https://rosebud.ai/assets/slot_green_3.pnG?oVGW' },
    { id: 'green-4', color: 'green', img: 'https://rosebud.ai/assets/slot_green_4.png?Gjpa' },
    // Pink Slots (Inner Ring)
    { id: 'pink-1', color: 'pink', img: 'https://rosebud.ai/assets/pink_slot-1.PNG?kLlr' },
    { id: 'pink-2', color: 'pink', img: 'https://rosebud.ai/assets/pink_slot-2.PNG?EXdh' },
    // Purple Slot (Center)
    { id: 'purple-1', color: 'purple', img: 'https://rosebud.ai/assets/slot_purple_1.png.png?H9i9' },
  ],
};

// Game configuration constants
export const GAME_CONFIG = {
  WIN_REWARD: 50,
  TIMER_INTERVALS: {
    HIGH_ACTIVITY: 15,
    MODERATE_ACTIVITY: 30,
    LOW_ACTIVITY: 60,
  },
  MAX_PLAYERS_PER_BOARD: 25, // Based on slot count
};

// Navigation routes
export const ROUTES = {
  HOME: 'home',
  PLAY: 'play',
  LOOM_LOBBY: 'loom-lobby',
  BUY_TOKENS: 'buyTokens',
  BUY_LOOMCOIN: 'buyLoomCoin',
  PROFILE: 'profile',
  SETTINGS: 'settings',
  CHAT: 'chat',
  STORIES: 'stories',
  MY_LOOMS: 'myLooms',
} as const;

export type RouteType = typeof ROUTES[keyof typeof ROUTES];
