#!/bin/bash

# Set colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Function to check if <PERSON><PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}Error: Docker is not running${NC}"
        exit 1
    fi
}

# Function to display usage
usage() {
    echo "Usage: $0 [command]"
    echo
    echo "Commands:"
    echo "  start        Start AI service"
    echo "  stop         Stop AI service"
    echo "  restart      Restart AI service"
    echo "  logs         Show AI service logs"
    echo "  status       Show AI service status"
    echo "  test         Test AI service with a sample query"
    echo "  help         Show this help message"
}

# Start AI service
start() {
    echo -e "${YELLOW}Starting AI service...${NC}"
    docker-compose up -d ai
    echo -e "${GREEN}AI service started${NC}"
    echo "Access the API at http://localhost:8000"
    echo "Access the docs at http://localhost:8000/docs"
}

# Stop AI service
stop() {
    echo -e "${YELLOW}Stopping AI service...${NC}"
    docker-compose stop ai
    echo -e "${GREEN}AI service stopped${NC}"
}

# Restart AI service
restart() {
    stop
    start
}

# Show AI service logs
logs() {
    docker-compose logs -f ai
}

# Show AI service status
status() {
    echo -e "${YELLOW}AI Service Status:${NC}"
    docker-compose ps ai
    
    echo -e "\n${YELLOW}Health Check:${NC}"
    curl -s http://localhost:8000/health | jq .
    
    echo -e "\n${YELLOW}Available Models:${NC}"
    curl -s http://localhost:8000/models | jq .
}

# Test AI service
test() {
    echo -e "${YELLOW}Testing AI service with sample query...${NC}"
    curl -s -X POST http://localhost:8000/query \
        -H "Content-Type: application/json" \
        -d '{
            "prompt": "Hello! How are you?",
            "max_tokens": 100,
            "temperature": 0.7
        }' | jq .
}

# Check if Docker is running
check_docker

# Parse command line arguments
case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    logs)
        logs
        ;;
    status)
        status
        ;;
    test)
        test
        ;;
    help)
        usage
        ;;
    *)
        usage
        exit 1
        ;;
esac

exit 0 