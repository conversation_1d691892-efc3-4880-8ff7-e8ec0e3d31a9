import { redis } from '../utils/redis';
import { GameRulesService } from './gameRules';
import { NotificationService } from './notification';

interface GameState {
  roomId: string;
  gameType: string;
  status: 'WAITING' | 'BETTING' | 'IN_PROGRESS' | 'COMPLETED';
  currentRound: number;
  players: {
    id: string;
    username: string;
    betAmount: number;
    betData?: any;
    status: 'ACTIVE' | 'CASHED_OUT' | 'BUST';
  }[];
  gameData: Record<string, any>;
  startTime?: number;
  endTime?: number;
}

export class GameStateManager {
  private static async getState(roomId: string): Promise<GameState | null> {
    const state = await redis.get(`gamestate:${roomId}`);
    return state ? JSON.parse(state) : null;
  }

  private static async setState(roomId: string, state: GameState): Promise<void> {
    await redis.set(`gamestate:${roomId}`, JSON.stringify(state));
  }

  static async initializeGame(roomId: string, gameType: string): Promise<GameState> {
    const state: GameState = {
      roomId,
      gameType,
      status: 'WAITING',
      currentRound: 1,
      players: [],
      gameData: {}
    };

    await this.setState(roomId, state);
    return state;
  }

  static async placeBet(
    roomId: string,
    userId: string,
    betAmount: number,
    betData?: any
  ): Promise<void> {
    const state = await this.getState(roomId);
    if (!state) throw new Error('Game not found');

    if (state.status !== 'BETTING') {
      throw new Error('Betting is not open');
    }

    // Validate bet
    if (!await GameRulesService.validateBet(userId, betAmount)) {
      throw new Error('Insufficient balance');
    }

    // Add or update player bet
    const playerIndex = state.players.findIndex(p => p.id === userId);
    if (playerIndex === -1) {
      state.players.push({
        id: userId,
        username: '', // Will be filled from user service
        betAmount,
        betData,
        status: 'ACTIVE'
      });
    } else {
      state.players[playerIndex].betAmount = betAmount;
      state.players[playerIndex].betData = betData;
      state.players[playerIndex].status = 'ACTIVE';
    }

    await this.setState(roomId, state);
  }

  static async startRound(roomId: string): Promise<void> {
    const state = await this.getState(roomId);
    if (!state) throw new Error('Game not found');

    state.status = 'IN_PROGRESS';
    state.startTime = Date.now();
    state.gameData = {}; // Reset game data

    switch (state.gameType) {
      case 'CRASH':
        state.gameData.crashPoint = GameRulesService.CrashGame.generateCrashPoint();
        break;
      case 'DICE':
        state.gameData.result = GameRulesService.DiceGame.generateResult();
        break;
      case 'ROULETTE':
        state.gameData.result = GameRulesService.RouletteGame.generateResult();
        break;
    }

    await this.setState(roomId, state);
  }

  static async cashOut(roomId: string, userId: string): Promise<number> {
    const state = await this.getState(roomId);
    if (!state) throw new Error('Game not found');

    const player = state.players.find(p => p.id === userId);
    if (!player) throw new Error('Player not found');
    if (player.status !== 'ACTIVE') throw new Error('Invalid player status');

    let payout = 0;
    switch (state.gameType) {
      case 'CRASH':
        const currentMultiplier = state.gameData.currentMultiplier || 1;
        payout = GameRulesService.CrashGame.calculatePayout(
          player.betAmount,
          currentMultiplier
        );
        break;
      // Add other game types
    }

    player.status = 'CASHED_OUT';
    await this.setState(roomId, state);

    return payout;
  }

  static async endRound(roomId: string): Promise<void> {
    const state = await this.getState(roomId);
    if (!state) throw new Error('Game not found');

    state.status = 'COMPLETED';
    state.endTime = Date.now();

    // Calculate results
    const results = this.calculateResults(state);
    await GameRulesService.processBets(roomId, results);

    // Prepare for next round
    state.currentRound++;
    state.status = 'BETTING';
    state.startTime = undefined;
    state.endTime = undefined;
    state.players = state.players.map(p => ({
      ...p,
      betAmount: 0,
      betData: undefined,
      status: 'ACTIVE'
    }));

    await this.setState(roomId, state);

    // Notify players
    for (const player of state.players) {
      const isWinner = results.winners.includes(player.id);
      await NotificationService.create({
        type: 'GAME_RESULT',
        priority: 'HIGH',
        title: isWinner ? 'You Won!' : 'Better Luck Next Time',
        message: `Round ${state.currentRound - 1} ended`,
        metadata: {
          roomId,
          gameType: state.gameType,
          result: isWinner ? 'WIN' : 'LOSS',
          amount: results.payouts[player.id] || -player.betAmount
        }
      });
    }
  }

  private static calculateResults(state: GameState): GameResult {
    const winners: string[] = [];
    const losers: string[] = [];
    const payouts: Record<string, number> = {};

    // Calculate based on game type
    switch (state.gameType) {
      case 'CRASH':
        for (const player of state.players) {
          if (player.status === 'CASHED_OUT') {
            winners.push(player.id);
            payouts[player.id] = GameRulesService.CrashGame.calculatePayout(
              player.betAmount,
              state.gameData.crashPoint
            );
          } else {
            losers.push(player.id);
            payouts[player.id] = -player.betAmount;
          }
        }
        break;
      // Add other game types
    }

    return { winners, losers, payouts };
  }
} 