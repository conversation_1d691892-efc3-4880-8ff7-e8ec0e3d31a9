import { <PERSON>ric<PERSON>ontainer, StartedTestContainer, Wait } from 'testcontainers';
import { PrismaClient } from '@prisma/client';
import { execSync } from 'child_process';
import { Redis } from 'ioredis';

interface ContainerHealthCheck {
  test: string[];
  interval: number;
  timeout: number;
  retries: number;
  startPeriod: number;
}

const POSTGRES_CONFIG = {
  image: 'postgres:14',
  user: 'test_user',
  password: 'test_password',
  database: 'test_db',
  port: 5432
};

const REDIS_CONFIG = {
  image: 'redis:alpine',
  port: 6379
};

let postgresContainer: StartedTestContainer;
let redisContainer: StartedTestContainer;
export let prisma: PrismaClient;
export let redis: Redis;

async function startPostgresContainer(): Promise<StartedTestContainer> {
  const container = await new GenericContainer(POSTGRES_CONFIG.image)
    .withEnvironment({
      POSTGRES_USER: POSTGRES_CONFIG.user,
      POSTGRES_PASSWORD: POSTGRES_CONFIG.password,
      POSTGRES_DB: POSTGRES_CONFIG.database
    })
    .withExposedPorts(POSTGRES_CONFIG.port)
    .withWaitStrategy(
      Wait.forLogMessage('database system is ready to accept connections')
    )
    .start();

  return container;
}

async function startRedisContainer(): Promise<StartedTestContainer> {
  const container = await new GenericContainer(REDIS_CONFIG.image)
    .withExposedPorts(REDIS_CONFIG.port)
    .withWaitStrategy(
      Wait.forLogMessage('Ready to accept connections')
    )
    .start();

  return container;
}

async function initializePrisma(container: StartedTestContainer): Promise<PrismaClient> {
  const port = container.getMappedPort(POSTGRES_CONFIG.port);
  const host = container.getHost();
  const databaseUrl = `postgresql://${POSTGRES_CONFIG.user}:${POSTGRES_CONFIG.password}@${host}:${port}/${POSTGRES_CONFIG.database}`;
  process.env.DATABASE_URL = databaseUrl;

  const client = new PrismaClient({
    datasources: { db: { url: databaseUrl } }
  });

  await client.$connect();
  return client;
}

async function initializeRedis(container: StartedTestContainer): Promise<Redis> {
  const port = container.getMappedPort(REDIS_CONFIG.port);
  const host = container.getHost();
  
  const client = new Redis({
    host,
    port,
    maxRetriesPerRequest: 3,
    retryStrategy: (times) => Math.min(times * 50, 2000)
  });

  await client.ping();
  return client;
}

beforeAll(async () => {
  try {
    console.log('Starting test containers...');
    
    // Start containers
    postgresContainer = await startPostgresContainer();
    console.log('PostgreSQL container started');
    
    redisContainer = await startRedisContainer();
    console.log('Redis container started');

    // Initialize clients
    prisma = await initializePrisma(postgresContainer);
    console.log('Prisma client initialized');
    
    redis = await initializeRedis(redisContainer);
    console.log('Redis client initialized');

    // Run migrations
    console.log('Running database migrations...');
    execSync('npx prisma migrate deploy');
    console.log('Migrations completed');

  } catch (error) {
    console.error('Error setting up test environment:', error);
    // Clean up any resources that were created
    await postgresContainer?.stop().catch(console.error);
    await redisContainer?.stop().catch(console.error);
    throw error;
  }
}, 60000);

afterAll(async () => {
  try {
    console.log('Cleaning up test environment...');
    await Promise.all([
      prisma?.$disconnect(),
      redis?.quit(),
      postgresContainer?.stop(),
      redisContainer?.stop()
    ].map(p => p?.catch(console.error)));
    console.log('Cleanup completed');
  } catch (error) {
    console.error('Error cleaning up test environment:', error);
  }
});

beforeEach(async () => {
  try {
    // Clean up database
    const tables = await prisma.$queryRaw<Array<{ tablename: string }>>`
      SELECT tablename 
      FROM pg_tables 
      WHERE schemaname='public'
    `;

    await Promise.all(
      tables.map(({ tablename }) =>
        prisma.$executeRawUnsafe(`TRUNCATE TABLE "${tablename}" CASCADE`)
      )
    );

    // Clear Redis cache
    await redis.flushall();
  } catch (error) {
    console.error('Error cleaning up before test:', error);
    throw error;
  }
}); 