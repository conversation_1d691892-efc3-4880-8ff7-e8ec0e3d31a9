import twilio from 'twilio';

const client = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

export async function sendWithdrawalNotification(
  phoneNumber: string,
  amount: number,
  currency: string
) {
  try {
    await client.messages.create({
      body: `Your withdrawal of ${amount} ${currency} has been initiated. Please allow up to 24 hours for processing.`,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: phoneNumber
    });
  } catch (error) {
    console.error('Error sending SMS notification:', error);
  }
} 