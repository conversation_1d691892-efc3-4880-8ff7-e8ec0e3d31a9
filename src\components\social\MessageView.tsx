import React from 'react';
import <PERSON><PERSON>eel from './StoryReel';
import { SOUNDS } from '../../constants';
import { playSound } from '../utils/SoundManager';

// Types
interface User {
  name: string;
  iconUrl: string;
}

interface Story {
  id: string;
  user: User;
}

interface ChatItem {
  id: string;
  user: User;
  lastMessage: string;
  timestamp: string;
  unread: boolean;
}

interface MessageViewProps {
  onNavigate: (route: string, params?: any) => void;
}

// Dummy data (will be replaced with real data later)
const storiesData: Story[] = [
  { id: 's2', user: { name: '<PERSON>', iconUrl: 'https://play.rosebud.ai/assets/ASSET%20FACE%202.png?JRmr' } },
  { id: 's8', user: { name: '<PERSON>', iconUrl: 'https://via.placeholder.com/50/888/fff?text=J' } },
  { id: 's3', user: { name: '<PERSON><PERSON>', iconUrl: 'https://play.rosebud.ai/assets/ASSET%20FACE%205.png?dNC6' } },
  { id: 's4', user: { name: '<PERSON>', iconUrl: 'https://play.rosebud.ai/assets/ASSET%20FACE%204.png?i4TK' } },
  { id: 's9', user: { name: 'Liam', iconUrl: 'https://via.placeholder.com/50/222/fff?text=L' } },
  { id: 's10', user: { name: 'Marry', iconUrl: 'https://via.placeholder.com/50/111/fff?text=M' } },
];

const chatListData: ChatItem[] = [
  { id: 'c1', user: { name: 'Sally Rooney', iconUrl: 'https://play.rosebud.ai/assets/ASSET%20FACE%202.png?JRmr' }, lastMessage: 'OMG!!! Yummy 😍', timestamp: '11:00 AM', unread: true },
  { id: 'c2', user: { name: 'Kristin Watson', iconUrl: 'https://via.placeholder.com/50/ddd/000?text=K' }, lastMessage: 'Ad ullamco', timestamp: '9:30 AM', unread: true },
  { id: 'c3', user: { name: 'Liam Pham', iconUrl: 'https://via.placeholder.com/50/222/fff?text=L' }, lastMessage: 'Irure inci', timestamp: 'Sun', unread: false },
  { id: 'c4', user: { name: 'Michael Key', iconUrl: 'https://play.rosebud.ai/assets/ASSET%20FACE%204.png?i4TK' }, lastMessage: 'Aute ullamco', timestamp: 'Sun', unread: false },
  { id: 'c5', user: { name: 'Jena Nguyen', iconUrl: 'https://play.rosebud.ai/assets/ASSET%20FACE%205.png?dNC6' }, lastMessage: 'Nostrud eiusmod', timestamp: '3d ago', unread: false },
  { id: 'c6', user: { name: 'Kiran Glaucus', iconUrl: 'https://via.placeholder.com/50/eee/000?text=KG' }, lastMessage: 'Proident cillum', timestamp: '1w ago', unread: false },
  { id: 'c7', user: { name: 'Marry Liu', iconUrl: 'https://via.placeholder.com/50/111/fff?text=M' }, lastMessage: 'Proident cillum', timestamp: '1w ago', unread: false },
];

const MessageView: React.FC<MessageViewProps> = ({ onNavigate }) => {
  const handleChatClick = (chatId: string) => {
    playSound(SOUNDS.UI_CLICK_MENU_SELECT);
    onNavigate('chat', { chatId });
  };

  return (
    <div className="messages-view">
      <div className="messages-header">
        <span className="messages-title">CHATS</span>
      </div>
      
      <div className="search-bar-container">
        <input 
          type="text" 
          placeholder="🔍 Search" 
          className="chat-search-input" 
        />
      </div>

      {/* Active Users Reel */}
      <StoryReel stories={storiesData} />

      {/* Chat List */}
      <div className="chat-list">
        {chatListData.map(chat => (
          <div 
            key={chat.id} 
            className="chat-list-item"
            onClick={() => handleChatClick(chat.id)}
            style={{ cursor: 'pointer' }}
          >
            <img 
              src={chat.user.iconUrl} 
              alt={chat.user.name} 
              className="chat-item-icon" 
            />
            <div className="chat-item-details">
              <span className="chat-item-name">{chat.user.name}</span>
              <span className="chat-item-last-message">{chat.lastMessage}</span>
            </div>
            <div className="chat-item-info">
              <span className="chat-item-timestamp">{chat.timestamp}</span>
              {chat.unread && <div className="unread-indicator"></div>}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MessageView;
