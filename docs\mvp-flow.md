# LoomLoot MVP Flow

```mermaid
graph TD
    A[Landing Page] --> B{User Choice}
    B -->|Play Free| C[Freeplay Mode]
    B -->|Skip| D[Registration]
    
    C -->|5 Rounds Complete| D
    C -->|Skip Anytime| D
    
    D --> E[Phone Verification]
    E --> F[ID Verification]
    F --> G[Buy Tokens]
    
    G --> H[Main Lobby]
    H --> I{Select Tier}
    
    I -->|Sufficient Balance| J[View Boards]
    I -->|Insufficient Balance| G
    
    J --> K[Join Board]
    K --> L[Play Rounds]
    
    L -->|Win| M{Post-Game}
    L -->|Lose| M
    
    M -->|Play Again| K
    M -->|Cash Out| N[Withdrawal]
    M -->|Buy More| G
    
    N -->|Direct Deposit| O[Bank Account]
    
    subgraph Authentication Flow
        D
        E
        F
    end
    
    subgraph Game Flow
        H
        I
        J
        K
        L
        M
    end
    
    subgraph Payment Flow
        G
        N
        O
    end
```

## Service Interaction Flow

```mermaid
sequenceDiagram
    participant U as User
    participant A as Auth Service
    participant G as Game Service
    participant T as Token Service
    participant B as Board Service
    participant P as Payment Service
    
    U->>A: Register/Login
    A->>U: Auth Token
    
    U->>T: Check Balance
    T->>U: Token Amount
    
    U->>B: Request Available Boards
    B->>T: Check User Balance
    T->>B: Balance Confirmed
    B->>U: Board List (Filtered by Tier)
    
    U->>G: Join Game
    G->>T: Lock Game Tokens
    T->>G: Tokens Locked
    G->>U: Game Started
    
    U->>G: Play Round
    G->>T: Update Balance
    T->>U: New Balance
    
    U->>P: Request Withdrawal
    P->>T: Check Balance
    T->>P: Balance Verified
    P->>U: Payment Initiated
```

## Container Architecture

```mermaid
graph TD
    subgraph Frontend
        UI[User Interface]
        WS[WebSocket Client]
    end
    
    subgraph Backend Services
        Auth[Authentication Service]
        Game[Game Service]
        Token[Token Service]
        Group[Group Service]
        Pay[Payment Service]
    end
    
    subgraph Databases
        UserDB[(User Database)]
        GameDB[(Game Database)]
        TokenDB[(Token Database)]
    end
    
    subgraph Cache
        Redis[Redis Cache]
    end
    
    UI --> Auth
    UI --> Game
    UI --> Token
    UI --> Group
    UI --> Pay
    
    WS --> Game
    
    Auth --> UserDB
    Auth --> Redis
    
    Game --> GameDB
    Game --> Redis
    Game --> Token
    
    Token --> TokenDB
    Token --> Redis
    
    Group --> GameDB
    Group --> TokenDB
    
    Pay --> TokenDB
    Pay --> UserDB
``` 