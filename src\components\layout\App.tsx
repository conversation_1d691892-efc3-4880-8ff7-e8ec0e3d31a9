import React, { useState, useEffect, useCallback } from 'react';
import { SOUNDS, ROUTES, GAME_CONFIG } from '../../constants';
import { initializeGameBoard, updateGameProgression, handleJoinGame } from '../game/GameBoardManager';

// Import all components
import Header from './Header';
import GameInfo from '../game/GameInfo';
import LoomLobbyView from '../lobby/LoomLobbyView';
import LoomLobbyCTA from '../lobby/LoomLobbyCTA';
import BuyTokensPage from '../commerce/BuyTokensPage';
import BuyLoomCoinView from '../commerce/BuyLoomCoinView';
import ProfileView from '../profile/ProfileView';
import SettingsView from '../profile/SettingsView';
import MyLoomsView from '../lobby/MyLoomsView';
import NewsFeed from '../social/NewsFeed';
import ChatDetailView from '../social/ChatDetailView';
import MessageView from '../social/MessageView';
import { playSound } from '../utils/SoundManager';

// Types
type RouteType = typeof ROUTES[keyof typeof ROUTES];

interface Player {
  playerId: string;
  username: string;
  iconUrl: string;
  slotId: string | null;
  backgroundUrl?: string;
}

interface VisualEffect {
  id: string;
  type: 'join' | 'merge' | 'split';
}

const App: React.FC = () => {
  // Navigation state
  const [currentView, setCurrentView] = useState<RouteType>(ROUTES.HOME);
  
  // Game state
  const [players, setPlayers] = useState<Player[]>([]);
  const [tokenBalance, setTokenBalance] = useState<number>(100);
  const [timeLeft, setTimeLeft] = useState<number>(30);
  const [isWinSequenceActive, setIsWinSequenceActive] = useState<boolean>(false);
  const [visualEffects, setVisualEffects] = useState<VisualEffect[]>([]);
  
  // UI state
  const [isSoundEnabled, setIsSoundEnabled] = useState<boolean>(true);
  const [selectedChatId, setSelectedChatId] = useState<string | null>(null);

  // Navigation handler
  const handleNavigate = useCallback((route: RouteType, params?: any) => {
    if (route === ROUTES.CHAT && params?.chatId) {
      setSelectedChatId(params.chatId);
    }
    setCurrentView(route);
  }, []);

  // Sound toggle
  const handleToggleSound = useCallback(() => {
    setIsSoundEnabled(prev => !prev);
    if (isSoundEnabled) {
      playSound(SOUNDS.UI_CLICK_BUTTON_01);
    }
  }, [isSoundEnabled]);

  // Game timer effect
  useEffect(() => {
    if (currentView === ROUTES.PLAY && !isWinSequenceActive) {
      const timer = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            // Timer reached 0, trigger game progression
            updateGameProgression();
            return 30; // Reset timer
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [currentView, isWinSequenceActive]);

  // Initialize game board manager
  useEffect(() => {
    initializeGameBoard({
      initialPlayersData: players,
      initialCurrentView: currentView,
      initialTokenBalance: tokenBalance,
      playersSetter: setPlayers,
      tokenBalanceSetter: setTokenBalance,
      timeLeftSetter: setTimeLeft,
      winSequenceSetter: setIsWinSequenceActiveState,
      visualEffectsSetter: setVisualEffects,
      currentViewSetter: setCurrentView,
      soundPlayer: isSoundEnabled ? playSound : () => {},
    });
  }, []);

  // Handle join game
  const handleJoinGameClick = useCallback(() => {
    if (tokenBalance >= 100) { // Assuming 100 tokens required
      setTokenBalance(prev => prev - 100);
      handleJoinGame();
    }
  }, [tokenBalance]);

  // Render current view
  const renderCurrentView = () => {
    switch (currentView) {
      case ROUTES.PLAY:
        return (
          <div className="main-content">
            <GameInfo timeLeft={timeLeft} />
            {/* Game board will be rendered here */}
            <div className="board-container">
              {/* Board rendering logic will go here */}
            </div>
            <div className="action-buttons-container">
              <button 
                className="action-button"
                onClick={handleJoinGameClick}
                disabled={tokenBalance < 100}
              >
                Join Game (100 Tokens)
              </button>
            </div>
          </div>
        );
      
      case ROUTES.LOOM_LOBBY:
        return <LoomLobbyView onNavigate={handleNavigate} />;
      
      case ROUTES.BUY_TOKENS:
        return <BuyTokensPage onNavigate={handleNavigate} />;
      
      case ROUTES.BUY_LOOMCOIN:
        return <BuyLoomCoinView onNavigate={handleNavigate} />;
      
      case ROUTES.PROFILE:
        return <ProfileView onNavigate={handleNavigate} />;
      
      case ROUTES.SETTINGS:
        return <SettingsView onNavigate={handleNavigate} />;
      
      case ROUTES.MY_LOOMS:
        return <MyLoomsView onNavigate={handleNavigate} />;
      
      case ROUTES.CHAT:
        if (selectedChatId) {
          return (
            <ChatDetailView 
              chatId={selectedChatId}
              onBack={() => handleNavigate(ROUTES.HOME)}
            />
          );
        }
        return <MessageView onNavigate={handleNavigate} />;
      
      case ROUTES.STORIES:
        return <NewsFeed onNavigate={handleNavigate} />;
      
      default:
        return (
          <div className="main-content">
            <div className="placeholder-view">
              <h2 className="placeholder-title">Welcome to Loom Loot</h2>
              <p className="placeholder-text">Select a game mode to begin!</p>
            </div>
            <LoomLobbyCTA onNavigate={handleNavigate} />
          </div>
        );
    }
  };

  return (
    <div className="app-container">
      <Header 
        isSoundEnabled={isSoundEnabled}
        onToggleSound={handleToggleSound}
        tokenBalance={tokenBalance}
        onNavigate={handleNavigate}
      />
      
      {renderCurrentView()}
      
      {/* Bottom Navigation */}
      <div className="bottom-nav-bar">
        <button 
          className={`nav-button ${currentView === ROUTES.HOME ? 'active' : ''}`}
          onClick={() => handleNavigate(ROUTES.HOME)}
        >
          <img src="https://play.rosebud.ai/assets/home-icon.png" alt="Home" className="nav-icon" />
          Home
        </button>
        
        <button 
          className={`nav-button ${currentView === ROUTES.LOOM_LOBBY ? 'active' : ''}`}
          onClick={() => handleNavigate(ROUTES.LOOM_LOBBY)}
        >
          <img src="https://play.rosebud.ai/assets/lobby-icon.png" alt="Lobby" className="nav-icon" />
          Lobby
        </button>
        
        <button 
          className={`nav-button ${currentView === ROUTES.STORIES ? 'active' : ''}`}
          onClick={() => handleNavigate(ROUTES.STORIES)}
        >
          <img src="https://play.rosebud.ai/assets/news-icon.png" alt="News" className="nav-icon" />
          News
        </button>
        
        <button 
          className={`nav-button ${currentView === ROUTES.CHAT ? 'active' : ''}`}
          onClick={() => handleNavigate(ROUTES.CHAT)}
        >
          <img src="https://play.rosebud.ai/assets/chat-icon.png" alt="Chat" className="nav-icon" />
          Chat
        </button>
        
        <button 
          className={`nav-button ${currentView === ROUTES.MY_LOOMS ? 'active' : ''}`}
          onClick={() => handleNavigate(ROUTES.MY_LOOMS)}
        >
          <img src="https://play.rosebud.ai/assets/my-looms-icon.png" alt="My Looms" className="nav-icon" />
          My Looms
        </button>
      </div>
      
      {/* Winner Banner */}
      {isWinSequenceActive && (
        <div className="winner-banner">
          🎉 WINNER! 🎉
        </div>
      )}
    </div>
  );
};

export default App;
