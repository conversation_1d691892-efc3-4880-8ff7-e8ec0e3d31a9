import { getRedisClient } from '../utils/redis';
import { LoomGroupManager } from './loomGroupManager';
import { 
  LoomDebitCard, 
  CreditBuilderTransaction, 
  CreditReport,
  CurrencyBalance,
  CurrencyConversion,
  SecurityLog,
  SecuritySettings
} from '../types/loom';
import crypto from 'crypto';
import geoip from 'geoip-lite';
import UAParser from 'ua-parser-js';

interface PaymentMethod {
  id: string;
  userId: string;
  type: 'DEBIT_CARD' | 'DIRECT_DEPOSIT';
  status: 'ACTIVE' | 'PENDING' | 'FAILED' | 'VERIFIED';
  lastUsed?: number;
  lastVerified?: number;
  details: {
    // For debit card
    last4?: string;
    expiryMonth?: string;
    expiryYear?: string;
    brand?: string;
    // For direct deposit
    bankName?: string;
    accountLast4?: string;
    routingNumber?: string;
    accountType?: 'CHECKING' | 'SAVINGS';
  };
}

interface WalletTransaction {
  id: string;
  userId: string;
  type: 'BUY' | 'SELL' | 'GROUP_ENTRY' | 'RAFFLE_ENTRY' | 'REWARD' | 'CASHOUT';
  amount: number;
  tokenAmount: number;
  timestamp: Date;
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  cashValue?: number;
  profitMargin?: number;
  paymentMethodId?: string;
}

interface UserWallet {
  userId: string;
  tokenBalance: number;
  totalBought: number;
  totalSold: number;
  lastActivityTimestamp: number;
  activeGroupId?: string;
  paymentMethods: PaymentMethod[];
}

export class LoomWalletManager {
  private static readonly TOKEN_BUY_PRICE = 1; // $1 per token
  private static readonly TOKEN_SELL_PRICE = 0.5; // $0.50 per token (2 tokens = $1)
  
  private static readonly SECURITY_SETTINGS_KEY = 'security_settings';
  private static readonly SECURITY_LOGS_KEY = 'security_logs';
  private static readonly CURRENCY_BALANCES_KEY = 'currency_balances';
  private static readonly CURRENCY_CONVERSIONS_KEY = 'currency_conversions';
  private static readonly MAX_FAILED_ATTEMPTS = 5;
  private static readonly SUSPICIOUS_ACTIVITY_THRESHOLD = 0.8;
  
  static async getWallet(userId: string): Promise<UserWallet> {
    const redis = await getRedisClient();
    const [wallet, paymentMethods] = await Promise.all([
      redis.hgetall(`wallet:${userId}`),
      this.getPaymentMethods(userId)
    ]);

    return wallet ? {
      userId,
      tokenBalance: parseInt(wallet.tokenBalance) || 0,
      totalBought: parseInt(wallet.totalBought) || 0,
      totalSold: parseInt(wallet.totalSold) || 0,
      lastActivityTimestamp: parseInt(wallet.lastActivityTimestamp) || 0,
      activeGroupId: wallet.activeGroupId,
      paymentMethods
    } : {
      userId,
      tokenBalance: 0,
      totalBought: 0,
      totalSold: 0,
      lastActivityTimestamp: 0,
      paymentMethods: []
    };
  }

  static async addPaymentMethod(
    userId: string,
    type: PaymentMethod['type'],
    details: PaymentMethod['details']
  ): Promise<PaymentMethod> {
    const redis = await getRedisClient();
    const paymentMethod: PaymentMethod = {
      id: `pm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      type,
      status: 'PENDING',
      details
    };

    await redis.hset(
      `payment_methods:${userId}`,
      paymentMethod.id,
      JSON.stringify(paymentMethod)
    );

    return paymentMethod;
  }

  static async verifyPaymentMethod(
    userId: string,
    paymentMethodId: string
  ): Promise<PaymentMethod> {
    const redis = await getRedisClient();
    const paymentMethod = await this.getPaymentMethod(userId, paymentMethodId);
    if (!paymentMethod) {
      throw new Error('Payment method not found');
    }

    paymentMethod.status = 'VERIFIED';
    paymentMethod.lastVerified = Date.now();

    await redis.hset(
      `payment_methods:${userId}`,
      paymentMethodId,
      JSON.stringify(paymentMethod)
    );

    return paymentMethod;
  }

  static async getPaymentMethods(userId: string): Promise<PaymentMethod[]> {
    const redis = await getRedisClient();
    const methods = await redis.hgetall(`payment_methods:${userId}`);
    return Object.values(methods).map(m => JSON.parse(m as string));
  }

  static async getPaymentMethod(
    userId: string,
    paymentMethodId: string
  ): Promise<PaymentMethod | null> {
    const redis = await getRedisClient();
    const method = await redis.hget(`payment_methods:${userId}`, paymentMethodId);
    return method ? JSON.parse(method) : null;
  }

  static async buyTokens(
    userId: string,
    amount: number,
    paymentMethodId: string
  ): Promise<WalletTransaction> {
    const redis = await getRedisClient();
    const [wallet, paymentMethod] = await Promise.all([
      this.getWallet(userId),
      this.getPaymentMethod(userId, paymentMethodId)
    ]);

    if (!paymentMethod || paymentMethod.status !== 'VERIFIED') {
      throw new Error('Invalid or unverified payment method');
    }

    const tokenAmount = amount; // $1 = 1 token

    const transaction: WalletTransaction = {
      id: `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      type: 'BUY',
      amount,
      tokenAmount,
      timestamp: new Date(),
      status: 'COMPLETED',
      paymentMethodId
    };

    // Update wallet
    await redis.hset(`wallet:${userId}`, {
      tokenBalance: wallet.tokenBalance + tokenAmount,
      totalBought: wallet.totalBought + tokenAmount,
      lastActivityTimestamp: Date.now()
    });

    // Store transaction
    await redis.set(`transaction:${transaction.id}`, JSON.stringify(transaction));
    await redis.zadd(`user:${userId}:transactions`, Date.now(), transaction.id);

    return transaction;
  }

  static async cashoutTokens(
    userId: string,
    tokenAmount: number,
    paymentMethodId: string
  ): Promise<WalletTransaction> {
    const redis = await getRedisClient();
    const [wallet, paymentMethod] = await Promise.all([
      this.getWallet(userId),
      this.getPaymentMethod(userId, paymentMethodId)
    ]);
    
    // Verify user is in active group
    if (!wallet.activeGroupId) {
      throw new Error('Must be in active Loom group to cash out tokens');
    }

    const group = await LoomGroupManager.getGroup(wallet.activeGroupId);
    if (!group?.isActive) {
      throw new Error('Must be in active Loom group to cash out tokens');
    }

    if (!paymentMethod || paymentMethod.status !== 'VERIFIED') {
      throw new Error('Invalid or unverified payment method');
    }

    // Check minimum balance requirement
    const minBalance = group.entryFee;
    if (wallet.tokenBalance - tokenAmount < minBalance) {
      throw new Error(`Must maintain minimum balance of ${minBalance} tokens`);
    }

    // Calculate cash value (2 tokens = $1)
    const cashValue = (tokenAmount / 2) * this.TOKEN_SELL_PRICE * 2;
    const profitMargin = tokenAmount * (this.TOKEN_BUY_PRICE - this.TOKEN_SELL_PRICE);

    const transaction: WalletTransaction = {
      id: `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      type: 'CASHOUT',
      amount: cashValue,
      tokenAmount,
      timestamp: new Date(),
      status: 'PENDING',
      cashValue,
      profitMargin,
      paymentMethodId
    };

    // Update wallet
    await redis.hset(`wallet:${userId}`, {
      tokenBalance: wallet.tokenBalance - tokenAmount,
      totalSold: wallet.totalSold + tokenAmount,
      lastActivityTimestamp: Date.now()
    });

    // Store transaction and profit data
    await redis.set(`transaction:${transaction.id}`, JSON.stringify(transaction));
    await redis.zadd(`user:${userId}:transactions`, Date.now(), transaction.id);
    await redis.zadd('admin:profits', profitMargin, transaction.id);
    
    // Queue cashout for processing
    await redis.rpush('cashout:pending', transaction.id);

    return transaction;
  }

  static async completeCashout(transactionId: string): Promise<void> {
    const redis = await getRedisClient();
    const txData = await redis.get(`transaction:${transactionId}`);
    if (!txData) {
      throw new Error('Transaction not found');
    }

    const transaction: WalletTransaction = JSON.parse(txData);
    if (transaction.type !== 'CASHOUT' || transaction.status !== 'PENDING') {
      throw new Error('Invalid transaction state');
    }

    // Process the cashout to the payment method
    // This would integrate with your payment processor (Stripe, etc.)
    // For now, we'll just mark it as completed
    transaction.status = 'COMPLETED';
    await redis.set(`transaction:${transaction.id}`, JSON.stringify(transaction));
  }

  static async getAdminProfits(
    startDate?: Date,
    endDate?: Date
  ): Promise<{ total: number; transactions: WalletTransaction[] }> {
    const redis = await getRedisClient();
    const start = startDate ? startDate.getTime() : 0;
    const end = endDate ? endDate.getTime() : Date.now();

    const txIds = await redis.zrangebyscore('admin:profits', start, end);
    const transactions = await Promise.all(
      txIds.map(async (id) => {
        const tx = await redis.get(`transaction:${id}`);
        return tx ? JSON.parse(tx) : null;
      })
    );

    const validTransactions = transactions.filter((tx): tx is WalletTransaction => tx !== null);
    const total = validTransactions.reduce((sum, tx) => sum + (tx.profitMargin || 0), 0);

    return { total, transactions: validTransactions };
  }

  static async requestLoomDebitCard(
    userId: string,
    enableCreditBuilder: boolean = false
  ): Promise<LoomDebitCard> {
    const redis = await getRedisClient();
    const wallet = await this.getWallet(userId);

    // Verify user eligibility (e.g., minimum token balance, activity history)
    if (wallet.tokenBalance < 100) { // Example minimum requirement
      throw new Error('Insufficient token balance for LoomLoot debit card');
    }

    const card: LoomDebitCard = {
      id: `card_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      cardNumber: '', // To be assigned by bank
      expiryMonth: '', // To be assigned by bank
      expiryYear: '', // To be assigned by bank
      status: 'PENDING',
      creditBuilderEnabled: enableCreditBuilder,
      monthlySpending: 0,
      monthlyLimit: 1000, // Default limit
      lastReportedDate: 0
    };

    await redis.hset(`loom_cards:${userId}`, card.id, JSON.stringify(card));
    return card;
  }

  static async getLoomDebitCard(
    userId: string,
    cardId: string
  ): Promise<LoomDebitCard | null> {
    const redis = await getRedisClient();
    const cardData = await redis.hget(`loom_cards:${userId}`, cardId);
    return cardData ? JSON.parse(cardData) : null;
  }

  static async getUserLoomDebitCards(userId: string): Promise<LoomDebitCard[]> {
    const redis = await getRedisClient();
    const cards = await redis.hgetall(`loom_cards:${userId}`);
    return Object.values(cards).map(card => JSON.parse(card));
  }

  static async recordCreditBuilderTransaction(
    userId: string,
    cardId: string,
    amount: number,
    merchantName: string
  ): Promise<CreditBuilderTransaction> {
    const redis = await getRedisClient();
    const card = await this.getLoomDebitCard(userId, cardId);
    
    if (!card || !card.creditBuilderEnabled) {
      throw new Error('Credit builder not enabled for this card');
    }

    const transaction: CreditBuilderTransaction = {
      id: `cbt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      cardId,
      amount,
      merchantName,
      timestamp: Date.now(),
      reportedToBureau: false
    };

    // Update card monthly spending
    card.monthlySpending += amount;
    await redis.hset(`loom_cards:${userId}`, cardId, JSON.stringify(card));

    // Store transaction
    await redis.set(
      `credit_transaction:${transaction.id}`,
      JSON.stringify(transaction)
    );
    await redis.zadd(
      `user:${userId}:credit_transactions`,
      Date.now(),
      transaction.id
    );

    return transaction;
  }

  static async generateMonthlyCreditReport(
    userId: string,
    month: number,
    year: number
  ): Promise<CreditReport> {
    const redis = await getRedisClient();
    const startOfMonth = new Date(year, month - 1, 1).getTime();
    const endOfMonth = new Date(year, month, 0).getTime();

    // Get all transactions for the month
    const transactions = await redis.zrangebyscore(
      `user:${userId}:credit_transactions`,
      startOfMonth,
      endOfMonth
    );

    const transactionData = await Promise.all(
      transactions.map(async (id) => {
        const tx = await redis.get(`credit_transaction:${id}`);
        return tx ? JSON.parse(tx) : null;
      })
    );

    const validTransactions = transactionData.filter(
      (tx): tx is CreditBuilderTransaction => tx !== null
    );

    const totalSpent = validTransactions.reduce((sum, tx) => sum + tx.amount, 0);

    const report: CreditReport = {
      userId,
      month,
      year,
      totalSpent,
      paymentsMade: totalSpent, // In credit builder, all spending is matched with payments
      reportedDate: Date.now(),
      status: 'PENDING'
    };

    // Store report
    const reportId = `${userId}_${year}_${month}`;
    await redis.set(`credit_report:${reportId}`, JSON.stringify(report));

    // Queue report for submission to credit bureaus
    await redis.rpush('credit_reports:pending', reportId);

    return report;
  }

  static async getUserCurrencyBalances(userId: string): Promise<CurrencyBalance[]> {
    const redis = await getRedisClient();
    const balances = await redis.hgetall(`${this.CURRENCY_BALANCES_KEY}:${userId}`);
    return Object.values(balances).map(b => JSON.parse(b));
  }

  static async convertCurrency(
    userId: string,
    fromCurrency: string,
    toCurrency: string,
    amount: number,
    ipAddress: string,
    userAgent: string
  ): Promise<CurrencyConversion> {
    const redis = await getRedisClient();

    // Get user's security settings
    const settings = await this.getSecuritySettings(userId);
    
    // Get location from IP
    const geoData = geoip.lookup(ipAddress);
    if (!geoData) {
      throw new Error('Unable to verify location');
    }

    // Verify user's location and check for suspicious activity
    const riskScore = await this.calculateRiskScore(userId, ipAddress, userAgent);
    if (riskScore > settings.suspiciousActivityThreshold) {
      await this.logSecurityEvent(userId, 'SUSPICIOUS_ACTIVITY', {
        ipAddress,
        userAgent,
        details: 'High risk currency conversion attempt',
        status: 'BLOCKED',
        riskScore
      });
      throw new Error('Transaction blocked due to suspicious activity');
    }

    // Get current exchange rate (you would integrate with a forex API here)
    const rate = await this.getExchangeRate(fromCurrency, toCurrency);
    
    // Calculate converted amount
    const convertedAmount = amount * rate;

    // Check if user has sufficient balance
    const balances = await this.getUserCurrencyBalances(userId);
    const fromBalance = balances.find(b => b.currency === fromCurrency);
    if (!fromBalance || fromBalance.balance < amount) {
      throw new Error('Insufficient balance');
    }

    // Create conversion record
    const conversion: CurrencyConversion = {
      id: `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      fromCurrency,
      toCurrency,
      fromAmount: amount,
      toAmount: convertedAmount,
      rate,
      timestamp: Date.now(),
      status: 'PENDING',
      ipAddress,
      location: {
        country: geoData.country,
        city: geoData.city,
        coordinates: {
          latitude: geoData.ll[0],
          longitude: geoData.ll[1]
        }
      }
    };

    // Update balances
    await Promise.all([
      redis.hset(
        `${this.CURRENCY_BALANCES_KEY}:${userId}`,
        fromCurrency,
        JSON.stringify({
          currency: fromCurrency,
          balance: fromBalance.balance - amount,
          lastUpdated: Date.now()
        })
      ),
      redis.hincrby(
        `${this.CURRENCY_BALANCES_KEY}:${userId}`,
        toCurrency,
        convertedAmount
      )
    ]);

    // Store conversion record
    await redis.set(
      `${this.CURRENCY_CONVERSIONS_KEY}:${conversion.id}`,
      JSON.stringify(conversion)
    );

    // Log the event
    await this.logSecurityEvent(userId, 'CURRENCY_CONVERSION', {
      ipAddress,
      userAgent,
      details: `Converted ${amount} ${fromCurrency} to ${convertedAmount} ${toCurrency}`,
      status: 'SUCCESS',
      riskScore
    });

    return conversion;
  }

  static async getSecuritySettings(userId: string): Promise<SecuritySettings> {
    const redis = await getRedisClient();
    const settings = await redis.get(`${this.SECURITY_SETTINGS_KEY}:${userId}`);
    
    if (!settings) {
      // Create default security settings
      const defaultSettings: SecuritySettings = {
        userId,
        twoFactorEnabled: false,
        twoFactorMethod: '2FA_APP',
        loginNotifications: true,
        transactionNotifications: true,
        locationBasedAuth: true,
        trustedDevices: [],
        maxDevices: 5,
        passwordLastChanged: Date.now(),
        securityQuestions: [],
        ipWhitelist: [],
        suspiciousActivityThreshold: 0.8
      };

      await redis.set(
        `${this.SECURITY_SETTINGS_KEY}:${userId}`,
        JSON.stringify(defaultSettings)
      );

      return defaultSettings;
    }

    return JSON.parse(settings);
  }

  static async logSecurityEvent(
    userId: string,
    eventType: SecurityLog['eventType'],
    {
      ipAddress,
      userAgent,
      details,
      status,
      riskScore
    }: {
      ipAddress: string;
      userAgent: string;
      details: string;
      status: SecurityLog['status'];
      riskScore: number;
    }
  ): Promise<void> {
    const redis = await getRedisClient();
    const geoData = geoip.lookup(ipAddress);
    const uaParser = new UAParser(userAgent);
    const deviceInfo = uaParser.getResult();

    const log: SecurityLog = {
      id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      eventType,
      timestamp: Date.now(),
      ipAddress,
      deviceInfo: {
        type: deviceInfo.device.type || 'unknown',
        browser: deviceInfo.browser.name || 'unknown',
        os: deviceInfo.os.name || 'unknown',
        deviceId: this.generateDeviceId(userAgent, ipAddress)
      },
      location: geoData ? {
        country: geoData.country,
        city: geoData.city,
        coordinates: {
          latitude: geoData.ll[0],
          longitude: geoData.ll[1]
        }
      } : {
        country: 'unknown',
        city: 'unknown',
        coordinates: { latitude: 0, longitude: 0 }
      },
      status,
      details,
      riskScore
    };

    await redis.zadd(
      `${this.SECURITY_LOGS_KEY}:${userId}`,
      log.timestamp,
      JSON.stringify(log)
    );
  }

  private static async calculateRiskScore(
    userId: string,
    ipAddress: string,
    userAgent: string
  ): Promise<number> {
    const redis = await getRedisClient();
    const settings = await this.getSecuritySettings(userId);
    let riskScore = 0;

    // Check if IP is whitelisted
    if (settings.ipWhitelist.includes(ipAddress)) {
      return 0;
    }

    // Get recent security logs
    const recentLogs = await redis.zrangebyscore(
      `${this.SECURITY_LOGS_KEY}:${userId}`,
      Date.now() - 24 * 60 * 60 * 1000, // Last 24 hours
      Date.now()
    );

    const logs: SecurityLog[] = recentLogs.map(log => JSON.parse(log));

    // Check for failed attempts
    const recentFailures = logs.filter(
      log => log.status === 'FAILED' && log.ipAddress === ipAddress
    );
    if (recentFailures.length >= this.MAX_FAILED_ATTEMPTS) {
      return 1;
    }

    // Check for location change
    const geoData = geoip.lookup(ipAddress);
    const lastLocation = logs
      .filter(log => log.status === 'SUCCESS')
      .sort((a, b) => b.timestamp - a.timestamp)[0]?.location;

    if (lastLocation && geoData && lastLocation.country !== geoData.country) {
      riskScore += 0.4;
    }

    // Check for new device
    const deviceId = this.generateDeviceId(userAgent, ipAddress);
    const isKnownDevice = settings.trustedDevices.some(d => d.deviceId === deviceId);
    if (!isKnownDevice) {
      riskScore += 0.3;
    }

    // Check for suspicious timing
    const lastActivity = logs
      .filter(log => log.status === 'SUCCESS')
      .sort((a, b) => b.timestamp - a.timestamp)[0]?.timestamp;

    if (lastActivity && Date.now() - lastActivity < 1000) { // Less than 1 second
      riskScore += 0.2;
    }

    return Math.min(riskScore, 1);
  }

  private static generateDeviceId(userAgent: string, ipAddress: string): string {
    return crypto
      .createHash('sha256')
      .update(`${userAgent}${ipAddress}`)
      .digest('hex');
  }

  private static async getExchangeRate(
    fromCurrency: string,
    toCurrency: string
  ): Promise<number> {
    // Integrate with a forex API here
    // For now, return a mock rate
    return 1.2;
  }

  static async lockCard(
    userId: string,
    cardId: string
  ): Promise<LoomDebitCard> {
    const redis = await getRedisClient();
    const card = await this.getLoomDebitCard(userId, cardId);
    
    if (!card) {
      throw new Error('Card not found');
    }

    if (card.status === 'BLOCKED') {
      throw new Error('Card is already locked');
    }

    // Store the previous status to restore it when unlocking
    const previousStatus = card.status;
    card.status = 'BLOCKED';
    card.lastUpdated = Date.now();

    await redis.hset(
      `loom_cards:${userId}`,
      cardId,
      JSON.stringify({ ...card, previousStatus })
    );

    // Log security event
    await this.logSecurityEvent(
      userId,
      'CARD_LOCK',
      {
        ipAddress: '',
        userAgent: '',
        details: `Card ${cardId} locked by user`,
        status: 'SUCCESS',
        riskScore: 0
      }
    );

    return card;
  }

  static async unlockCard(
    userId: string,
    cardId: string
  ): Promise<LoomDebitCard> {
    const redis = await getRedisClient();
    const cardData = await redis.hget(`loom_cards:${userId}`, cardId);
    
    if (!cardData) {
      throw new Error('Card not found');
    }

    const card = JSON.parse(cardData);
    if (card.status !== 'BLOCKED') {
      throw new Error('Card is not locked');
    }

    // Restore previous status or set to ACTIVE if not available
    card.status = card.previousStatus || 'ACTIVE';
    delete card.previousStatus;
    card.lastUpdated = Date.now();

    await redis.hset(
      `loom_cards:${userId}`,
      cardId,
      JSON.stringify(card)
    );

    // Log security event
    await this.logSecurityEvent(
      userId,
      'CARD_UNLOCK',
      {
        ipAddress: '',
        userAgent: '',
        details: `Card ${cardId} unlocked by user`,
        status: 'SUCCESS',
        riskScore: 0
      }
    );

    return card;
  }

  static async isCardLocked(
    userId: string,
    cardId: string
  ): Promise<boolean> {
    const card = await this.getLoomDebitCard(userId, cardId);
    return card?.status === 'BLOCKED';
  }
} 