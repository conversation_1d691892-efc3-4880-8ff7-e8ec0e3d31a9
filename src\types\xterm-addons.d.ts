declare module 'xterm-addon-webgl' {
  import { Terminal, ITerminalAddon } from 'xterm';

  export class WebglAddon implements ITerminalAddon {
    constructor();
    activate(terminal: Terminal): void;
    dispose(): void;
  }
}

declare module 'xterm-addon-search' {
  import { Terminal, ITerminalAddon } from 'xterm';

  export class SearchAddon implements ITerminalAddon {
    constructor();
    activate(terminal: Terminal): void;
    dispose(): void;
    findNext(term: string, searchOptions?: { regex?: boolean; wholeWord?: boolean; caseSensitive?: boolean }): boolean;
    findPrevious(term: string, searchOptions?: { regex?: boolean; wholeWord?: boolean; caseSensitive?: boolean }): boolean;
  }
} 