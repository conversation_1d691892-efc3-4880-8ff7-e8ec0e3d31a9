{"scripts": {"test": "jest", "inspect": "npx @web-applets/inspector .", "dev": "vite", "build": "vite build"}, "dependencies": {"@web-applets/sdk": "latest"}, "devDependencies": {"jest": "^29.7.0", "vite": "^6.2.2"}, "name": "mcp_server_project", "version": "1.0.0", "description": "", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/unternet-co/web-applet-template-ts-vite.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/unternet-co/web-applet-template-ts-vite/issues"}, "homepage": "https://github.com/unternet-co/web-applet-template-ts-vite#readme"}