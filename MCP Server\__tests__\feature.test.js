const { describe, test, expect } = require('@jest/globals');

describe('Basic Test Suite', () => {
  test('basic functionality', () => {
    expect(true).toBe(true);
  });

  test('string operations', () => {
    const str = 'Hello, World!';
    expect(str).toContain('Hello');
    expect(str).toHaveLength(13);
  });

  test('async operations', async () => {
    const result = await Promise.resolve(42);
    expect(result).toBe(42);
  });
}); 