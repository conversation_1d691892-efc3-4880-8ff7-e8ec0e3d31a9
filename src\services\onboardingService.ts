import { User, OnboardingState } from '../types/user';

export const ONBOARDING_STEPS = {
  WELCOME: 'welcome',
  LOOM_GROUPS: 'loom_groups',
  TOKENS: 'tokens',
  LOOM_COIN: 'loom_coin',
  WALLET: 'wallet',
  GAME_ROOMS: 'game_rooms',
  COMPLETE: 'complete'
} as const;

export class OnboardingService {
  static async updateProgress(userId: string, step: string): Promise<void> {
    try {
      await fetch('/api/onboarding/progress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, step })
      });
    } catch (error) {
      console.error('Failed to update onboarding progress:', error);
    }
  }

  static async completeOnboarding(userId: string): Promise<void> {
    try {
      await fetch('/api/onboarding/complete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId })
      });
    } catch (error) {
      console.error('Failed to complete onboarding:', error);
    }
  }

  static shouldShowOnboarding(user: User): boolean {
    return !user.onboarding?.completed && !user.onboarding?.hasRevisited;
  }
} 