# Application
NODE_ENV=production
PORT=3000

# Database
DB_USER=your_production_db_user
DB_PASSWORD=your_production_db_password
DB_NAME=loomloot_prod
DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@db:5432/${DB_NAME}

# Redis
REDIS_URL=redis://redis:6379

# Authentication
NEXTAUTH_URL=https://loomloot.com
NEXTAUTH_SECRET=your_nextauth_secret

# Bitcoin Integration
BTC_NETWORK=mainnet
BTC_API_KEY=your_bitcoin_api_key

# AI Integration
ANTHROPIC_API_KEY=************************************************************************************************************

# Docker
DOCKER_HUB_USERNAME=your_dockerhub_username

# Domain
DOMAIN=loomloot.com

# SSL Certificate paths (for Nginx)
SSL_CERTIFICATE_PATH=/etc/nginx/ssl/fullchain.pem
SSL_CERTIFICATE_KEY_PATH=/etc/nginx/ssl/privkey.pem 