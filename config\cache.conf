# Cache Settings
fastcgi_cache_path /tmp/nginx_cache levels=1:2 keys_zone=LOOMLOOT:100m inactive=60m;
fastcgi_cache_key "$scheme$request_method$host$request_uri";
fastcgi_cache_use_stale error timeout invalid_header http_500;
fastcgi_cache_valid 200 60m;
fastcgi_cache_valid 404 1m;

# Browser Caching
location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|woff|woff2|ttf|svg|eot)$ {
    expires 30d;
    add_header Cache-Control "public, no-transform";
}

location ~* \.(html|htm)$ {
    expires 1h;
    add_header Cache-Control "public, no-transform";
}

# Gzip Compression
gzip on;
gzip_vary on;
gzip_proxied any;
gzip_comp_level 6;
gzip_types text/plain text/css text/xml application/json application/javascript application/xml+rss application/atom+xml image/svg+xml;

# Microcache
fastcgi_cache_bypass $http_pragma;
fastcgi_cache_revalidate on;
add_header X-Cache-Status $upstream_cache_status;

# Don't cache authenticated requests
map $http_cookie $no_cache {
    default 0;
    ~SESS 1;
    ~wordpress_logged_in 1;
}

# Cache Bypass
set $skip_cache 0;

if ($request_method = POST) {
    set $skip_cache 1;
}

if ($query_string != "") {
    set $skip_cache 1;
}

if ($http_cookie ~* "comment_author|wordpress_[a-f0-9]+|wp-postpass|wordpress_logged_in") {
    set $skip_cache 1;
}

if ($request_uri ~* "/wp-admin/|/xmlrpc.php|wp-.*.php|/feed/|index.php|sitemap(_index)?.xml") {
    set $skip_cache 1;
}

# Cache Settings for API
location /api/ {
    proxy_cache LOOMLOOT;
    proxy_cache_use_stale error timeout http_500 http_502 http_503 http_504;
    proxy_cache_valid 200 1m;
    proxy_cache_valid 404 1m;
    proxy_cache_bypass $http_pragma;
    add_header X-Cache-Status $upstream_cache_status;
    
    # Don't cache if conditions are met
    proxy_cache_bypass $skip_cache;
    proxy_no_cache $skip_cache;
} 