#!/bin/bash

# Set colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Function to display usage
usage() {
    echo "Usage: $0 [command]"
    echo
    echo "Commands:"
    echo "  start-dev     Start development environment"
    echo "  start-prod    Start production environment"
    echo "  stop          Stop all containers"
    echo "  restart       Restart all containers"
    echo "  logs          Show container logs"
    echo "  status        Show container status"
    echo "  clean         Clean up unused Docker resources"
    echo "  build         Build Docker images"
    echo "  prune         Remove all unused containers, networks, images"
    echo "  help          Show this help message"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}Error: Docker is not running${NC}"
        exit 1
    fi
}

# Start development environment
start_dev() {
    echo -e "${YELLOW}Starting development environment...${NC}"
    docker-compose -f docker-compose.dev.yml up -d
    echo -e "${GREEN}Development environment started${NC}"
    echo "Access the application at http://localhost:3000"
    echo "Access Adminer at http://localhost:8080"
    echo "Access Redis Commander at http://localhost:8081"
}

# Start production environment
start_prod() {
    echo -e "${YELLOW}Starting production environment...${NC}"
    docker-compose -f docker-compose.prod.yml up -d
    echo -e "${GREEN}Production environment started${NC}"
}

# Stop containers
stop() {
    echo -e "${YELLOW}Stopping containers...${NC}"
    docker-compose -f docker-compose.dev.yml down
    docker-compose -f docker-compose.prod.yml down
    echo -e "${GREEN}Containers stopped${NC}"
}

# Restart containers
restart() {
    stop
    if [ "$1" == "prod" ]; then
        start_prod
    else
        start_dev
    fi
}

# Show container logs
show_logs() {
    if [ "$1" == "prod" ]; then
        docker-compose -f docker-compose.prod.yml logs -f
    else
        docker-compose -f docker-compose.dev.yml logs -f
    fi
}

# Show container status
show_status() {
    echo -e "${YELLOW}Container Status:${NC}"
    docker-compose -f docker-compose.dev.yml ps
    docker-compose -f docker-compose.prod.yml ps
}

# Clean up Docker resources
clean() {
    echo -e "${YELLOW}Cleaning up Docker resources...${NC}"
    docker system prune -f
    echo -e "${GREEN}Cleanup complete${NC}"
}

# Build Docker images
build() {
    if [ "$1" == "prod" ]; then
        echo -e "${YELLOW}Building production images...${NC}"
        docker-compose -f docker-compose.prod.yml build
    else
        echo -e "${YELLOW}Building development images...${NC}"
        docker-compose -f docker-compose.dev.yml build
    fi
    echo -e "${GREEN}Build complete${NC}"
}

# Remove all unused Docker resources
prune() {
    echo -e "${RED}Warning: This will remove all unused containers, networks, and images${NC}"
    read -p "Are you sure you want to continue? [y/N] " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}Removing unused Docker resources...${NC}"
        docker system prune -a --volumes -f
        echo -e "${GREEN}Prune complete${NC}"
    fi
}

# Check if Docker is running
check_docker

# Parse command line arguments
case "$1" in
    start-dev)
        start_dev
        ;;
    start-prod)
        start_prod
        ;;
    stop)
        stop
        ;;
    restart)
        restart "$2"
        ;;
    logs)
        show_logs "$2"
        ;;
    status)
        show_status
        ;;
    clean)
        clean
        ;;
    build)
        build "$2"
        ;;
    prune)
        prune
        ;;
    help)
        usage
        ;;
    *)
        usage
        exit 1
        ;;
esac

exit 0 