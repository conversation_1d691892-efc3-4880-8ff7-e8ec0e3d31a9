import { RafflePrize, RaffleTier, RaffleTicket, Raffle } from '../types/raffle';
import { getRedisClient } from '../utils/redis';
import type { Redis } from 'ioredis';

class RaffleService {
  private static instance: RaffleService;
  private static redisClient: Redis | null = null;

  private constructor() {
    RaffleService.initRedis();
  }

  private static async initRedis() {
    if (!RaffleService.redisClient) {
      RaffleService.redisClient = await getRedisClient();
    }
  }

  public static getInstance(): RaffleService {
    if (!RaffleService.instance) {
      RaffleService.instance = new RaffleService();
    }
    return RaffleService.instance;
  }

  async getPrizesByTier(tier: RaffleTier): Promise<RafflePrize[]> {
    await RaffleService.initRedis();
    const prizeIds = await RaffleService.redisClient!.smembers(`prizes:${tier}`);
    const prizes = await Promise.all(
      prizeIds.map(async (id) => {
        const data = await RaffleService.redisClient!.get(`prize:${id}`);
        return data ? JSON.parse(data) : null;
      })
    );
    return prizes.filter(Boolean);
  }

  async purchaseTickets(prizeId: string, quantity: number): Promise<RaffleTicket[]> {
    await RaffleService.initRedis();
    // TODO: Implement ticket purchase logic
    return [];
  }

  async getMyTickets(): Promise<RaffleTicket[]> {
    await RaffleService.initRedis();
    // TODO: Implement get user tickets logic
    return [];
  }

  async getWinningOdds(prizeId: string, ticketQuantity: number): Promise<number> {
    await RaffleService.initRedis();
    // TODO: Implement odds calculation
    return 0;
  }

  async getRecentWinners(): Promise<{
    userId: string;
    prizeId: string;
    winDate: Date;
    ticketNumber: number;
  }[]> {
    await RaffleService.initRedis();
    // TODO: Implement recent winners logic
    return [];
  }

  static async getTicket(userId: string, ticketId: string): Promise<RaffleTicket | null> {
    await this.initRedis();
    const data = await RaffleService.redisClient!.get(`ticket:${ticketId}`);
    return data ? JSON.parse(data) : null;
  }

  static async getRaffles(): Promise<Raffle[]> {
    await this.initRedis();
    const raffleIds = await RaffleService.redisClient!.smembers('active_raffles');
    const raffles = await Promise.all(
      raffleIds.map(async (id) => {
        const data = await RaffleService.redisClient!.get(`raffle:${id}`);
        return data ? JSON.parse(data) : null;
      })
    );

    return raffles.filter(Boolean);
  }

  static async getUserTickets(userId: string): Promise<RaffleTicket[]> {
    await this.initRedis();
    const ticketIds = await RaffleService.redisClient!.smembers(`user:${userId}:raffle_tickets`);
    const tickets = await Promise.all(
      ticketIds.map(async (id) => {
        const data = await RaffleService.redisClient!.get(`raffle:ticket:${id}`);
        return data ? JSON.parse(data) : null;
      })
    );

    return tickets.filter(Boolean);
  }
}

export const raffleService = RaffleService.getInstance();