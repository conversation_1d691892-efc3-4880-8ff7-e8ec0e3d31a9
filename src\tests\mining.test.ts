import { MiningService } from '../services/mining/miningService';
import { PrismaClient } from '@prisma/client';
import { redis } from '../utils/redis';

const prisma = new PrismaClient();

interface MiningStats {
  deviceId: string;
  totalHashes: number;
  hashRate: number;
  successfulMines: number;
}

describe('MiningService', () => {
  let miningService: MiningService;

  beforeAll(async () => {
    miningService = MiningService.getInstance();
    await redis.flushall();
  });

  afterEach(async () => {
    await redis.flushall();
  });

  afterAll(async () => {
    await prisma.$disconnect();
    await redis.quit();
  });

  describe('Mining Operations', () => {
    it('should start mining for eligible user', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+1234567890',
          isVerified: true,
          settings: {
            create: {
              miningEnabled: true
            }
          }
        }
      });

      const deviceId = 'test-device-1';
      await miningService.startMining(testUser.id, deviceId);

      // Verify mining started
      const stats = await miningService.getMiningStats(testUser.id);
      expect(stats).toHaveLength(1);
      expect(stats[0].deviceId).toBe(deviceId);
      expect(stats[0].hashRate).toBeGreaterThan(0);
    });

    it('should reject mining for ineligible user', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+1234567891',
          isVerified: true,
          settings: {
            create: {
              miningEnabled: false
            }
          }
        }
      });

      await expect(
        miningService.startMining(testUser.id, 'test-device-2')
      ).rejects.toThrow('Mining not enabled for user');
    });

    it('should prevent duplicate mining sessions', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+1234567892',
          isVerified: true,
          settings: {
            create: {
              miningEnabled: true
            }
          }
        }
      });

      const deviceId = 'test-device-3';
      await miningService.startMining(testUser.id, deviceId);

      await expect(
        miningService.startMining(testUser.id, deviceId)
      ).rejects.toThrow('Device is already mining');
    });
  });

  describe('Mining Rewards', () => {
    it('should award rewards for successful mining', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+1234567893',
          isVerified: true,
          settings: {
            create: {
              miningEnabled: true
            }
          },
          wallet: {
            create: {
              loomCoinBalance: 0,
              bitcoinBalance: 0
            }
          }
        }
      });

      // Simulate successful mining
      await miningService.startMining(testUser.id, 'test-device-4');
      
      // Wait for some mining rewards
      await new Promise(resolve => setTimeout(resolve, 5000));

      const wallet = await prisma.wallet.findUnique({
        where: { userId: testUser.id }
      });

      expect(wallet?.loomCoinBalance).toBeGreaterThan(0);
      expect(wallet?.bitcoinBalance).toBeGreaterThan(0);
    });

    it('should track mining statistics accurately', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+1234567894',
          isVerified: true,
          settings: {
            create: {
              miningEnabled: true
            }
          }
        }
      });

      const deviceId = 'test-device-5';
      await miningService.startMining(testUser.id, deviceId);

      // Wait for some mining activity
      await new Promise(resolve => setTimeout(resolve, 3000));

      const stats = await miningService.getMiningStats(testUser.id);
      const deviceStats = stats.find((s: MiningStats) => s.deviceId === deviceId);

      expect(deviceStats).toBeDefined();
      expect(deviceStats?.totalHashes).toBeGreaterThan(0);
      expect(deviceStats?.hashRate).toBeGreaterThan(0);
      expect(deviceStats?.successfulMines).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Mining Control', () => {
    it('should stop mining when requested', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+1234567895',
          isVerified: true,
          settings: {
            create: {
              miningEnabled: true
            }
          }
        }
      });

      const deviceId = 'test-device-6';
      await miningService.startMining(testUser.id, deviceId);
      await miningService.stopMining(testUser.id, deviceId);

      const hashRate = await redis.hget(`mining:hashrate:${testUser.id}`, deviceId);
      expect(hashRate).toBeNull();
    });

    it('should handle multiple devices per user', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+1234567896',
          isVerified: true,
          settings: {
            create: {
              miningEnabled: true
            }
          }
        }
      });

      const devices = ['test-device-7', 'test-device-8'];
      await Promise.all(
        devices.map(deviceId => miningService.startMining(testUser.id, deviceId))
      );

      const stats = await miningService.getMiningStats(testUser.id);
      expect(stats).toHaveLength(2);
      expect(stats.map((s: MiningStats) => s.deviceId)).toEqual(expect.arrayContaining(devices));
    });
  });

  describe('Global Mining Stats', () => {
    it('should track global mining statistics', async () => {
      const users = await Promise.all([
        prisma.user.create({
          data: {
            phoneNumber: '+1234567897',
            isVerified: true,
            settings: {
              create: {
                miningEnabled: true
              }
            }
          }
        }),
        prisma.user.create({
          data: {
            phoneNumber: '+1234567898',
            isVerified: true,
            settings: {
              create: {
                miningEnabled: true
              }
            }
          }
        })
      ]);

      await Promise.all(
        users.map((user, i) => 
          miningService.startMining(user.id, `test-device-${9 + i}`)
        )
      );

      // Wait for some mining activity
      await new Promise(resolve => setTimeout(resolve, 3000));

      const globalStats = await miningService.getGlobalMiningStats();
      expect(globalStats.activeMiners).toBe(2);
      expect(globalStats.totalHashRate).toBeGreaterThan(0);
      expect(globalStats.totalHashes).toBeGreaterThan(0);
    });

    it('should handle miner disconnections gracefully', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+1234567899',
          isVerified: true,
          settings: {
            create: {
              miningEnabled: true
            }
          }
        }
      });

      const deviceId = 'test-device-11';
      await miningService.startMining(testUser.id, deviceId);

      // Simulate unexpected disconnection
      process.emit('SIGINT', 'SIGINT');

      // Wait for cleanup
      await new Promise(resolve => setTimeout(resolve, 1000));

      const hashRate = await redis.hget(`mining:hashrate:${testUser.id}`, deviceId);
      expect(hashRate).toBeNull();
    });
  });
}); 