#!/bin/bash

# Function to check if a service is healthy
check_service() {
    local service=$1
    local endpoint=$2
    local expected=$3
    
    response=$(curl -s -o /dev/null -w "%{http_code}" $endpoint)
    
    if [ "$response" = "$expected" ]; then
        echo "✅ $service is healthy (Status: $response)"
        return 0
    else
        echo "❌ $service is unhealthy (Status: $response)"
        return 1
    fi
}

# Check API health
check_service "API" "http://localhost:3000/api/health" "200"

# Check database connection through API health endpoint
db_status=$(curl -s http://localhost:3000/api/health | grep -o '"database":true')
if [ "$db_status" = '"database":true' ]; then
    echo "✅ Database connection is healthy"
else
    echo "❌ Database connection is unhealthy"
    exit 1
fi

# Check Redis connection through API health endpoint
redis_status=$(curl -s http://localhost:3000/api/health | grep -o '"redis":true')
if [ "$redis_status" = '"redis":true' ]; then
    echo "✅ Redis connection is healthy"
else
    echo "❌ Redis connection is unhealthy"
    exit 1
fi

# All checks passed
exit 0 