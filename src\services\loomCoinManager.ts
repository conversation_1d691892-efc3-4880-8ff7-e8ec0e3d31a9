import { Redis } from '@upstash/redis';
import { ethers } from 'ethers';
import { getRedisClient } from '../utils/redis';
import { GroupTier } from '../types/loom';

interface LoomCoinTransaction {
  id: string;
  fromUserId: string;
  toUserId: string;
  amount: number;
  timestamp: number;
  type: 'REWARD' | 'TRANSFER' | 'LOCK' | 'UNLOCK' | 'GROUP_ENTRY';
  groupId?: string;
}

interface PendingPurchase {
  userId: string;
  amount: number;
  timestamp: number;
  status: 'pending' | 'completed' | 'failed';
}

export class LoomCoinManager {
  private static redis = new Redis({
    url: process.env.UPSTASH_REDIS_REST_URL!,
    token: process.env.UPSTASH_REDIS_REST_TOKEN!,
  });

  private static CONTRACT_ADDRESS = process.env.LOOMCOIN_CONTRACT_ADDRESS!;
  private static provider = new ethers.providers.JsonRpcProvider(process.env.ETH_RPC_URL);
  private static wallet = new ethers.Wallet(process.env.GAME_MANAGER_PRIVATE_KEY!, LoomCoinManager.provider);

  static async getInGameBalance(userId: string): Promise<number> {
    const redis = await getRedisClient();
    const balance = await redis.get(`loomcoin:balance:${userId}`);
    return balance ? Number(balance) : 0;
  }

  static async lockTokens(userId: string, amount: number): Promise<void> {
    const redis = await getRedisClient();
    const contract = new ethers.Contract(
      this.CONTRACT_ADDRESS,
      ['function lockTokens(uint256 amount)'],
      this.wallet
    );

    await contract.lockTokens(ethers.utils.parseEther(amount.toString()));
    await redis.incrby(`loomcoin:balance:${userId}`, amount);

    const transaction: LoomCoinTransaction = {
      id: `tx_${Date.now()}_${userId}`,
      fromUserId: userId,
      toUserId: userId,
      amount,
      timestamp: Date.now(),
      type: 'LOCK'
    };

    await redis.rpush('loomcoin:transactions', JSON.stringify(transaction));
  }

  static async unlockTokens(userId: string, amount: number): Promise<void> {
    const redis = await getRedisClient();
    const balance = await this.getInGameBalance(userId);
    
    if (balance < amount) {
      throw new Error('Insufficient in-game balance');
    }

    const contract = new ethers.Contract(
      this.CONTRACT_ADDRESS,
      ['function unlockTokens(uint256 amount)'],
      this.wallet
    );

    await contract.unlockTokens(ethers.utils.parseEther(amount.toString()));
    await redis.decrby(`loomcoin:balance:${userId}`, amount);

    const transaction: LoomCoinTransaction = {
      id: `tx_${Date.now()}_${userId}`,
      fromUserId: userId,
      toUserId: userId,
      amount,
      timestamp: Date.now(),
      type: 'UNLOCK'
    };

    await redis.rpush('loomcoin:transactions', JSON.stringify(transaction));
  }

  static async moveTokensInGame(
    fromUserId: string,
    toUserId: string,
    amount: number,
    type: LoomCoinTransaction['type'],
    groupId?: string
  ): Promise<void> {
    const redis = await getRedisClient();
    const fromBalance = await this.getInGameBalance(fromUserId);
    
    if (fromBalance < amount) {
      throw new Error('Insufficient in-game balance');
    }

    // Update in-game balances
    await Promise.all([
      redis.decrby(`loomcoin:balance:${fromUserId}`, amount),
      redis.incrby(`loomcoin:balance:${toUserId}`, amount)
    ]);

    // Record transaction
    const transaction: LoomCoinTransaction = {
      id: `tx_${Date.now()}_${fromUserId}_${toUserId}`,
      fromUserId,
      toUserId,
      amount,
      timestamp: Date.now(),
      type,
      groupId
    };

    await redis.rpush('loomcoin:transactions', JSON.stringify(transaction));

    // Update contract state
    const contract = new ethers.Contract(
      this.CONTRACT_ADDRESS,
      ['function moveTokensInGame(address from, address to, uint256 amount)'],
      this.wallet
    );

    await contract.moveTokensInGame(
      await this.getUserAddress(fromUserId),
      await this.getUserAddress(toUserId),
      ethers.utils.parseEther(amount.toString())
    );
  }

  static async getUserTransactions(userId: string): Promise<LoomCoinTransaction[]> {
    const redis = await getRedisClient();
    const transactions = await redis.lrange('loomcoin:transactions', 0, -1);
    
    return transactions
      .map(tx => JSON.parse(tx))
      .filter(tx => tx.fromUserId === userId || tx.toUserId === userId);
  }

  private static async getUserAddress(userId: string): Promise<string> {
    const redis = await getRedisClient();
    const address = await redis.get(`user:${userId}:eth_address`);
    if (!address) {
      throw new Error('User ETH address not found');
    }
    return address;
  }

  static async calculateGrowthRate(): Promise<number> {
    const redis = await getRedisClient();
    const totalLocked = await redis.get('loomcoin:total_locked');
    const activeGroups = await redis.get('loomcoin:active_groups');
    
    // Base growth rate is 0.1% per day
    let growthRate = 0.001;

    // Add 0.01% for every 1000 LoomCoins locked
    if (totalLocked) {
      growthRate += (Number(totalLocked) / 1000) * 0.0001;
    }

    // Add 0.02% for every active group
    if (activeGroups) {
      growthRate += (Number(activeGroups) * 0.0002);
    }

    return growthRate;
  }

  static async updateGrowthRate(): Promise<void> {
    const contract = new ethers.Contract(
      this.CONTRACT_ADDRESS,
      ['function updateGrowthRate(uint256 newRate)'],
      this.wallet
    );

    const growthRate = await this.calculateGrowthRate();
    await contract.updateGrowthRate(Math.floor(growthRate * 1000)); // Convert to basis points
  }

  static async getGroupEntryFee(tier: GroupTier, priceType: 'USD' | 'LOOMCOIN' = 'LOOMCOIN'): Promise<number> {
    const baseFee = tier;
    
    if (priceType === 'USD') {
      // Convert tier value to USD equivalent
      return baseFee; // This will represent USD value (e.g., 10, 20, 50, etc.)
    } else {
      // Get current LoomCoin price from the market
      const loomCoinPrice = await this.getCurrentLoomCoinPrice();
      // Calculate how many LoomCoins are needed to match the USD value
      return Math.ceil(baseFee / loomCoinPrice);
    }
  }

  static async getCurrentLoomCoinPrice(): Promise<number> {
    try {
      const redis = await getRedisClient();
      const price = await redis.get('loomcoin:current_price');
      return price ? parseFloat(price) : 1; // Default to 1:1 if price not set
    } catch (error) {
      console.error('Error fetching LoomCoin price:', error);
      return 1; // Default to 1:1 in case of error
    }
  }

  static async setGroupPriceType(groupId: string, priceType: 'USD' | 'LOOMCOIN'): Promise<void> {
    const redis = await getRedisClient();
    await redis.set(`group:${groupId}:price_type`, priceType);
  }

  static async getGroupPriceType(groupId: string): Promise<'USD' | 'LOOMCOIN'> {
    const redis = await getRedisClient();
    const type = await redis.get(`group:${groupId}:price_type`);
    return (type as 'USD' | 'LOOMCOIN') || 'LOOMCOIN';
  }

  static async storePendingPurchase(
    paymentId: string,
    userId: string,
    amount: number
  ): Promise<void> {
    const redis = await getRedisClient();
    const purchase: PendingPurchase = {
      userId,
      amount,
      timestamp: Date.now(),
      status: 'pending'
    };

    await redis.set(
      `loomcoin:pending_purchase:${paymentId}`,
      JSON.stringify(purchase),
      'EX',
      3600 // Expire after 1 hour
    );
  }

  static async completePurchase(
    paymentId: string
  ): Promise<void> {
    const redis = await getRedisClient();
    const purchaseData = await redis.get(`loomcoin:pending_purchase:${paymentId}`);
    
    if (!purchaseData) {
      throw new Error('Purchase not found');
    }

    const purchase: PendingPurchase = JSON.parse(purchaseData);
    if (purchase.status !== 'pending') {
      throw new Error('Purchase already processed');
    }

    // Update purchase status
    purchase.status = 'completed';
    await redis.set(
      `loomcoin:pending_purchase:${paymentId}`,
      JSON.stringify(purchase),
      'EX',
      3600
    );

    // Mint new LoomCoins to the contract
    const contract = new ethers.Contract(
      this.CONTRACT_ADDRESS,
      ['function mint(address to, uint256 amount)'],
      this.wallet
    );

    const userAddress = await this.getUserAddress(purchase.userId);
    await contract.mint(
      userAddress,
      ethers.utils.parseEther(purchase.amount.toString())
    );

    // Lock tokens for in-game use
    await this.lockTokens(purchase.userId, purchase.amount);

    // Record transaction
    const transaction: LoomCoinTransaction = {
      id: `purchase_${Date.now()}_${purchase.userId}`,
      fromUserId: 'MINT',
      toUserId: purchase.userId,
      amount: purchase.amount,
      timestamp: Date.now(),
      type: 'LOCK'
    };

    await redis.rpush('loomcoin:transactions', JSON.stringify(transaction));
  }

  static async failPurchase(
    paymentId: string
  ): Promise<void> {
    const redis = await getRedisClient();
    const purchaseData = await redis.get(`loomcoin:pending_purchase:${paymentId}`);
    
    if (!purchaseData) {
      throw new Error('Purchase not found');
    }

    const purchase: PendingPurchase = JSON.parse(purchaseData);
    purchase.status = 'failed';
    
    await redis.set(
      `loomcoin:pending_purchase:${paymentId}`,
      JSON.stringify(purchase),
      'EX',
      3600
    );
  }
} 