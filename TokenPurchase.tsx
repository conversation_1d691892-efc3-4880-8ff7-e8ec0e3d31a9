import React from "react";

const TokenPurchase: React.FC = () => {
   return (
      <div className="p-6">
         <div className="font-bold text-2xl mb-2">Welcome to LoomLoot</div>
         <div className="font-semibold mb-4">Buy Your Tokens To Begin Loom</div>

         <div className="bg-gray-100 p-2 rounded-md mb-2">
            <div className="grid grid-cols-2">
               <div>500 Coins</div>
               <div>
                  $100
                  <button className="ml-4 bg-yellow-400 hover:bg-yellow-500 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                     {" "}
                     Buy{" "}
                  </button>
               </div>
            </div>
         </div>

         <div className="bg-gray-100 p-2 rounded-md mb-2">
            <div className="grid grid-cols-2">
               <div>500 Coins</div>
               <div>
                  $100
                  <button className="ml-4 bg-yellow-400 hover:bg-yellow-500 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                     {" "}
                     Buy{" "}
                  </button>
               </div>
            </div>
         </div>
         <div className="bg-gray-100 p-2 rounded-md mb-2">
            <div className="grid grid-cols-2">
               <div>1000 Coins</div>
               <div>
                  $1000
                  <button className="ml-4 bg-yellow-400 hover:bg-yellow-500 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                     {" "}
                     Buy{" "}
                  </button>
               </div>
            </div>
         </div>

         <button className="mt-4 text-sm text-gray-500" type="button">
            Remind Me Later
         </button>
      </div>
   );
};

export default TokenPurchase;
