import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Types for environment variables
interface SupabaseConfig {
  url: string;
  anonKey: string;
}

// --- IMPORTANT ---
// These should be set via environment variables in a real application
// For development, you can set them in your .env.local file:
// NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
// NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
// --- IMPORTANT ---

const getSupabaseConfig = (): SupabaseConfig => {
  // Try to get from environment variables first
  const envUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const envAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  // Fallback to placeholder values (for development only)
  const supabaseUrl = envUrl || 'YOUR_SUPABASE_URL';
  const supabaseAnonKey = envAnonKey || 'YOUR_SUPABASE_ANON_KEY';

  return {
    url: supabaseUrl,
    anonKey: supabaseAnonKey,
  };
};

const config = getSupabaseConfig();

// Validate that the credentials are properly configured
const validateCredentials = (config: SupabaseConfig): boolean => {
  if (config.url === 'YOUR_SUPABASE_URL' || config.anonKey === 'YOUR_SUPABASE_ANON_KEY') {
    console.warn(
      `⚠️  Supabase credentials not configured! 
      
Please set your Supabase credentials:
1. Create a .env.local file in your project root
2. Add the following lines:
   NEXT_PUBLIC_SUPABASE_URL=your_actual_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_actual_supabase_anon_key
3. Replace with your actual Supabase project details from https://app.supabase.com

Current config:
- URL: ${config.url}
- Key: ${config.anonKey.substring(0, 10)}...`
    );
    return false;
  }

  if (!config.url.startsWith('https://') || !config.anonKey.startsWith('eyJ')) {
    console.warn('⚠️  Supabase credentials appear to be invalid format');
    return false;
  }

  return true;
};

// Validate credentials
const isConfigValid = validateCredentials(config);

// Create the Supabase client
let supabase: SupabaseClient;

try {
  supabase = createClient(config.url, config.anonKey, {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
    },
  });

  if (isConfigValid) {
    console.log('✅ Supabase client initialized successfully');
  } else {
    console.log('⚠️  Supabase client initialized with placeholder credentials');
  }
} catch (error) {
  console.error('❌ Failed to initialize Supabase client:', error);
  throw new Error('Failed to initialize Supabase client');
}

// Helper functions for common Supabase operations
export const supabaseHelpers = {
  /**
   * Check if Supabase is properly configured
   */
  isConfigured: (): boolean => isConfigValid,

  /**
   * Get current user session
   */
  getCurrentUser: async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error) throw error;
      return user;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  },

  /**
   * Sign in with email and password
   */
  signIn: async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error signing in:', error);
      throw error;
    }
  },

  /**
   * Sign up with email and password
   */
  signUp: async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error signing up:', error);
      throw error;
    }
  },

  /**
   * Sign out current user
   */
  signOut: async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  },

  /**
   * Listen to auth state changes
   */
  onAuthStateChange: (callback: (event: string, session: any) => void) => {
    return supabase.auth.onAuthStateChange(callback);
  },
};

// Export the Supabase client and helpers
export { supabase };
export default supabase;
