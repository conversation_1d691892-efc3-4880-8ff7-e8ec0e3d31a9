version: '3.8'

services:
  # Main application service
  app:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./src:/app/src
      - ./data:/app/data
    environment:
      - POSTGRES_HOST=db
      - REDIS_HOST=cache
      - ML_SERVICE_HOST=ml_service
    depends_on:
      - db
      - cache
      - ml_service

  # Database service
  db:
    image: postgres:15
    container_name: loomloot_db
    restart: always
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: loomloot_dev
    volumes:
      - postgres_data:/var/lib/postgresql/data

  # Redis cache service
  redis:
    image: redis:7
    container_name: loomloot_redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # ML service with GGML models
  ml_service:
    build:
      context: .
      dockerfile: Dockerfile.ml
    volumes:
      - ./models:/app/models
    environment:
      - MODEL_PATH=/app/models
      - CUDA_VISIBLE_DEVICES=0  # For GPU support

  # Admin panel service
  admin:
    build:
      context: .
      dockerfile: Dockerfile.admin
    ports:
      - "8001:8001"
    depends_on:
      - app
      - db

volumes:
  postgres_data:
  redis_data:
  model_data: 