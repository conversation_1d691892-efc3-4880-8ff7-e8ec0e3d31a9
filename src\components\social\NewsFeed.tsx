import React from 'react';
import FeedItem from './FeedItem';
import Story<PERSON>eel from './StoryReel';

// Types
interface User {
  name: string;
  iconUrl: string;
}

interface Story {
  id: string;
  user: User;
}

interface FeedItemData {
  id: number;
  user: User;
  timestamp: string;
  action?: string;
  location?: string;
  content: string;
  imageUrl?: string;
  likes: number;
  comments: number;
  shares: number;
}

// Dummy data for stories
const storiesData: Story[] = [
  { id: 's1', user: { name: 'Live', iconUrl: 'https://play.rosebud.ai/assets/play.png?vC77' } },
  { id: 's2', user: { name: '<PERSON><PERSON>', iconUrl: 'https://play.rosebud.ai/assets/ASSET%20FACE%205.png?dNC6' } },
  { id: 's3', user: { name: '<PERSON>', iconUrl: 'https://play.rosebud.ai/assets/ASSET%20FACE%204.png?i4TK' } },
  { id: 's4', user: { name: '<PERSON>', iconUrl: 'https://play.rosebud.ai/assets/ASSET%20FACE%202.png?JRmr' } },
  { id: 's5', user: { name: 'User 4', iconUrl: 'https://via.placeholder.com/50/888/fff?text=U4' } },
  { id: 's6', user: { name: 'User 5', iconUrl: 'https://via.placeholder.com/50/777/fff?text=U5' } },
  { id: 's7', user: { name: 'User 6', iconUrl: 'https://via.placeholder.com/50/666/fff?text=U6' } },
];

// Dummy data for the feed
const feedItemsData: FeedItemData[] = [
  {
    id: 1,
    user: { name: 'Sally', iconUrl: 'https://play.rosebud.ai/assets/ASSET%20FACE%202.png?JRmr' },
    timestamp: 'Mar 28, 2025',
    action: 'Played 100 Token Tier',
    content: 'Earned 700 Tokens in the 100 Token Tier!',
    likes: 20,
    comments: 3,
    shares: 1,
  },
  {
    id: 2,
    user: { name: 'Jena', iconUrl: 'https://play.rosebud.ai/assets/ASSET%20FACE%205.png?dNC6' },
    timestamp: 'Mar 25, 2025',
    location: 'Bali, Indonesia',
    content: '"Jena is ranked 3rd place on the Bali, Indonesia Leaderboard" #Leaderboard #Bali 🟢',
    imageUrl: 'https://img.freepik.com/free-photo/beautiful-shot-sea-with-mountain-distance-clear-sky_181624-22489.jpg',
    likes: 20,
    comments: 3,
    shares: 1,
  },
  {
    id: 3,
    user: { name: 'Michael', iconUrl: 'https://play.rosebud.ai/assets/ASSET%20FACE%204.png?i4TK' },
    timestamp: 'Mar 25, 2025',
    action: 'is Live Streaming.',
    content: 'Join my livestream! #1000TokenTier',
    imageUrl: 'https://img.freepik.com/free-photo/crowd-people-silhouettes-concert-front-stage-bright-lights_181624-24291.jpg',
    likes: 20,
    comments: 3,
    shares: 1,
  },
];

const NewsFeed: React.FC = () => {
  return (
    <div className="news-feed">
      {/* Render the Story Reel */}
      <StoryReel stories={storiesData} />
      
      <div className="feed-items-list">
        {feedItemsData.map(item => (
          <FeedItem key={item.id} item={item} />
        ))}
      </div>
    </div>
  );
};

export default NewsFeed;
