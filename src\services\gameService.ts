import { redis } from '../utils/redis';

interface GameChallenge {
  id: string;
  challengerId: string;
  stake: number;
  status: 'PENDING' | 'ACCEPTED' | 'DECLINED' | 'COMPLETED';
  opponentId?: string;
  winnerId?: string;
  createdAt: number;
  completedAt?: number;
}

interface GameResult {
  gameId: string;
  winnerId: string;
  loserId: string;
  stake: number;
  winnings: number;
  timestamp: number;
}

export class GameService {
  static async createChallenge(
    challengerId: string,
    stake: number
  ): Promise<GameChallenge> {
    // Validate stake amount
    if (stake < 1 || stake > 100) {
      throw new Error('Invalid stake amount');
    }

    // Check user balance
    const balance = await redis.get(`user:${challengerId}:balance`);
    if (!balance || Number(balance) < stake) {
      throw new Error('Insufficient balance');
    }

    // Create challenge
    const challenge: GameChallenge = {
      id: `game_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      challengerId,
      stake,
      status: 'PENDING',
      createdAt: Date.now()
    };

    // Reserve stake amount
    await redis.decrby(`user:${challengerId}:balance`, stake);
    await redis.set(`game:${challenge.id}`, JSON.stringify(challenge));
    await redis.sadd(`user:${challengerId}:games`, challenge.id);

    return challenge;
  }

  static async acceptChallenge(
    gameId: string,
    opponentId: string
  ): Promise<GameChallenge> {
    const gameData = await redis.get(`game:${gameId}`);
    if (!gameData) throw new Error('Game not found');

    const game: GameChallenge = JSON.parse(gameData);
    if (game.status !== 'PENDING') {
      throw new Error('Game is no longer available');
    }

    // Check user balance
    const balance = await redis.get(`user:${opponentId}:balance`);
    if (!balance || Number(balance) < game.stake) {
      throw new Error('Insufficient balance');
    }

    // Reserve stake amount
    await redis.decrby(`user:${opponentId}:balance`, game.stake);

    // Update game
    game.opponentId = opponentId;
    game.status = 'ACCEPTED';
    await redis.set(`game:${gameId}`, JSON.stringify(game));
    await redis.sadd(`user:${opponentId}:games`, gameId);

    // Start game round
    await this.playGameRound(game);

    return game;
  }

  private static async playGameRound(game: GameChallenge): Promise<GameResult> {
    // Simulate game outcome (random for this example)
    const random = Math.random();
    const winnerId = random > 0.5 ? game.challengerId : game.opponentId!;
    const loserId = winnerId === game.challengerId ? game.opponentId! : game.challengerId;

    // Calculate winnings (95% of total pot, 5% platform fee)
    const totalPot = game.stake * 2;
    const winnings = Math.floor(totalPot * 0.95);

    // Update balances
    await redis.incrby(`user:${winnerId}:balance`, winnings);

    // Record result
    const result: GameResult = {
      gameId: game.id,
      winnerId,
      loserId,
      stake: game.stake,
      winnings,
      timestamp: Date.now()
    };

    // Update game status
    game.status = 'COMPLETED';
    game.winnerId = winnerId;
    game.completedAt = Date.now();
    await redis.set(`game:${game.id}`, JSON.stringify(game));

    // Save result
    await redis.set(`game:${game.id}:result`, JSON.stringify(result));
    await redis.sadd(`user:${winnerId}:wins`, game.id);
    await redis.sadd(`user:${loserId}:losses`, game.id);

    return result;
  }

  static async getActiveGames(): Promise<GameChallenge[]> {
    const gameIds = await redis.smembers('active_games');
    const games = await Promise.all(
      gameIds.map(async (id) => {
        const data = await redis.get(`game:${id}`);
        return data ? JSON.parse(data) : null;
      })
    );

    return games.filter(Boolean);
  }

  static async getUserGames(userId: string): Promise<GameChallenge[]> {
    const gameIds = await redis.smembers(`user:${userId}:games`);
    const games = await Promise.all(
      gameIds.map(async (id) => {
        const data = await redis.get(`game:${id}`);
        return data ? JSON.parse(data) : null;
      })
    );

    return games.filter(Boolean);
  }
} 