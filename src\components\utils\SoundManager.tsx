// Simple audio utility for LoomLoot
// Handles sound effects and audio management throughout the application

// Types
interface ActiveSource {
  source: AudioBufferSourceNode;
  startTime: number;
}

interface ActiveSources {
  [url: string]: ActiveSource;
}

// Audio context setup with fallback for older browsers
const audioContext = new ((window as any).AudioContext || (window as any).webkitAudioContext)();
const activeSources: ActiveSources = {}; // Keep track of playing sounds for potential control later
let soundEnabled = true; // Internal state for sound status

/**
 * Function to update the internal sound status
 * @param isEnabled - Whether sound should be enabled or disabled
 */
export const setSoundEnabledStatus = (isEnabled: boolean): void => {
  soundEnabled = isEnabled;
  console.log(`Sound enabled status set to: ${soundEnabled}`);
  
  if (!soundEnabled) {
    // Stop all currently playing sounds when disabled
    Object.values(activeSources).forEach(({ source }) => {
      try {
        source.stop();
      } catch (e) {
        // Ignore errors if source already stopped
        console.warn('Error stopping audio source:', e);
      }
    });
    
    // Clear active sources tracker
    for (const key in activeSources) { 
      delete activeSources[key]; 
    }
  }
};

/**
 * Get current sound enabled status
 * @returns boolean indicating if sound is enabled
 */
export const isSoundEnabled = (): boolean => {
  return soundEnabled;
};

/**
 * Play a sound from a URL with specified volume
 * @param url - The URL of the sound file to play
 * @param volume - Volume level (0.0 to 1.0), defaults to 0.5
 * @param forcePlay - Whether to play even if sound is disabled, defaults to false
 */
export const playSound = (url: string, volume: number = 0.5, forcePlay: boolean = false): void => {
  // Exit early if sound is disabled, unless forcePlay is true
  if (!soundEnabled && !forcePlay) {
    return;
  }

  if (!url) {
    console.warn('playSound called with no URL.');
    return;
  }

  // Validate volume parameter
  const clampedVolume = Math.max(0, Math.min(1, volume));
  if (clampedVolume !== volume) {
    console.warn(`Volume ${volume} clamped to ${clampedVolume}`);
  }

  // Simple check to prevent rapid re-triggering of the same sound
  if (activeSources[url] && audioContext.currentTime - activeSources[url].startTime < 0.1) {
    // console.log(`Skipping rapid replay of: ${url}`);
    return;
  }

  // Fetch and play the audio
  fetch(url)
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.arrayBuffer();
    })
    .then(arrayBuffer => audioContext.decodeAudioData(arrayBuffer))
    .then(audioBuffer => {
      const source = audioContext.createBufferSource();
      source.buffer = audioBuffer;

      const gainNode = audioContext.createGain();
      gainNode.gain.setValueAtTime(clampedVolume, audioContext.currentTime);

      source.connect(gainNode);
      gainNode.connect(audioContext.destination);

      source.start();
      activeSources[url] = { source, startTime: audioContext.currentTime }; // Track the source

      source.onended = () => {
        delete activeSources[url]; // Clean up tracker when done
      };
    })
    .catch(error => {
      console.error(`Error playing sound ${url}:`, error);
    });
};

/**
 * Stop a specific sound by URL
 * @param url - The URL of the sound to stop
 */
export const stopSound = (url: string): void => {
  if (activeSources[url]) {
    try {
      activeSources[url].source.stop();
      delete activeSources[url];
    } catch (e) {
      console.warn(`Error stopping sound ${url}:`, e);
    }
  }
};

/**
 * Stop all currently playing sounds
 */
export const stopAllSounds = (): void => {
  Object.keys(activeSources).forEach(url => {
    stopSound(url);
  });
};

/**
 * Get the number of currently playing sounds
 * @returns number of active audio sources
 */
export const getActiveSoundCount = (): number => {
  return Object.keys(activeSources).length;
};

/**
 * Resume audio context if it's suspended (required for some browsers)
 */
export const resumeAudioContext = async (): Promise<void> => {
  if (audioContext.state === 'suspended') {
    try {
      await audioContext.resume();
      console.log('Audio context resumed');
    } catch (error) {
      console.error('Error resuming audio context:', error);
    }
  }
};

// Initialize audio context on first user interaction
let audioContextInitialized = false;

export const initializeAudioContext = (): void => {
  if (!audioContextInitialized) {
    resumeAudioContext();
    audioContextInitialized = true;
  }
};

// Auto-initialize on first user interaction
if (typeof window !== 'undefined') {
  const initOnInteraction = () => {
    initializeAudioContext();
    document.removeEventListener('click', initOnInteraction);
    document.removeEventListener('touchstart', initOnInteraction);
  };

  document.addEventListener('click', initOnInteraction);
  document.addEventListener('touchstart', initOnInteraction);
}

export default {
  playSound,
  stopSound,
  stopAllSounds,
  setSoundEnabledStatus,
  isSoundEnabled,
  getActiveSoundCount,
  resumeAudioContext,
  initializeAudioContext,
};
