import twilio from 'twilio';
import { redis } from '../utils/redis';

interface OTPRequest {
  phoneNumber: string;
  code: string;
  expiresAt: number;
  attempts: number;
}

export class AuthService {
  private static readonly OTP_EXPIRY = 10 * 60 * 1000; // 10 minutes
  private static readonly MAX_ATTEMPTS = 3;
  private static readonly twilioClient = twilio(
    process.env.TWILIO_ACCOUNT_SID,
    process.env.TWILIO_AUTH_TOKEN
  );

  static async requestOTP(phoneNumber: string): Promise<boolean> {
    // Validate phone number format
    if (!this.isValidPhoneNumber(phoneNumber)) {
      throw new Error('Invalid phone number format');
    }

    // Generate 6-digit OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    
    // Store OTP with expiration
    const otpRequest: OTPRequest = {
      phoneNumber,
      code: otp,
      expiresAt: Date.now() + this.OTP_EXPIRY,
      attempts: 0
    };

    await redis.set(
      `otp:${phoneNumber}`,
      JSON.stringify(otpRequest),
      'PX',
      this.OTP_EXPIRY
    );

    // Send OTP via Twilio
    try {
      await this.twilioClient.messages.create({
        body: `Your LoomLoot verification code is: ${otp}`,
        to: phoneNumber,
        from: process.env.TWILIO_PHONE_NUMBER
      });
      return true;
    } catch (error) {
      console.error('Failed to send OTP:', error);
      throw new Error('Failed to send verification code');
    }
  }

  static async verifyOTP(
    phoneNumber: string,
    code: string
  ): Promise<boolean> {
    const storedData = await redis.get(`otp:${phoneNumber}`);
    if (!storedData) {
      throw new Error('No verification code found or code expired');
    }

    const otpRequest: OTPRequest = JSON.parse(storedData);

    // Check expiration
    if (Date.now() > otpRequest.expiresAt) {
      await redis.del(`otp:${phoneNumber}`);
      throw new Error('Verification code expired');
    }

    // Check attempts
    if (otpRequest.attempts >= this.MAX_ATTEMPTS) {
      await redis.del(`otp:${phoneNumber}`);
      throw new Error('Too many failed attempts');
    }

    // Verify code
    if (otpRequest.code !== code) {
      otpRequest.attempts++;
      await redis.set(
        `otp:${phoneNumber}`,
        JSON.stringify(otpRequest),
        'PX',
        otpRequest.expiresAt - Date.now()
      );
      throw new Error('Invalid verification code');
    }

    // Success - clean up OTP
    await redis.del(`otp:${phoneNumber}`);
    return true;
  }

  private static isValidPhoneNumber(phoneNumber: string): boolean {
    // Basic phone number validation (E.164 format)
    return /^\+[1-9]\d{1,14}$/.test(phoneNumber);
  }
} 