import { redis } from '../utils/redis';
import { UserProfileService } from './userProfileService';

interface TaxWithholding {
  userId: string;
  year: number;
  totalEarnings: number;
  withheldAmount: number;
  paymentsSent: {
    amount: number;
    date: number;
    checkNumber: string;
  }[];
}

export class TaxManagementService {
  private static readonly WITHHOLDING_RATE = 0.5; // 50% withholding
  private static readonly TOKEN_TO_USD = 0.5; // 2 tokens = $1

  static async processCashout(
    userId: string,
    tokenAmount: number
  ): Promise<{ 
    netAmount: number;
    withheldAmount: number;
  }> {
    const profile = await UserProfileService.getProfile(userId);
    if (!profile) throw new Error('Profile not found');

    const usdAmount = tokenAmount * this.TOKEN_TO_USD;
    const withheldAmount = usdAmount * this.WITHHOLDING_RATE;
    const netAmount = usdAmount - withheldAmount;

    // Store tax withholding record
    const year = new Date().getFullYear();
    const key = `tax:withholding:${userId}:${year}`;
    
    const existingData = await redis.get(key);
    const withholding: TaxWithholding = existingData ? 
      JSON.parse(existingData) : {
        userId,
        year,
        totalEarnings: 0,
        withheldAmount: 0,
        paymentsSent: []
      };

    withholding.totalEarnings += usdAmount;
    withholding.withheldAmount += withheldAmount;

    await redis.set(key, JSON.stringify(withholding));
    
    // Add to tax reserve pool
    await redis.incrby('tax:reservePool', Math.floor(withheldAmount * 100));

    return { netAmount, withheldAmount };
  }

  static async generateTaxPayment(
    userId: string,
    year: number
  ): Promise<string> {
    const withholding = await this.getTaxWithholding(userId, year);
    if (!withholding) throw new Error('No tax withholding found');

    const checkNumber = `TAX${year}${userId.slice(-6)}`;
    
    withholding.paymentsSent.push({
      amount: withholding.withheldAmount,
      date: Date.now(),
      checkNumber
    });

    await redis.set(
      `tax:withholding:${userId}:${year}`,
      JSON.stringify(withholding)
    );

    // Deduct from reserve pool
    await redis.decrby(
      'tax:reservePool',
      Math.floor(withholding.withheldAmount * 100)
    );

    return checkNumber;
  }

  static async getTaxWithholding(
    userId: string,
    year: number
  ): Promise<TaxWithholding | null> {
    const data = await redis.get(`tax:withholding:${userId}:${year}`);
    return data ? JSON.parse(data) : null;
  }

  static async generateTaxDocument(
    userId: string,
    year: number,
    documentType: '1099-MISC' | 'W-9'
  ): Promise<void> {
    const profile = await UserProfileService.getProfile(userId);
    if (!profile) throw new Error('Profile not found');

    const withholding = await this.getTaxWithholding(userId, year);
    if (!withholding) throw new Error('No tax withholding found');

    // Add tax document to profile
    profile.taxDocuments.push({
      year,
      documentType,
      status: 'PENDING'
    });

    await UserProfileService.updateProfile(userId, profile);
  }
} 