import { getRedisClient } from './redis';

interface TokenUpdate {
  userId: string;
  amount: number;
  tokenType: string;
  paymentId: string;
  status: 'pending' | 'completed' | 'failed';
}

export const updateUserTokens = async (update: TokenUpdate): Promise<void> => {
  const redis = await getRedisClient();
  
  try {
    // Get current user balance
    const userKey = `user:${update.userId}:balance`;
    const currentBalance = await redis.get(userKey);
    const balance = currentBalance ? parseInt(currentBalance) : 0;
    
    // Update balance based on status
    if (update.status === 'completed') {
      await redis.set(userKey, balance + update.amount);
      
      // Record transaction
      await redis.zadd(
        `user:${update.userId}:transactions`,
        Date.now(),
        JSON.stringify({
          type: 'payment',
          amount: update.amount,
          tokenType: update.tokenType,
          paymentId: update.paymentId,
          timestamp: Date.now()
        })
      );
    }
    
    // Update payment status
    await redis.set(
      `payment:${update.paymentId}:status`,
      update.status,
      'EX',
      24 * 60 * 60 // Expire in 24 hours
    );
  } catch (error) {
    console.error('Error updating user tokens:', error);
    throw error;
  }
}; 