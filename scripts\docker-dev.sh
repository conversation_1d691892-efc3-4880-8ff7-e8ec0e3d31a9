#!/bin/bash

# Set colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}Error: Docker is not running${NC}"
        exit 1
    fi
}

# Function to display usage
usage() {
    echo "Usage: $0 [command]"
    echo
    echo "Commands:"
    echo "  start       Start development environment"
    echo "  stop        Stop development environment"
    echo "  restart     Restart development environment"
    echo "  logs        Show container logs"
    echo "  status      Show container status"
    echo "  clean       Clean up development environment"
    echo "  rebuild     Rebuild and restart containers"
    echo "  shell       Open shell in app container"
    echo "  db         Open database CLI"
    echo "  redis      Open Redis CLI"
    echo "  help       Show this help message"
}

# Start development environment
start() {
    echo -e "${YELLOW}Starting development environment...${NC}"
    docker-compose -f docker-compose.dev.yml up -d
    echo -e "${GREEN}Development environment started${NC}"
    echo "Access the application at http://localhost:3000"
    echo "Access Adminer at http://localhost:8080"
    echo "Access Redis Commander at http://localhost:8081"
}

# Stop development environment
stop() {
    echo -e "${YELLOW}Stopping development environment...${NC}"
    docker-compose -f docker-compose.dev.yml down
    echo -e "${GREEN}Development environment stopped${NC}"
}

# Restart development environment
restart() {
    stop
    start
}

# Show container logs
logs() {
    if [ "$1" ]; then
        docker-compose -f docker-compose.dev.yml logs -f "$1"
    else
        docker-compose -f docker-compose.dev.yml logs -f
    fi
}

# Show container status
status() {
    echo -e "${YELLOW}Container Status:${NC}"
    docker-compose -f docker-compose.dev.yml ps

    echo -e "\n${YELLOW}Resource Usage:${NC}"
    docker stats --no-stream $(docker-compose -f docker-compose.dev.yml ps -q)
}

# Clean up development environment
clean() {
    echo -e "${RED}Warning: This will remove all development containers, volumes, and cached images${NC}"
    read -p "Are you sure you want to continue? [y/N] " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose -f docker-compose.dev.yml down -v
        docker system prune -f
        echo -e "${GREEN}Development environment cleaned${NC}"
    fi
}

# Rebuild containers
rebuild() {
    echo -e "${YELLOW}Rebuilding containers...${NC}"
    docker-compose -f docker-compose.dev.yml build --no-cache
    docker-compose -f docker-compose.dev.yml up -d
    echo -e "${GREEN}Containers rebuilt and started${NC}"
}

# Open shell in app container
shell() {
    docker-compose -f docker-compose.dev.yml exec app /bin/sh
}

# Open database CLI
db() {
    docker-compose -f docker-compose.dev.yml exec db psql -U postgres -d loomloot_dev
}

# Open Redis CLI
redis() {
    docker-compose -f docker-compose.dev.yml exec redis redis-cli
}

# Check if Docker is running
check_docker

# Parse command line arguments
case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    logs)
        logs "$2"
        ;;
    status)
        status
        ;;
    clean)
        clean
        ;;
    rebuild)
        rebuild
        ;;
    shell)
        shell
        ;;
    db)
        db
        ;;
    redis)
        redis
        ;;
    help)
        usage
        ;;
    *)
        usage
        exit 1
        ;;
esac

exit 0 