import { redis } from '../utils/redis';
import { GameLogic } from '../utils/gameLogic';

interface DrawingResult {
  id: string;
  type: 'LOTTERY' | 'DAILY_RAFFLE' | 'WEEKLY_RAFFLE' | 'MONTHLY_RAFFLE';
  numbers?: number[];
  megaNumber?: number;
  winners: string[];
  totalPrize: number;
  drawingDate: number;
}

export class DrawingService {
  static async performLotteryDrawing(): Promise<DrawingResult> {
    // Generate winning numbers
    const { numbers, megaNumber } = GameLogic.generateLotteryNumbers();

    // Get all active tickets
    const ticketKeys = await redis.keys('lottery:ticket:*');
    const tickets = await Promise.all(
      ticketKeys.map(async (key) => {
        const data = await redis.get(key);
        return data ? JSON.parse(data) : null;
      })
    );

    // Check all tickets for winners
    const winners: { userId: string; prize: number }[] = [];
    for (const ticket of tickets) {
      if (!ticket || ticket.status !== 'PENDING') continue;

      const result = GameLogic.checkLotteryWin(
        ticket.numbers,
        ticket.megaNumber,
        numbers,
        megaNumber
      );

      if (result.prize > 0) {
        winners.push({ userId: ticket.userId, prize: result.prize });
        // Update ticket status
        ticket.status = 'WON';
        ticket.prize = result.prize;
        await redis.set(`lottery:ticket:${ticket.id}`, JSON.stringify(ticket));
      } else {
        ticket.status = 'LOST';
        await redis.set(`lottery:ticket:${ticket.id}`, JSON.stringify(ticket));
      }
    }

    // Record drawing result
    const drawingResult: DrawingResult = {
      id: `drawing_${Date.now()}`,
      type: 'LOTTERY',
      numbers,
      megaNumber,
      winners: winners.map(w => w.userId),
      totalPrize: winners.reduce((sum, w) => sum + w.prize, 0),
      drawingDate: Date.now()
    };

    await redis.set(`drawing:${drawingResult.id}`, JSON.stringify(drawingResult));
    return drawingResult;
  }

  static async performRaffleDrawing(raffleId: string): Promise<DrawingResult> {
    // Get raffle data
    const raffleData = await redis.get(`raffle:${raffleId}`);
    if (!raffleData) throw new Error('Raffle not found');
    const raffle = JSON.parse(raffleData);

    // Get all tickets
    const ticketIds = await redis.smembers(`raffle:${raffleId}:tickets`);
    
    // Select winner
    const winner = GameLogic.selectRaffleWinner(ticketIds);

    // Record result
    const drawingResult: DrawingResult = {
      id: `drawing_${Date.now()}`,
      type: `${raffle.type}_RAFFLE`,
      winners: [winner],
      totalPrize: raffle.prize,
      drawingDate: Date.now()
    };

    // Update tickets status
    for (const ticketId of ticketIds) {
      const ticketData = await redis.get(`raffle:ticket:${ticketId}`);
      if (!ticketData) continue;

      const ticket = JSON.parse(ticketData);
      ticket.status = ticket.userId === winner ? 'WON' : 'LOST';
      if (ticket.status === 'WON') {
        ticket.prize = raffle.prize;
      }

      await redis.set(`raffle:ticket:${ticketId}`, JSON.stringify(ticket));
    }

    // Update raffle status
    raffle.status = 'COMPLETED';
    raffle.winner = winner;
    await redis.set(`raffle:${raffleId}`, JSON.stringify(raffle));

    await redis.set(`drawing:${drawingResult.id}`, JSON.stringify(drawingResult));
    return drawingResult;
  }
} 