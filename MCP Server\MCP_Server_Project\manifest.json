{"name": "MCP Brave Search Server", "description": "A Model Context Protocol server implementation for Brave Search", "icons": [{"src": "/icon-128x128.png"}], "actions": {"search": {"description": "Performs a search query using Brave Search API", "params_schema": {"type": "object", "properties": {"query": {"type": "string", "description": "The search query to execute"}, "count": {"type": "number", "description": "Number of results to return", "default": 10}}, "required": ["query"]}}, "set_name": {"description": "Sets the name of the user.", "params_schema": {"type": "object", "properties": {"name": {"type": "string"}}}}}, "api_keys": {"brave_search": {"description": "API key for Brave Search", "required": true}}}