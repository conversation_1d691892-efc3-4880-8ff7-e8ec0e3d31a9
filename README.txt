LoomLoot - Next-Generation Play-to-Earn Ecosystem
=======================================

Project Overview
---------------
LoomLoot is a comprehensive play-to-earn gaming platform that includes various casino-style games, raffles, and competitive gaming features. The platform is built using Next.js for the frontend and includes a backend API service.

Project Structure
---------------
/
├── src/                    # Frontend source code
│   ├── components/         # Reusable React components
│   │   ├── admin/         # Admin-specific components
│   │   ├── common/        # Shared UI components
│   │   ├── games/         # Game-specific components
│   │   └── layout/        # Layout components
│   ├── pages/             # Next.js pages
│   │   ├── admin/         # Admin dashboard pages
│   │   ├── api/          # API routes
│   │   └── games/        # Game pages
│   ├── services/          # Service layer for API calls
│   ├── utils/             # Utility functions
│   ├── hooks/             # Custom React hooks
│   └── types/             # TypeScript type definitions
├── api/                   # Backend API service
├── public/                # Static assets
└── node_modules/          # Dependencies

Detailed Code Structure and Layers
--------------------------------
1. Frontend Layer (src/)
   ├── components/
   │   ├── admin/
   │   │   ├── BlackjackDealer.tsx         # Dealer controls for blackjack games
   │   │   ├── BlockedEntities.tsx         # Management of blocked users/items
   │   │   └── ScratchCardManager.tsx      # Scratch card game administration
   │   ├── common/
   │   │   ├── Button.tsx                  # Reusable button component with variants
   │   │   ├── Card.tsx                    # Container component for content
   │   │   ├── Input.tsx                   # Form input components
   │   │   └── Layout.tsx                  # Page layout wrapper
   │   ├── games/
   │   │   ├── RouletteGame.tsx            # Roulette game implementation
   │   │   ├── RouletteGame.module.css     # Roulette-specific styles
   │   │   ├── BlackjackGame.tsx           # Blackjack game implementation
   │   │   ├── PokerGame.tsx               # Poker game implementation
   │   │   ├── DiceGame.tsx                # Dice game implementation
   │   │   └── PoolGame.tsx                # Pool game implementation
   │   └── layout/
   │       ├── Header.tsx                  # Site header with navigation
   │       ├── Footer.tsx                  # Site footer
   │       └── Sidebar.tsx                 # Navigation sidebar
   │
   ├── pages/
   │   ├── admin/
   │   │   ├── users.tsx                   # User management dashboard
   │   │   ├── analytics.tsx               # Analytics dashboard
   │   │   └── blocked-entities.tsx        # Blocked entities management
   │   ├── api/
   │   │   ├── auth/                       # Authentication endpoints
   │   │   ├── games/                      # Game-related endpoints
   │   │   ├── payments/                   # Payment processing endpoints
   │   │   └── raffles/                    # Raffle system endpoints
   │   ├── games/
   │   │   ├── roulette.tsx               # Roulette game page
   │   │   ├── blackjack.tsx              # Blackjack game page
   │   │   └── poker.tsx                  # Poker game page
   │   ├── _app.tsx                       # Next.js app wrapper
   │   └── index.tsx                      # Homepage
   │
   ├── services/
   │   ├── raffleService.ts               # Raffle business logic
   │   ├── slots/
   │   │   └── slotStatistics.ts          # Slot machine statistics
   │   └── loomGroupSimulator.ts          # Group simulation service
   │
   ├── utils/
   │   ├── bitcoin.ts                     # Bitcoin payment utilities
   │   ├── redis.ts                       # Redis connection utilities
   │   ├── user.ts                        # User management utilities
   │   └── queryClient.ts                 # React Query configuration
   │
   ├── hooks/
   │   ├── useWebSocket.ts                # WebSocket connection hook
   │   └── useAuth.ts                     # Authentication hook
   │
   └── types/
       ├── raffle.ts                      # Raffle-related types
       └── websocket.ts                   # WebSocket message types

2. Backend Layer (api/)
   ├── src/
   │   ├── controllers/                   # Request handlers
   │   ├── services/                      # Business logic
   │   ├── models/                        # Data models
   │   └── middleware/                    # Custom middleware
   │
   ├── config/                           # Configuration files
   └── tests/                            # Backend tests

Component Dependencies and Data Flow
---------------------------------
1. Game Components Flow:
   RouletteGame.tsx
   ├── Uses: useWebSocket.ts for real-time updates
   ├── Depends on: Button.tsx for controls
   └── Connects to: /api/games/roulette endpoints

2. Admin Dashboard Flow:
   users.tsx
   ├── Uses: @tanstack/react-query for data fetching
   ├── Depends on: Card.tsx, Button.tsx
   └── Connects to: /api/admin/users endpoints

3. Payment Flow:
   bitcoin.ts
   ├── Uses: bitcoinjs-lib for crypto operations
   ├── Connects to: Blockchain.info API
   └── Integrates with: user.ts for balance updates

Code Layer Interactions
---------------------
1. Frontend-Backend Communication:
   ```typescript
   // Frontend API call (services/raffleService.ts)
   async getPrizesByTier(tier: RaffleTier): Promise<RafflePrize[]> {
     const response = await fetch(`/api/raffles?tier=${tier}`);
     return response.json();
   }

   // Backend handler (api/src/controllers/raffles.ts)
   async function getRaffles(req: Request, res: Response) {
     const { tier } = req.query;
     const prizes = await RaffleService.getPrizesByTier(tier);
     res.json(prizes);
   }
   ```

2. WebSocket Integration:
   ```typescript
   // Frontend connection (hooks/useWebSocket.ts)
   const ws = new WebSocket(`ws://localhost:3000/api/ws/games`);
   ws.onmessage = (event) => handleGameUpdate(event.data);

   // Backend handler (api/src/websocket/gameHandler.ts)
   wss.on('connection', (ws) => {
     ws.on('message', handleGameAction);
   });
   ```

3. Redis Integration:
   ```typescript
   // Frontend service (services/raffleService.ts)
   const redisClient = await getRedisClient();
   await redisClient.set(`raffle:${id}`, JSON.stringify(raffleData));

   // Backend service (api/src/services/redis.ts)
   const client = new Redis(process.env.REDIS_URL);
   ```

Running Different Code Layers
---------------------------
1. Development Mode:
   ```bash
   # Frontend development server
   npm run dev
   # Watches for changes in src/ directory
   # Enables hot reloading
   # Runs on http://localhost:3000

   # Backend development server
   npm run start:backend
   # Watches for changes in api/ directory
   # Enables debugging
   # Runs on http://localhost:3001
   ```

2. Production Mode:
   ```bash
   # Build frontend
   npm run build:frontend
   # Generates optimized production build in .next/

   # Build backend
   npm run build:backend
   # Compiles TypeScript to JavaScript in dist/

   # Start production servers
   npm run start
   # Runs optimized builds
   # Enables production optimizations
   ```

3. Test Environment:
   ```bash
   # Run frontend tests
   npm run test:frontend
   # Executes tests in src/__tests__/

   # Run backend tests
   npm run test:backend
   # Executes tests in api/tests/
   ```

Debugging Different Layers
------------------------
1. Frontend Debugging:
   - Use React Developer Tools for component inspection
   - Check Network tab for API calls
   - Use console.log() for component lifecycle

2. Backend Debugging:
   - Use VS Code debugger with launch.json
   - Monitor Redis commands with redis-cli monitor
   - Check API logs in api/logs/

3. WebSocket Debugging:
   - Use Browser's Network tab, WS section
   - Enable WebSocket debug logs
   - Monitor connection status

Performance Optimization
----------------------
1. Frontend Optimization:
   - Implement React.memo() for expensive renders
   - Use proper key props in lists
   - Lazy load components with dynamic imports

2. Backend Optimization:
   - Implement proper caching strategies
   - Use database indexing
   - Implement rate limiting

3. WebSocket Optimization:
   - Implement heartbeat mechanism
   - Handle reconnection logic
   - Batch updates when possible

Key Features
-----------
1. Gaming Components:
   - Roulette Game
   - Blackjack Game
   - Poker Game
   - Dice Game
   - Pool Game

2. Admin Features:
   - User Management
   - Analytics Dashboard
   - Blocked Entities Management
   - Game Management

3. Raffle System:
   - Multiple tier support (Bronze to Jackpot)
   - Prize management
   - Ticket system

4. Payment Integration:
   - Bitcoin payment support
   - Token system
   - Payment verification

Setup Instructions
----------------
1. Install Dependencies:
   ```bash
   npm install
   ```

2. Environment Setup:
   Create a .env file with the following variables:
   ```
   NEXT_PUBLIC_API_URL=http://localhost:3000
   REDIS_URL=your_redis_url
   BITCOIN_NETWORK=testnet
   JWT_SECRET=your_jwt_secret
   ```

3. Database Setup:
   The project uses Redis for data storage. Ensure Redis is installed and running.

Running the Application
---------------------
1. Start the Backend:
   ```bash
   npm run start:backend
   ```
   This will start the API service on port 3001

2. Start the Frontend:
   ```bash
   npm run start:frontend
   ```
   This will start the Next.js development server on port 3000

Component Documentation
---------------------
1. Game Components:
   - RouletteGame: Interactive roulette wheel with betting system
   - BlackjackGame: Card game with dealer interaction
   - PokerGame: Multiplayer poker implementation
   - DiceGame: Simple dice rolling game
   - PoolGame: Physics-based pool game

2. Admin Components:
   - UserManagement: User CRUD operations
   - Analytics: Game statistics and user metrics
   - BlockedEntities: Management of restricted items/users

3. Common Components:
   - Button: Customizable button component
   - Card: Container component
   - Layout: Page layout wrapper

API Documentation
---------------
1. User Endpoints:
   - POST /api/auth/login
   - POST /api/auth/register
   - GET /api/users/profile
   - PATCH /api/users/update

2. Game Endpoints:
   - GET /api/games/list
   - POST /api/games/start
   - POST /api/games/end
   - GET /api/games/statistics

3. Payment Endpoints:
   - POST /api/payments/create-bitcoin-payment
   - GET /api/payments/verify-bitcoin-payment
   - POST /api/payments/process-token-purchase

WebSocket Integration
-------------------
The platform uses WebSocket connections for real-time game updates:
1. Connection URL: ws://localhost:3000/api/ws/games
2. Events:
   - GAME_STATE: Current game state
   - PLAYER_ACTION: Player moves/bets
   - GAME_RESULT: Round results

Redis Data Structure
------------------
1. User Data:
   - user:{id}:profile - User profile data
   - user:{id}:balance - User token balance
   - user:{id}:games - User game history

2. Game Data:
   - game:{id}:state - Current game state
   - game:{id}:players - Active players
   - game:{id}:bets - Active bets

3. Raffle Data:
   - raffle:{id} - Raffle details
   - raffle:tickets - Ticket assignments

Security Features
---------------
1. Authentication:
   - JWT-based authentication
   - Session management
   - Role-based access control

2. Payment Security:
   - Bitcoin payment verification
   - Double verification system
   - Secure token management

3. Game Security:
   - Server-side validation
   - Anti-cheat measures
   - Rate limiting

Development Guidelines
--------------------
1. Code Style:
   - Use TypeScript for type safety
   - Follow React hooks patterns
   - Implement proper error handling

2. Testing:
   - Write unit tests for utilities
   - Component testing with React Testing Library
   - API endpoint testing

3. Performance:
   - Implement proper caching strategies
   - Optimize component rendering
   - Use proper WebSocket management

Troubleshooting
-------------
1. Common Issues:
   - Redis connection errors: Check Redis server status
   - WebSocket disconnections: Check network stability
   - Payment verification failures: Check Bitcoin network status

2. Debug Tools:
   - React Developer Tools
   - Network monitoring
   - Redis CLI for database inspection

Contact & Support
---------------
For technical support or questions:
1. Check the documentation first
2. Contact the development team
3. Submit issues through the proper channels 