import React, { useState } from 'react';
import StoryItem from './StoryItem';
import StoryModal from './StoryMode';

// Types
interface User {
  name: string;
  iconUrl: string;
}

interface Story {
  id: string;
  user: User;
  content?: string;
  imageUrl?: string;
  timestamp?: string;
}

interface StoryReelProps {
  stories: Story[];
  onStoryClick?: (story: Story) => void;
}

// Component to display stories horizontally
const StoryReel: React.FC<StoryReelProps> = ({ stories, onStoryClick }) => {
  const [selectedStory, setSelectedStory] = useState<Story | null>(null);

  if (!stories || stories.length === 0) {
    return null; // Don't render if no stories
  }

  const handleStoryClick = (story: Story) => {
    setSelectedStory(story);
    
    // Call the optional callback if provided
    if (onStoryClick) {
      onStoryClick(story);
    }
  };

  const handleCloseModal = () => {
    setSelectedStory(null);
  };

  return (
    <>
      <div className="story-reel" role="list" aria-label="Stories">
        {stories.map(story => (
          <div key={story.id} role="listitem">
            <StoryItem 
              story={story} 
              onStoryClick={handleStoryClick}
            />
          </div>
        ))}
      </div>
      
      {/* Story Modal */}
      {selectedStory && (
        <StoryModal 
          story={selectedStory} 
          onClose={handleCloseModal}
        />
      )}
    </>
  );
};

export default StoryReel;
