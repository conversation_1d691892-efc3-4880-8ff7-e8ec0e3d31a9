import { execSync } from 'child_process';
import { BackupService } from '@/services/backup';
import { PrismaClient } from '@prisma/client';
import { redis } from '@/utils/redis';

const prisma = new PrismaClient();
const backupService = BackupService.getInstance();

async function runPreDeploymentChecks() {
  console.log('🔍 Running pre-deployment checks...\n');

  try {
    // Check database connection
    await prisma.$connect();
    console.log('✅ Database connection successful');

    // Check Redis connection
    await redis.ping();
    console.log('✅ Redis connection successful');

    // Run tests
    execSync('npm run test', { stdio: 'inherit' });
    console.log('✅ Tests passed');

    // Run type checking
    execSync('npx tsc --noEmit', { stdio: 'inherit' });
    console.log('✅ Type checking passed');

    // Run linting
    execSync('npm run lint', { stdio: 'inherit' });
    console.log('✅ Linting passed');

    // Create backup
    await backupService.performBackup('full');
    console.log('✅ Backup created');

    return true;
  } catch (error) {
    console.error('❌ Pre-deployment checks failed:', error);
    return false;
  }
}

async function runDatabaseMigrations() {
  console.log('\n🔄 Running database migrations...');
  
  try {
    execSync('npx prisma migrate deploy', { stdio: 'inherit' });
    console.log('✅ Database migrations successful');
    return true;
  } catch (error) {
    console.error('❌ Database migrations failed:', error);
    return false;
  }
}

async function buildApplication() {
  console.log('\n🏗️ Building application...');
  
  try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ Build successful');
    return true;
  } catch (error) {
    console.error('❌ Build failed:', error);
    return false;
  }
}

async function deployToProduction() {
  console.log('\n🚀 Deploying to production...');
  
  try {
    // Push to production branch
    execSync('git push origin main', { stdio: 'inherit' });
    console.log('✅ Code pushed to production');

    // Deploy using Vercel CLI
    execSync('vercel --prod', { stdio: 'inherit' });
    console.log('✅ Deployment successful');

    return true;
  } catch (error) {
    console.error('❌ Deployment failed:', error);
    return false;
  }
}

async function runPostDeploymentChecks() {
  console.log('\n🔍 Running post-deployment checks...');
  
  try {
    // Check production API health
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/health`);
    if (!response.ok) {
      throw new Error(`Health check failed: ${response.statusText}`);
    }
    console.log('✅ Production API health check passed');

    // Monitor deployment for 5 minutes
    console.log('👀 Monitoring deployment for 5 minutes...');
    let checkCount = 0;
    const interval = setInterval(async () => {
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/health`);
        if (!response.ok) {
          console.error(`❌ Health check failed at ${new Date().toISOString()}`);
        } else {
          console.log(`✅ Health check passed at ${new Date().toISOString()}`);
        }
      } catch (error) {
        console.error(`❌ Health check error at ${new Date().toISOString()}:`, error);
      }

      checkCount++;
      if (checkCount >= 30) { // 30 checks, one every 10 seconds
        clearInterval(interval);
        console.log('✅ Deployment monitoring completed');
      }
    }, 10000);

    return true;
  } catch (error) {
    console.error('❌ Post-deployment checks failed:', error);
    return false;
  }
}

async function deploy() {
  console.log('🚀 Starting deployment process...\n');

  try {
    // Run pre-deployment checks
    if (!await runPreDeploymentChecks()) {
      throw new Error('Pre-deployment checks failed');
    }

    // Run database migrations
    if (!await runDatabaseMigrations()) {
      throw new Error('Database migrations failed');
    }

    // Build application
    if (!await buildApplication()) {
      throw new Error('Application build failed');
    }

    // Deploy to production
    if (!await deployToProduction()) {
      throw new Error('Deployment to production failed');
    }

    // Run post-deployment checks
    if (!await runPostDeploymentChecks()) {
      throw new Error('Post-deployment checks failed');
    }

    console.log('\n✨ Deployment completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('\n💥 Deployment failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    await redis.quit();
  }
}

// Run deployment
deploy(); 