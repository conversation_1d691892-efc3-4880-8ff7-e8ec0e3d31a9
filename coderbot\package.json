{"name": "coderbot-ai", "version": "1.0.0", "description": "AI-powered coding assistant with Claude 3.5 Sonnet integration", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "test": "jest", "lint": "eslint src --ext .ts", "format": "prettier --write \"src/**/*.ts\"", "prepare": "npm run build", "prepublishOnly": "npm test && npm run lint", "dev": "tsc -w"}, "keywords": ["ai", "coding", "assistant", "claude", "anthropic"], "author": "Your Name", "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.18.1", "ioredis": "^5.3.2"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^20.2.5", "@typescript-eslint/eslint-plugin": "^5.59.8", "@typescript-eslint/parser": "^5.59.8", "eslint": "^8.41.0", "jest": "^29.5.0", "prettier": "^2.8.8", "testcontainers": "^10.0.0", "ts-jest": "^29.1.0", "typescript": "^5.1.3"}, "files": ["dist/**/*"]}