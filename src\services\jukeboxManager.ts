import { getRedisClient } from '../utils/redis';
import { GroupType } from '../types/loom';
import { LoomCoinManager } from './loomCoinManager';

interface Song {
  id: string;
  title: string;
  artist: string;
  duration: number;
  streamUrl: string;
}

interface PlaylistEntry {
  song: Song;
  userId: string;
  timestamp: number;
}

export class JukeboxManager {
  private static readonly SONG_COST = 10; // tokens
  private static readonly PLAYLIST_KEY = 'jukebox:playlists';
  private static readonly USER_HISTORY_KEY = 'jukebox:user_history';
  private static readonly STREAM_COUNT_KEY = 'jukebox:stream_counts';

  private static readonly PLAYLISTS: Record<GroupType, Song[]> = {
    [GroupType.FOUR_JUMPS]: [
      // Add default songs for FOUR_JUMPS playlist
    ],
    [GroupType.FULL_RIDE]: [
      // Add default songs for FULL_RIDE playlist
    ]
  };

  static async getAvailableSongs(groupType: GroupType): Promise<Song[]> {
    return this.PLAYLISTS[groupType] || [];
  }

  static async addSongToPlaylist(
    groupId: string,
    userId: string,
    songId: string
  ): Promise<PlaylistEntry | null> {
    const redis = await getRedisClient();

    // Check if user has enough tokens
    const userBalance = await LoomCoinManager.getInGameBalance(userId);
    if (userBalance < this.SONG_COST) {
      throw new Error('Insufficient tokens');
    }

    // Get song details
    const group = await redis.hget('loom_groups', groupId);
    if (!group) throw new Error('Group not found');
    
    const groupData = JSON.parse(group);
    const availableSongs = await this.getAvailableSongs(groupData.type);
    const song = availableSongs.find(s => s.id === songId);
    
    if (!song) throw new Error('Song not found');

    // Deduct tokens
    await LoomCoinManager.moveTokensInGame(
      userId,
      'SYSTEM',
      this.SONG_COST,
      'GROUP_ENTRY',
      groupId
    );

    // Add to playlist
    const entry: PlaylistEntry = {
      song,
      userId,
      timestamp: Date.now()
    };

    await redis.rpush(
      `${this.PLAYLIST_KEY}:${groupId}`,
      JSON.stringify(entry)
    );

    // Add to user history
    await redis.rpush(
      `${this.USER_HISTORY_KEY}:${userId}`,
      JSON.stringify({
        ...entry,
        groupId
      })
    );

    // Increment stream count for artist
    await redis.hincrby(
      this.STREAM_COUNT_KEY,
      song.artist,
      1
    );

    return entry;
  }

  static async getGroupPlaylist(groupId: string): Promise<PlaylistEntry[]> {
    const redis = await getRedisClient();
    const playlist = await redis.lrange(
      `${this.PLAYLIST_KEY}:${groupId}`,
      0,
      -1
    );
    return playlist.map(entry => JSON.parse(entry));
  }

  static async getUserHistory(userId: string): Promise<(PlaylistEntry & { groupId: string })[]> {
    const redis = await getRedisClient();
    const history = await redis.lrange(
      `${this.USER_HISTORY_KEY}:${userId}`,
      0,
      -1
    );
    return history.map(entry => JSON.parse(entry));
  }

  static async getArtistStreamCount(artist: string): Promise<number> {
    const redis = await getRedisClient();
    const count = await redis.hget(this.STREAM_COUNT_KEY, artist);
    return count ? parseInt(count) : 0;
  }
} 