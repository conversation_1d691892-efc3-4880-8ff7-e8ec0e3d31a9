export interface BaseWebSocketMessage {
  type: string;
}

export interface PlayerMessage extends BaseWebSocketMessage {
  type: 'PLAYER_MESSAGE';
  messageType: 'text' | 'voice';
  content: string;
}

export interface GameActionMessage extends BaseWebSocketMessage {
  type: 'PLACE_BET' | 'SPIN_WHEEL' | 'PLAYER_HIT' | 'PLAYER_STAND' | 'PLAYER_DOUBLE';
  betType?: string;
  numbers?: number[];
  amount?: number;
}

export interface DealerMessage extends BaseWebSocketMessage {
  type: 'DEALER_MESSAGE' | 'DEALER_SHUFFLE' | 'CLOSE_TABLE';
  messageType?: 'text' | 'voice';
  content?: string;
  targetPlayer?: string | null;
}

export type WebSocketMessage = PlayerMessage | GameActionMessage | DealerMessage; 