import { redis } from '../utils/redis';
import { NotificationService } from './notification';

interface SystemMetrics {
  activeUsers: number;
  pendingApprovals: number;
  failedLogins: number;
  avgResponseTime: number;
  cpuUsage: number;
  memoryUsage: number;
  storageUsage: number;
}

interface ActivityLog {
  id: string;
  userId: string;
  action: string;
  ip: string;
  userAgent: string;
  timestamp: number;
  metadata: Record<string, any>;
}

export class MonitoringService {
  static async logActivity(activity: Omit<ActivityLog, 'id' | 'timestamp'>) {
    const id = `activity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const log: ActivityLog = {
      ...activity,
      id,
      timestamp: Date.now()
    };

    // Store activity log
    await redis.set(`activity:${id}`, JSON.stringify(log));
    await redis.zadd('activities:all', Date.now(), id);

    // Add to user-specific activity set
    await redis.zadd(`user:${activity.userId}:activities`, Date.now(), id);

    // Check for suspicious patterns
    await this.checkSuspiciousActivity(activity);

    return log;
  }

  static async getSystemMetrics(): Promise<SystemMetrics> {
    const [
      activeUsers,
      pendingApprovals,
      failedLogins
    ] = await Promise.all([
      redis.scard('users:active'),
      redis.scard('pending:users'),
      redis.get('metrics:failed_logins')
    ]);

    // Get system metrics (implement based on your infrastructure)
    const systemMetrics = await this.getSystemResourceMetrics();

    return {
      activeUsers: Number(activeUsers),
      pendingApprovals: Number(pendingApprovals),
      failedLogins: Number(failedLogins || 0),
      ...systemMetrics
    };
  }

  private static async checkSuspiciousActivity(activity: ActivityLog) {
    const timeWindow = 5 * 60 * 1000; // 5 minutes
    const threshold = 10; // Number of attempts

    // Check for repeated actions from same IP
    const recentActivities = await redis.zrangebyscore(
      `ip:${activity.ip}:activities`,
      Date.now() - timeWindow,
      Date.now()
    );

    if (recentActivities.length >= threshold) {
      await NotificationService.create({
        type: 'SUSPICIOUS_ACTIVITY',
        priority: 'HIGH',
        title: 'Suspicious Activity Detected',
        message: `Multiple actions detected from IP ${activity.ip}`,
        metadata: {
          ip: activity.ip,
          userId: activity.userId,
          activityCount: recentActivities.length,
          timeWindow: '5 minutes'
        }
      });

      // Add IP to watch list
      await redis.sadd('watchlist:ips', activity.ip);
    }

    // Store activity for IP tracking
    await redis.zadd(
      `ip:${activity.ip}:activities`,
      Date.now(),
      JSON.stringify(activity)
    );

    // Cleanup old IP activities
    await redis.zremrangebyscore(
      `ip:${activity.ip}:activities`,
      0,
      Date.now() - timeWindow
    );
  }

  private static async getSystemResourceMetrics() {
    // Implement based on your infrastructure
    // Example using node-os-utils or similar
    return {
      avgResponseTime: 150, // ms
      cpuUsage: 45.5, // percentage
      memoryUsage: 62.3, // percentage
      storageUsage: 38.7 // percentage
    };
  }

  static async getActivityLogs(
    filters: {
      userId?: string;
      action?: string;
      startTime?: number;
      endTime?: number;
    },
    page = 1,
    limit = 50
  ) {
    const start = (page - 1) * limit;
    const end = start + limit - 1;

    let activityIds;
    if (filters.userId) {
      activityIds = await redis.zrevrange(
        `user:${filters.userId}:activities`,
        start,
        end
      );
    } else {
      activityIds = await redis.zrevrange('activities:all', start, end);
    }

    const activities = await Promise.all(
      activityIds.map(async (id) => {
        const data = await redis.get(`activity:${id}`);
        return data ? JSON.parse(data) : null;
      })
    );

    return activities.filter((activity) => {
      if (!activity) return false;
      if (filters.action && activity.action !== filters.action) return false;
      if (filters.startTime && activity.timestamp < filters.startTime) return false;
      if (filters.endTime && activity.timestamp > filters.endTime) return false;
      return true;
    });
  }
} 