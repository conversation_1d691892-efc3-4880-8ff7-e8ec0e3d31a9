# CoderBot AI

An AI-powered coding assistant built with Claude 3.5 Sonnet. Features include rate limiting, response caching, and concurrent request handling.

## Installation

```bash
npm install coderbot-ai
```

## Usage

```typescript
import { CoderBot } from 'coderbot-ai';

// Initialize CoderBot
const coderBot = new CoderBot({
  apiKey: 'your-anthropic-api-key', // Optional, defaults to ANTHROPIC_API_KEY env var
  model: 'claude-3-sonnet',         // Optional, defaults to claude-3-sonnet
  redisUrl: 'redis://localhost:6379' // Optional, defaults to REDIS_URL env var
});

// Initialize Redis connection
await coderBot.initialize();

// Query the AI
const response = await coderBot.query('Write a function to calculate Fibonacci numbers');
console.log(response.text);

// Clean up resources
await coderBot.cleanup();
```

## Features

- 🤖 Powered by Claude 3.5 Sonnet
- 🚀 Response caching for improved performance
- ⚡ Rate limiting to prevent API abuse
- 🔄 Concurrent request handling
- 📊 Built-in metrics tracking
- 🔒 Type-safe API

## Configuration

### Environment Variables

- `ANTHROPIC_API_KEY`: Your Anthropic API key
- `REDIS_URL`: Redis connection URL (default: redis://localhost:6379)

### Options

```typescript
interface AIOptions {
  maxTokens?: number;      // Maximum tokens to generate
  temperature?: number;    // Response randomness (0-1)
  stopSequences?: string[]; // Stop generation at these sequences
  topP?: number;          // Nucleus sampling parameter
  topK?: number;          // Top-k sampling parameter
  contextWindow?: number; // Maximum context window size
}
```

## Development

```bash
# Install dependencies
npm install

# Run tests
npm test

# Build
npm run build

# Format code
npm run format

# Lint code
npm run lint
```

## Testing

The test suite uses Docker containers for Redis. Make sure you have Docker installed and running.

```bash
npm test
```

## License

MIT 