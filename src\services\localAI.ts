import { LoomGroup, GroupTier } from '../types/loom';

interface AIResponse {
  text: string;
  confidence: number;
  suggestions?: {
    groupId: string;
    reason: string;
    estimatedReward: number;
  }[];
}

interface AIRequest {
  prompt: string;
  context?: any;
  temperature?: number;
  maxTokens?: number;
}

export class LocalAIService {
  private static instance: LocalAIService;
  private readonly baseUrl: string;

  private constructor() {
    this.baseUrl = process.env.LOCAL_AI_URL || 'http://localhost:8080';
  }

  public static getInstance(): LocalAIService {
    if (!LocalAIService.instance) {
      LocalAIService.instance = new LocalAIService();
    }
    return LocalAIService.instance;
  }

  async getBoardSuggestions(
    availableGroups: LoomGroup[],
    userPreferences: {
      preferredTier?: GroupTier;
      maxWaitTime?: number;
      targetHourlyRate?: number;
    }
  ): Promise<AIResponse> {
    const prompt = this.buildSuggestionPrompt(availableGroups, userPreferences);
    
    try {
      const response = await fetch(`${this.baseUrl}/v1/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          temperature: 0.7,
          max_tokens: 500,
          model: 'loom-assistant'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to get AI suggestions');
      }

      const data = await response.json();
      return this.parseSuggestionResponse(data.choices[0].text, availableGroups);
    } catch (error) {
      console.error('LocalAI suggestion error:', error);
      return {
        text: "I'm having trouble analyzing the boards right now. Please try again later.",
        confidence: 0
      };
    }
  }

  async getInfiniteModeStrategy(
    userStats: {
      timeRemaining: { daily: number; weekly: number };
      currentTier: GroupTier;
      averageCompletionTime: number;
    }
  ): Promise<AIResponse> {
    const prompt = this.buildStrategyPrompt(userStats);
    
    try {
      const response = await fetch(`${this.baseUrl}/v1/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          temperature: 0.7,
          max_tokens: 500,
          model: 'loom-assistant'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to get AI strategy');
      }

      const data = await response.json();
      return {
        text: data.choices[0].text,
        confidence: data.choices[0].confidence || 0.8
      };
    } catch (error) {
      console.error('LocalAI strategy error:', error);
      return {
        text: "I'm having trouble generating a strategy right now. Please try again later.",
        confidence: 0
      };
    }
  }

  private buildSuggestionPrompt(
    groups: LoomGroup[],
    preferences: any
  ): string {
    return `
      Analyze these Loom boards and suggest the best options based on:
      User preferences: ${JSON.stringify(preferences)}
      Available boards: ${JSON.stringify(groups)}
      Consider factors like:
      - Wait times and current player count
      - Potential hourly earnings
      - Board completion speed
      - Player activity levels
      Provide specific recommendations with reasoning.
    `;
  }

  private buildStrategyPrompt(userStats: any): string {
    return `
      Create an infinite mode strategy based on:
      User stats: ${JSON.stringify(userStats)}
      Consider:
      - Daily and weekly time limits
      - Optimal board progression
      - Break scheduling
      - Profit maximization
      Provide a detailed plan for the available time.
    `;
  }

  private parseSuggestionResponse(
    response: string,
    availableGroups: LoomGroup[]
  ): AIResponse {
    try {
      // Basic response parsing - enhance based on your model's output format
      const suggestions = availableGroups
        .slice(0, 3)
        .map(group => ({
          groupId: group.id,
          reason: "Based on current activity and potential rewards",
          estimatedReward: group.currentPrizePool / (group.timerDuration * 15) * 3600000
        }));

      return {
        text: response,
        confidence: 0.8,
        suggestions
      };
    } catch (error) {
      console.error('Error parsing AI response:', error);
      return {
        text: response,
        confidence: 0.5
      };
    }
  }

  async customQuery(request: AIRequest): Promise<AIResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/v1/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: request.prompt,
          temperature: request.temperature || 0.7,
          max_tokens: request.maxTokens || 500,
          model: 'loom-assistant',
          context: request.context
        })
      });

      if (!response.ok) {
        throw new Error('Failed to process custom query');
      }

      const data = await response.json();
      return {
        text: data.choices[0].text,
        confidence: data.choices[0].confidence || 0.8
      };
    } catch (error) {
      console.error('LocalAI custom query error:', error);
      return {
        text: "I'm having trouble processing your request right now. Please try again later.",
        confidence: 0
      };
    }
  }
} 