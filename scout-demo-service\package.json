{"name": "@docker/scout-demo-service", "version": "0.1.0", "description": "A boilerplate for Node.js web applications", "repository": {"type": "git", "url": "https://github.com/docker/scout-demo-servixe.git"}, "license": "Apache-2", "scripts": {"start": "node app.js", "test": "nyc mocha --timeout=10000 --exit", "lint": "eslint \"**/*.js\""}, "dependencies": {"express": "4.17.1"}, "devDependencies": {"chai": "^4.2.0", "eslint": "^7.17.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-plugin-chai-friendly": "^0.6.0", "eslint-plugin-import": "^2.22.1", "mocha": "^8.2.1", "nyc": "^15.1.0", "sinon": "^9.2.3", "supertest": "^6.0.1"}, "engines": {"node": ">=10.23.1", "npm": ">=6.14.10"}}