import { DebitCardService } from '../services/payment/debitCard';
import { PrismaClient } from '@prisma/client';
import { redis } from '../utils/redis';
import axios from 'axios';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

const prisma = new PrismaClient();

describe('DebitCardService', () => {
  let debitCardService: DebitCardService;

  beforeAll(async () => {
    debitCardService = DebitCardService.getInstance();
    await redis.flushall();
  });

  afterEach(async () => {
    await redis.flushall();
    jest.clearAllMocks();
  });

  afterAll(async () => {
    await prisma.$disconnect();
    await redis.quit();
  });

  describe('Card Application', () => {
    it('should process valid card application', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+1234567890',
          isVerified: true,
          wallet: {
            create: {
              loomCoinBalance: 2000,
              bitcoinBalance: 0.1
            }
          }
        }
      });

      // Create some transaction history
      await Promise.all(Array(5).fill(0).map((_, i) => 
        prisma.transaction.create({
          data: {
            userId: testUser.id,
            type: 'GAME_WIN',
            amount: 100,
            status: 'COMPLETED'
          }
        })
      ));

      const application = {
        userId: testUser.id,
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        address: {
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          zipCode: '10001',
          country: 'USA'
        },
        ssn: '***********',
        employmentStatus: 'EMPLOYED',
        annualIncome: 75000
      };

      mockedAxios.post.mockResolvedValueOnce({
        data: {
          id: 'test-card-1',
          cardNumber: '****************',
          expiryMonth: 12,
          expiryYear: 2025,
          cvv: '123'
        }
      });

      const cardDetails = await debitCardService.applyForCard(application);

      expect(cardDetails.status).toBe('PENDING');
      expect(cardDetails.cardNumber).toMatch(/^\d{16}$/);
      expect(cardDetails.expiryMonth).toBe(12);
      expect(cardDetails.expiryYear).toBe(2025);
    });

    it('should reject underage applicants', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+1234567891',
          isVerified: true
        }
      });

      const application = {
        userId: testUser.id,
        firstName: 'Young',
        lastName: 'User',
        dateOfBirth: new Date().toISOString().split('T')[0], // Today
        address: {
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          zipCode: '10001',
          country: 'USA'
        },
        ssn: '***********',
        employmentStatus: 'STUDENT',
        annualIncome: 0
      };

      await expect(
        debitCardService.applyForCard(application)
      ).rejects.toThrow('Must be 18 or older to apply for a card');
    });

    it('should reject applications with insufficient account activity', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+**********',
          isVerified: true,
          wallet: {
            create: {
              loomCoinBalance: 500, // Below minimum
              bitcoinBalance: 0
            }
          }
        }
      });

      const application = {
        userId: testUser.id,
        firstName: 'Low',
        lastName: 'Balance',
        dateOfBirth: '1990-01-01',
        address: {
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          zipCode: '10001',
          country: 'USA'
        },
        ssn: '***********',
        employmentStatus: 'EMPLOYED',
        annualIncome: 50000
      };

      await expect(
        debitCardService.applyForCard(application)
      ).rejects.toThrow('Account does not meet minimum requirements');
    });
  });

  describe('Card Activation', () => {
    it('should activate card with valid activation code', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+**********',
          isVerified: true
        }
      });

      // Create pending card
      const card = await prisma.debitCard.create({
        data: {
          userId: testUser.id,
          cardNumber: '****************',
          expiryMonth: 12,
          expiryYear: 2025,
          cvv: '123',
          status: 'PENDING'
        }
      });

      mockedAxios.post.mockResolvedValueOnce({ data: { status: 'success' } });

      await debitCardService.activateCard(testUser.id, card.id, '123456');

      const updatedCard = await prisma.debitCard.findUnique({
        where: { id: card.id }
      });

      expect(updatedCard?.status).toBe('ACTIVE');
    });

    it('should reject invalid activation codes', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+1234567894',
          isVerified: true
        }
      });

      const card = await prisma.debitCard.create({
        data: {
          userId: testUser.id,
          cardNumber: '****************',
          expiryMonth: 12,
          expiryYear: 2025,
          cvv: '123',
          status: 'PENDING'
        }
      });

      mockedAxios.post.mockRejectedValueOnce(new Error('Invalid activation code'));

      await expect(
        debitCardService.activateCard(testUser.id, card.id, 'invalid')
      ).rejects.toThrow();

      const updatedCard = await prisma.debitCard.findUnique({
        where: { id: card.id }
      });

      expect(updatedCard?.status).toBe('PENDING');
    });
  });

  describe('Card Management', () => {
    it('should retrieve card details', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+1234567895',
          isVerified: true
        }
      });

      const card = await prisma.debitCard.create({
        data: {
          userId: testUser.id,
          cardNumber: '****************',
          expiryMonth: 12,
          expiryYear: 2025,
          cvv: '123',
          status: 'ACTIVE'
        }
      });

      mockedAxios.get.mockResolvedValueOnce({ data: { balance: 1000 } });

      const cardDetails = await debitCardService.getCardDetails(testUser.id, card.id);

      expect(cardDetails.cardNumber).toMatch(/^\*{12}\d{4}$/);
      expect(cardDetails.cvv).toBe('***');
      expect(cardDetails.balance).toBe(1000);
    });

    it('should block card when requested', async () => {
      const testUser = await prisma.user.create({
        data: {
          phoneNumber: '+1234567896',
          isVerified: true
        }
      });

      const card = await prisma.debitCard.create({
        data: {
          userId: testUser.id,
          cardNumber: '****************',
          expiryMonth: 12,
          expiryYear: 2025,
          cvv: '123',
          status: 'ACTIVE'
        }
      });

      mockedAxios.post.mockResolvedValueOnce({ data: { status: 'success' } });

      await debitCardService.blockCard(testUser.id, card.id, 'Lost card');

      const updatedCard = await prisma.debitCard.findUnique({
        where: { id: card.id }
      });

      expect(updatedCard?.status).toBe('BLOCKED');
    });

    it('should prevent unauthorized card access', async () => {
      const [user1, user2] = await Promise.all([
        prisma.user.create({
          data: {
            phoneNumber: '+1234567897',
            isVerified: true
          }
        }),
        prisma.user.create({
          data: {
            phoneNumber: '+1234567898',
            isVerified: true
          }
        })
      ]);

      const card = await prisma.debitCard.create({
        data: {
          userId: user1.id,
          cardNumber: '****************',
          expiryMonth: 12,
          expiryYear: 2025,
          cvv: '123',
          status: 'ACTIVE'
        }
      });

      await expect(
        debitCardService.getCardDetails(user2.id, card.id)
      ).rejects.toThrow('Card not found');

      await expect(
        debitCardService.blockCard(user2.id, card.id, 'Unauthorized')
      ).rejects.toThrow('Card not found');
    });
  });
}); 