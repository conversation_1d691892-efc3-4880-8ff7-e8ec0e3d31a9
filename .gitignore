# Dependencies
/node_modules
/.pnp
.pnp.js

# Production build
/build
/dist

# Environment variables
.env*.local
.env.development
.env.production
.env

# IDE
.idea/
.vscode/
*.swp
*.swo

# Misc
.DS_Store
*.pem
Thumbs.db

# Logs
logs
*.log

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Testing
/coverage
/cypress/videos
/cypress/screenshots
.nyc_output

# Next.js
/.next/
/out/

# Vercel
.vercel

# Typescript
*.tsbuildinfo
next-env.d.ts

# Prisma
/prisma/migrations/
/prisma/*.db
/prisma/*.db-journal

# Cache
.cache/
.next/cache/
.eslintcache

# Docker
docker-compose.override.yml
.docker/

# Blockchain
artifacts/
cache/
typechain/
.openzeppelin/
hardhat.config.js.timestamp-*

# Local development
*.local
local.*
.env.local.*

# Temporary files
*.tmp
*.temp
.temp/
tmp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Package manager
yarn.lock
package-lock.json
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# IDE specific files
.idea/
.vscode/
*.sublime-project
*.sublime-workspace
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
