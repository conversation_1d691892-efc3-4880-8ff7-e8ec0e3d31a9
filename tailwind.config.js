/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx}',
    './src/components/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      colors: {
        navy: {
          800: '#1a1f2c',
          900: '#0f1219',
        },
      },
      keyframes: {
        holographic: {
          '0%, 100%': {
            backgroundPosition: '0% 50%',
            filter: 'hue-rotate(0deg) brightness(100%)',
          },
          '50%': {
            backgroundPosition: '100% 50%',
            filter: 'hue-rotate(45deg) brightness(120%)',
          },
        },
        float: {
          '0%, 100%': {
            transform: 'translateY(0) translateX(-50%)',
          },
          '50%': {
            transform: 'translateY(-10px) translateX(-50%)',
          },
        },
        'spin-slow': {
          from: {
            transform: 'rotate(0deg)',
          },
          to: {
            transform: 'rotate(360deg)',
          },
        },
      },
      animation: {
        holographic: 'holographic 3s ease-in-out infinite',
        float: 'float 3s ease-in-out infinite',
        'spin-slow': 'spin-slow 8s linear infinite',
      },
      backgroundSize: {
        '200%': '200% 200%',
      },
      typography: {
        DEFAULT: {
          css: {
            color: '#fff',
            a: {
              color: '#EAB308',
              '&:hover': {
                color: '#CA8A04',
              },
            },
            h1: {
              color: '#fff',
            },
            h2: {
              color: '#fff',
            },
            h3: {
              color: '#fff',
            },
            h4: {
              color: '#fff',
            },
            strong: {
              color: '#fff',
            },
            code: {
              color: '#fff',
            },
            figcaption: {
              color: '#fff',
            },
            blockquote: {
              color: '#fff',
              borderLeftColor: '#EAB308',
            },
          },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
}; 