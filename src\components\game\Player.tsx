import React from 'react';
import UsernameDisplay from '../profile/UsernameDisplay';

// Types
interface PlayerData {
  playerId: string;
  username: string;
  iconUrl: string;
  slotId: string | null;
  backgroundUrl?: string;
}

interface PlayerProps {
  player: PlayerData | null;
  isCurrentPlayer?: boolean;
}

// Player component: Renders icon and optionally username
const Player: React.FC<PlayerProps> = ({ player, isCurrentPlayer = false }) => {
  if (!player) return null;

  // Conditionally add the 'current-player' class
  const containerClasses = `player-container ${isCurrentPlayer ? 'current-player' : ''}`;

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const target = e.target as HTMLImageElement;
    target.style.opacity = '0'; // Hide gracefully if image fails
  };

  return (
    <div className={containerClasses}>
      <img
        src={player.iconUrl || ''}
        alt={player.iconUrl ? `${player.username}'s icon` : 'Player icon'}
        className="player-icon"
        onError={handleImageError}
      />

      {/* Conditionally render UsernameDisplay only if isCurrentPlayer is true */}
      {isCurrentPlayer && (
        <UsernameDisplay username={player.username} />
      )}
    </div>
  );
};

export default Player;
