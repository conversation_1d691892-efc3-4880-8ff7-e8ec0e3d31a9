#!/bin/bash

# Load environment variables
source ../.env.production

# Check if backup date is provided
if [ -z "$1" ]; then
    echo "Please provide backup date in format YYYYMMDD_HHMMSS"
    exit 1
fi

BACKUP_DATE=$1
BACKUP_DIR="/opt/loomloot/backups"

# Verify backup files exist
if [ ! -f "$BACKUP_DIR/db_backup_$BACKUP_DATE.sql.gz" ] || \
   [ ! -f "$BACKUP_DIR/redis_backup_$BACKUP_DATE.rdb" ] || \
   [ ! -f "$BACKUP_DIR/app_backup_$BACKUP_DATE.tar.gz" ]; then
    echo "Backup files for date $BACKUP_DATE not found!"
    exit 1
fi

# Stop running services
echo "Stopping services..."
docker-compose -f ../docker-compose.prod.yml down

# Restore application files
echo "Restoring application files..."
cd ..
tar -xzf "$BACKUP_DIR/app_backup_$BACKUP_DATE.tar.gz"

# Start database and Redis containers
echo "Starting database and Redis containers..."
docker-compose -f docker-compose.prod.yml up -d db redis
sleep 10  # Wait for services to be ready

# Restore PostgreSQL database
echo "Restoring PostgreSQL database..."
gunzip -c "$BACKUP_DIR/db_backup_$BACKUP_DATE.sql.gz" | \
    docker exec -i loomloot_db_1 psql -U $DB_USER $DB_NAME

# Restore Redis data
echo "Restoring Redis data..."
docker cp "$BACKUP_DIR/redis_backup_$BACKUP_DATE.rdb" loomloot_redis_1:/data/dump.rdb
docker exec loomloot_redis_1 redis-cli SHUTDOWN SAVE
sleep 5

# Start remaining services
echo "Starting remaining services..."
docker-compose -f docker-compose.prod.yml up -d

# Verify restoration
echo "Verifying restoration..."
docker-compose -f docker-compose.prod.yml ps
docker exec loomloot_db_1 psql -U $DB_USER -d $DB_NAME -c "\dt"
docker exec loomloot_redis_1 redis-cli DBSIZE

# Create restore report
echo "Creating restore report..."
REPORT_FILE="$BACKUP_DIR/restore_report_$(date +%Y%m%d_%H%M%S).txt"
echo "Restore completed at $(date)" > $REPORT_FILE
echo "Restored from backup date: $BACKUP_DATE" >> $REPORT_FILE
echo "Application version: $(git describe --tags)" >> $REPORT_FILE
echo "Database tables restored: $(docker exec loomloot_db_1 psql -U $DB_USER -d $DB_NAME -c "\dt" | wc -l)" >> $REPORT_FILE
echo "Redis keys restored: $(docker exec loomloot_redis_1 redis-cli DBSIZE)" >> $REPORT_FILE

echo "Restore completed successfully!"
echo "Please check $REPORT_FILE for details." 