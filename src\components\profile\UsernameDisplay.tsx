import React from 'react';

// Types
interface UsernameDisplayProps {
  username: string;
  className?: string;
  maxLength?: number;
  showTooltip?: boolean;
}

// UsernameDisplay component: Renders username using a standard text element
const UsernameDisplay: React.FC<UsernameDisplayProps> = ({ 
  username, 
  className = '',
  maxLength = 12,
  showTooltip = true
}) => {
  // Simple check for empty username
  if (!username || username.trim() === '') {
    return null;
  }

  // Truncate username if it's too long
  const displayUsername = username.length > maxLength 
    ? `${username.substring(0, maxLength)}...` 
    : username;

  // Determine if tooltip should be shown
  const shouldShowTooltip = showTooltip && username.length > maxLength;

  return (
    <div className={`username-display ${className}`.trim()}>
      <span 
        className="username-text"
        title={shouldShowTooltip ? username : undefined}
        aria-label={`Username: ${username}`}
      >
        {displayUsername}
      </span>
    </div>
  );
};

export default UsernameDisplay;
