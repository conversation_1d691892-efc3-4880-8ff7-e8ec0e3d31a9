import { Prisma } from '@prisma/client';

// Announcement Types
export type AnnouncementData = {
  id?: string;
  title: string;
  content: string;
  type: string;
  priority: number;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
  conditions: Prisma.JsonObject;
  createdAt?: Date;
  updatedAt?: Date;
};

export type AnnouncementViewData = {
  id?: string;
  announcementId: string;
  userId: string;
  viewedAt?: Date;
};

// Survey Types
export type SurveyData = {
  id?: string;
  title: string;
  description: string;
  type: string;
  questions: Prisma.JsonObject;
  isActive: boolean;
  startDate: Date;
  endDate: Date;
  conditions: Prisma.JsonObject;
  createdAt?: Date;
  updatedAt?: Date;
};

export type SurveyResponseData = {
  id?: string;
  surveyId: string;
  userId: string;
  answers: Prisma.JsonObject;
  score?: number;
};

// Resource Center Types
export type ResourceCenterData = Prisma.ResourceCenterGetPayload<{
  select: {
    id: true;
    title: true;
    content: true;
    type: true;
    category: true;
    tags: true;
    priority: true;
    isPublished: true;
    views: true;
    createdAt: true;
    updatedAt: true;
  }
}>;

// Onboarding Flow Types
export type OnboardingFlowData = Prisma.OnboardingFlowGetPayload<{
  select: {
    id: true;
    name: true;
    description: true;
    type: true;
    steps: true;
    conditions: true;
    isActive: true;
    priority: true;
    createdAt: true;
    updatedAt: true;
  }
}>;

export type FlowCompletionData = Prisma.FlowCompletionGetPayload<{
  select: {
    id: true;
    flowId: true;
    userId: true;
    completedAt: true;
    lastStepIndex: true;
    feedback: true;
  }
}>; 