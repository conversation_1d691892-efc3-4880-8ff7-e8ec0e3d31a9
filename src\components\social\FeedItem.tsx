import React from 'react';
import { SOUNDS } from '../../constants';
import { playSound } from '../utils/SoundManager';

// Types
interface User {
  name: string;
  iconUrl: string;
}

interface FeedItemData {
  id: number;
  user: User;
  timestamp: string;
  action?: string;
  location?: string;
  content: string;
  imageUrl?: string;
  likes: number;
  comments: number;
  shares: number;
}

interface FeedItemProps {
  item: FeedItemData;
}

// Simple placeholder for a single feed item
const FeedItem: React.FC<FeedItemProps> = ({ item }) => {
  const handleLike = () => {
    playSound(SOUNDS.UI_CLICK_BUTTON_01);
    // TODO: Implement like functionality
  };

  const handleComment = () => {
    playSound(SOUNDS.UI_CLICK_BUTTON_01);
    // TODO: Implement comment functionality
  };

  const handleShare = () => {
    playSound(SOUNDS.UI_CLICK_BUTTON_01);
    // TODO: Implement share functionality
  };

  const handleOptions = () => {
    playSound(SOUNDS.UI_CLICK_MENU_SELECT);
    // TODO: Implement options menu
  };

  return (
    <div className="feed-item">
      <div className="feed-item-header">
        <img 
          src={item.user.iconUrl} 
          alt={item.user.name} 
          className="feed-item-user-icon" 
        />
        <div className="feed-item-user-info">
          <span className="feed-item-username">{item.user.name}</span>
          {item.action && <span className="feed-item-action"> {item.action}</span>}
          {item.location && <span className="feed-item-location"> is in {item.location}</span>}
          <span className="feed-item-timestamp">{item.timestamp}</span>
        </div>
        <button className="feed-item-options" onClick={handleOptions}>
          ...
        </button>
      </div>
      
      <div className="feed-item-content">
        <p>{item.content}</p>
        {item.imageUrl && (
          <img 
            src={item.imageUrl} 
            alt="Feed content" 
            className="feed-item-image"
          />
        )}
      </div>
      
      <div className="feed-item-actions">
        <button onClick={handleLike}>
          👍 {item.likes}
        </button>
        <button onClick={handleComment}>
          💬 {item.comments}
        </button>
        <button onClick={handleShare}>
          🔗 {item.shares}
        </button>
      </div>
    </div>
  );
};

export default FeedItem;
