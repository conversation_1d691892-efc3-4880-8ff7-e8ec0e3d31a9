import { LoomGroupManager } from './loomGroupManager';
import { GroupTier, GroupType, SpotPosition, LoomGroup } from '../types/loom';
import { getRedisClient } from '../utils/redis';

interface SimulationMetrics {
  averageCompletionTime: number;
  successRate: number;
  averageReward: number;
  userSatisfactionScore: number;
  systemLoadScore: number;
  groupBalanceScore: number;
  tierUtilization: Record<GroupTier, number>;
  averageWaitTime: number;
  peakConcurrentUsers: number;
  resourceUtilization: number;
  networkLatency: number;
  errorRate: number;
  userRetentionRate: number;
}

interface SimulationConfig {
  userCount: number;
  duration: number; // in milliseconds
  userBehaviorVariance: number; // 0-1, how random user behavior is
  systemLoadFactor: number; // 0-1, simulated system load
  tierDistribution: Record<GroupTier, number>; // percentage distribution across tiers
  scenario: SimulationScenario;
  networkCondition: NetworkCondition;
  userPatterns: UserPattern[];
  errorProbability: number;
}

enum SimulationScenario {
  NORMAL = 'NORMAL',
  HIGH_LOAD = 'HIGH_LOAD',
  FLASH_CROWD = 'FLASH_CROWD',
  NETWORK_INSTABILITY = 'NETWORK_INSTABILITY',
  MIXED_TIER_PREFERENCE = 'MIXED_TIER_PREFERENCE'
}

enum NetworkCondition {
  OPTIMAL = 'OPTIMAL',
  DEGRADED = 'DEGRADED',
  UNSTABLE = 'UNSTABLE',
  REGIONAL_ISSUES = 'REGIONAL_ISSUES'
}

enum UserPattern {
  REGULAR = 'REGULAR',
  AGGRESSIVE = 'AGGRESSIVE',
  CASUAL = 'CASUAL',
  BOT_LIKE = 'BOT_LIKE',
  MIXED = 'MIXED'
}

interface TrendAnalysis {
  trend: 'improving' | 'stable' | 'degrading';
  confidence: number;
  velocityOfChange: number;
}

interface PerformanceInsights {
  systemHealth: {
    score: number;
    bottlenecks: string[];
    recommendations: string[];
  };
  userExperience: {
    score: number;
    painPoints: string[];
    improvements: string[];
  };
  resourceEfficiency: {
    score: number;
    utilization: Record<string, number>;
    optimizations: string[];
  };
  trends: Record<string, TrendAnalysis>;
}

export class LoomGroupSimulator {
  private static readonly METRICS_KEY = 'loom_group_metrics';
  private static metrics: SimulationMetrics[] = [];

  static async runSimulation(config: SimulationConfig): Promise<{
    metrics: SimulationMetrics;
    insights: PerformanceInsights;
  }> {
    const metrics = await this.runBaseSimulation(config);
    const insights = this.calculatePerformanceInsights(metrics, this.metrics);
    return { metrics, insights };
  }

  private static async runBaseSimulation(config: SimulationConfig): Promise<SimulationMetrics> {
    const metrics: SimulationMetrics = {
      averageCompletionTime: 0,
      successRate: 0,
      averageReward: 0,
      userSatisfactionScore: 0,
      systemLoadScore: 0,
      groupBalanceScore: 0,
      tierUtilization: Object.values(GroupTier).reduce((acc, tier) => {
        if (typeof tier === 'number') {
          acc[tier] = 0;
        }
        return acc;
      }, {} as Record<GroupTier, number>),
      averageWaitTime: 0,
      peakConcurrentUsers: 0,
      resourceUtilization: 0,
      networkLatency: 0,
      errorRate: 0,
      userRetentionRate: 0
    };

    // Apply scenario-specific configurations
    this.applyScenarioConfig(config);

    // Generate simulated users with patterns
    const users = await this.generateSimulatedUsers(config);
    
    // Process users in batches with network conditions
    const batchResults = await this.processBatch(users, config);
    
    // Update metrics based on simulation results
    metrics.averageCompletionTime = batchResults.completionTimes.reduce((a, b) => a + b, 0) / batchResults.completionTimes.length;
    metrics.successRate = batchResults.successfulMoves / batchResults.totalMoves;
    metrics.averageReward = batchResults.totalRewards / users.length;
    metrics.peakConcurrentUsers = batchResults.peakConcurrentUsers;
    metrics.averageWaitTime = batchResults.totalWaitTime / users.length;
    metrics.errorRate = batchResults.errorCount / batchResults.totalOperations;
    metrics.userRetentionRate = batchResults.retainedUsers / users.length;
    metrics.networkLatency = this.calculateNetworkLatency(config.networkCondition);
    metrics.resourceUtilization = this.calculateResourceUtilization(batchResults);
    metrics.groupBalanceScore = this.calculateGroupBalance(batchResults.groupDistribution);
    
    // Update tier utilization
    Object.entries(batchResults.tierUsage).forEach(([tier, usage]) => {
      metrics.tierUtilization[Number(tier)] = usage / users.length;
    });

    // Calculate satisfaction and system load scores
    metrics.userSatisfactionScore = this.calculateSatisfactionScore(metrics);
    metrics.systemLoadScore = this.calculateSystemLoadScore(config);

    // Store metrics for future reference
    this.metrics.push(metrics);
    await this.storeMetrics(metrics);

    return metrics;
  }

  private static async storeMetrics(metrics: SimulationMetrics): Promise<void> {
    const redis = await getRedisClient();
    const existingMetrics = await redis.get(this.METRICS_KEY);
    const metricsArray = existingMetrics ? JSON.parse(existingMetrics) : [];
    metricsArray.push(metrics);
    await redis.set(this.METRICS_KEY, JSON.stringify(metricsArray.slice(-100))); // Keep last 100 metrics
  }

  private static async processBatch(
    users: string[],
    config: SimulationConfig
  ): Promise<{
    completedGroups: number;
    totalRewards: number;
    successfulMoves: number;
    totalMoves: number;
    completionTimes: number[];
    totalWaitTime: number;
    peakConcurrentUsers: number;
    errorCount: number;
    totalOperations: number;
    retainedUsers: number;
    groupDistribution: Record<string, number>;
    tierUsage: Record<string, number>;
  }> {
    const startTime = Date.now();
    const results = {
      completedGroups: 0,
      totalRewards: 0,
      successfulMoves: 0,
      totalMoves: 0,
      completionTimes: [] as number[],
      totalWaitTime: 0,
      peakConcurrentUsers: 0,
      errorCount: 0,
      totalOperations: 0,
      retainedUsers: 0,
      groupDistribution: {} as Record<string, number>,
      tierUsage: {} as Record<string, number>
    };

    let currentUsers = 0;
    let currentWaitTime = 0;
    let peakUsers = 0;

    for (const userId of users) {
      const tier = this.selectTierForUser(config.tierDistribution);
      const group = await LoomGroupManager.getOrCreateAvailableGroup(tier);
      
      try {
        await LoomGroupManager.joinGroup(userId, group.id);
        results.successfulMoves++;
        results.totalMoves++;
        results.totalRewards += 100;
        results.completionTimes.push(Date.now() - startTime);
        results.totalWaitTime += currentWaitTime;
        currentWaitTime = 0;
        currentUsers++;
        results.groupDistribution[tier] = (results.groupDistribution[tier] || 0) + 1;
        results.tierUsage[tier] = (results.tierUsage[tier] || 0) + 1;
      } catch (error) {
        console.error(`Simulation error for user ${userId}:`, error);
        results.errorCount++;
        results.totalMoves++;
        results.totalOperations++;
      }

      currentWaitTime++;
      peakUsers = Math.max(peakUsers, currentUsers);
    }

    results.peakConcurrentUsers = peakUsers;
    results.retainedUsers = currentUsers;

    return results;
  }

  private static async generateSimulatedUsers(config: SimulationConfig): Promise<string[]> {
    const users: string[] = [];
    for (let i = 0; i < config.userCount; i++) {
      users.push(`sim_user_${Date.now()}_${i}`);
    }
    return users;
  }

  private static selectTierForUser(distribution: Record<GroupTier, number>): GroupTier {
    const random = Math.random();
    let cumulativeProbability = 0;
    
    for (const [tier, probability] of Object.entries(distribution)) {
      cumulativeProbability += probability;
      if (random <= cumulativeProbability) {
        return Number(tier) as GroupTier;
      }
    }
    
    return GroupTier.STARTER;
  }

  private static calculateSatisfactionScore(metrics: SimulationMetrics): number {
    return (
      metrics.successRate * 0.4 +
      (1 / (metrics.averageCompletionTime / 60000)) * 0.3 + // Convert to minutes
      (metrics.averageReward / 1000) * 0.3
    );
  }

  private static calculateSystemLoadScore(config: SimulationConfig): number {
    return Math.min(
      1,
      (config.userCount / 500000) * 0.6 +
      config.systemLoadFactor * 0.4
    );
  }

  private static applyScenarioConfig(config: SimulationConfig) {
    switch (config.scenario) {
      case SimulationScenario.HIGH_LOAD:
        config.systemLoadFactor *= 1.5;
        config.userCount = Math.min(500000, config.userCount * 2);
        break;
      case SimulationScenario.FLASH_CROWD:
        config.userBehaviorVariance *= 1.3;
        config.systemLoadFactor *= 2;
        break;
      case SimulationScenario.NETWORK_INSTABILITY:
        config.networkCondition = NetworkCondition.UNSTABLE;
        config.errorProbability *= 1.5;
        break;
      case SimulationScenario.MIXED_TIER_PREFERENCE:
        // Adjust tier distribution for more varied preferences
        Object.keys(config.tierDistribution).forEach(tier => {
          config.tierDistribution[tier] = 1 / Object.keys(config.tierDistribution).length;
        });
        break;
    }
  }

  private static calculateNetworkLatency(condition: NetworkCondition): number {
    const baseLatency = 50; // 50ms base latency
    switch (condition) {
      case NetworkCondition.OPTIMAL:
        return baseLatency;
      case NetworkCondition.DEGRADED:
        return baseLatency * 2;
      case NetworkCondition.UNSTABLE:
        return baseLatency * (2 + Math.random() * 3);
      case NetworkCondition.REGIONAL_ISSUES:
        return baseLatency * (3 + Math.random() * 5);
      default:
        return baseLatency;
    }
  }

  private static calculateResourceUtilization(results: any): number {
    const maxResources = 100;
    const usedResources = results.peakConcurrentUsers * 0.1 +
      results.activeGroups * 0.2 +
      (results.networkLatency / 1000) * 0.3;
    return Math.min(1, usedResources / maxResources);
  }

  private static calculateGroupBalance(distribution: Record<string, number>): number {
    const values = Object.values(distribution);
    const average = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((acc, val) => acc + Math.pow(val - average, 2), 0) / values.length;
    return 1 - Math.sqrt(variance) / average; // Higher score means better balance
  }

  private static calculatePerformanceInsights(
    metrics: SimulationMetrics,
    history: SimulationMetrics[]
  ): PerformanceInsights {
    const insights: PerformanceInsights = {
      systemHealth: {
        score: 0,
        bottlenecks: [],
        recommendations: []
      },
      userExperience: {
        score: 0,
        painPoints: [],
        improvements: []
      },
      resourceEfficiency: {
        score: 0,
        utilization: {},
        optimizations: []
      },
      trends: {}
    };

    // Calculate system health
    insights.systemHealth.score = this.calculateSystemHealthScore(metrics);
    insights.systemHealth.bottlenecks = this.identifySystemBottlenecks(metrics);
    insights.systemHealth.recommendations = this.generateSystemRecommendations(metrics);

    // Calculate user experience
    insights.userExperience.score = this.calculateUserExperienceScore(metrics);
    insights.userExperience.painPoints = this.identifyUserPainPoints(metrics);
    insights.userExperience.improvements = this.generateUserExperienceImprovements(metrics);

    // Calculate resource efficiency
    insights.resourceEfficiency.score = this.calculateResourceEfficiencyScore(metrics);
    insights.resourceEfficiency.utilization = this.calculateResourceUtilizationBreakdown(metrics);
    insights.resourceEfficiency.optimizations = this.generateResourceOptimizations(metrics);

    // Calculate trends
    insights.trends = this.analyzeTrends(metrics, history);

    return insights;
  }

  private static calculateSystemHealthScore(metrics: SimulationMetrics): number {
    return (
      (1 - metrics.errorRate) * 0.3 +
      (1 - metrics.networkLatency / 1000) * 0.2 +
      metrics.successRate * 0.3 +
      (1 - metrics.resourceUtilization) * 0.2
    );
  }

  private static identifySystemBottlenecks(metrics: SimulationMetrics): string[] {
    const bottlenecks: string[] = [];
    
    if (metrics.networkLatency > 200) {
      bottlenecks.push(`High network latency: ${Math.round(metrics.networkLatency)}ms`);
    }
    
    if (metrics.errorRate > 0.05) {
      bottlenecks.push(`High error rate: ${(metrics.errorRate * 100).toFixed(1)}%`);
    }
    
    if (metrics.resourceUtilization > 0.8) {
      bottlenecks.push(`High resource utilization: ${(metrics.resourceUtilization * 100).toFixed(1)}%`);
    }

    const totalUsers = this.metrics.length > 0 ? this.metrics[this.metrics.length - 1].peakConcurrentUsers : 0;
    if (metrics.peakConcurrentUsers / totalUsers > 0.9) {
      bottlenecks.push('Near maximum concurrent user capacity');
    }

    return bottlenecks;
  }

  private static calculateUserExperienceScore(metrics: SimulationMetrics): number {
    return (
      metrics.userSatisfactionScore * 0.4 +
      (1 - metrics.averageWaitTime / 10000) * 0.2 +
      metrics.userRetentionRate * 0.4
    );
  }

  private static identifyUserPainPoints(metrics: SimulationMetrics): string[] {
    const painPoints: string[] = [];

    if (metrics.averageWaitTime > 3000) {
      painPoints.push(`Long wait times: ${Math.round(metrics.averageWaitTime)}ms average`);
    }

    if (metrics.userRetentionRate < 0.7) {
      painPoints.push(`Low user retention: ${(metrics.userRetentionRate * 100).toFixed(1)}%`);
    }

    Object.entries(metrics.tierUtilization).forEach(([tier, utilization]) => {
      if (utilization < 0.3) {
        painPoints.push(`Low tier ${tier} utilization: ${(utilization * 100).toFixed(1)}%`);
      }
    });

    return painPoints;
  }

  private static calculateResourceEfficiencyScore(metrics: SimulationMetrics): number {
    const simulationDuration = this.metrics.length > 0 ? this.metrics[this.metrics.length - 1].averageCompletionTime : 0;
    return (
      (1 - Math.abs(0.7 - metrics.resourceUtilization)) * 0.4 +
      metrics.groupBalanceScore * 0.3 +
      (1 - metrics.averageCompletionTime / (simulationDuration || 1)) * 0.3
    );
  }

  private static analyzeTrends(
    currentMetrics: SimulationMetrics,
    history: SimulationMetrics[]
  ): Record<string, TrendAnalysis> {
    const trends: Record<string, TrendAnalysis> = {};
    const metricsToAnalyze = [
      'userSatisfactionScore',
      'errorRate',
      'resourceUtilization',
      'userRetentionRate'
    ];

    metricsToAnalyze.forEach(metric => {
      const recentHistory = history.slice(-5); // Last 5 simulations
      if (recentHistory.length < 2) {
        trends[metric] = { trend: 'stable', confidence: 1, velocityOfChange: 0 };
        return;
      }

      const values = recentHistory.map(m => m[metric as keyof SimulationMetrics] as number);
      const changes = values.slice(1).map((v, i) => v - values[i]);
      const averageChange = changes.reduce((a, b) => a + b, 0) / changes.length;
      const variance = changes.reduce((a, b) => a + Math.pow(b - averageChange, 2), 0) / changes.length;
      const confidence = 1 / (1 + Math.sqrt(variance));

      trends[metric] = {
        trend: averageChange > 0.01 ? 'improving' : averageChange < -0.01 ? 'degrading' : 'stable',
        confidence,
        velocityOfChange: averageChange
      };
    });

    return trends;
  }

  private static generateSystemRecommendations(metrics: SimulationMetrics): string[] {
    const recommendations: string[] = [];

    if (metrics.networkLatency > 150) {
      recommendations.push('Consider implementing regional server distribution');
    }

    if (metrics.errorRate > 0.03) {
      recommendations.push('Implement circuit breakers and fallback mechanisms');
    }

    if (metrics.resourceUtilization > 0.7) {
      recommendations.push('Enable auto-scaling for high load periods');
    }

    return recommendations;
  }

  private static generateUserExperienceImprovements(metrics: SimulationMetrics): string[] {
    const improvements: string[] = [];

    if (metrics.userSatisfactionScore < 0.8) {
      improvements.push('Improve matchmaking algorithm');
    }

    if (metrics.averageWaitTime > 3000) {
      improvements.push('Optimize group formation speed');
    }

    if (metrics.userRetentionRate < 0.7) {
      improvements.push('Enhance reward distribution mechanism');
    }

    return improvements;
  }

  private static calculateResourceUtilizationBreakdown(metrics: SimulationMetrics): Record<string, number> {
    return {
      cpu: metrics.resourceUtilization * 0.4,
      memory: metrics.resourceUtilization * 0.3,
      network: (metrics.networkLatency / 1000) * 0.2,
      storage: metrics.resourceUtilization * 0.1
    };
  }

  private static generateResourceOptimizations(metrics: SimulationMetrics): string[] {
    const optimizations: string[] = [];

    if (metrics.groupBalanceScore < 0.7) {
      optimizations.push('Adjust group size thresholds');
    }

    const utilizationBreakdown = this.calculateResourceUtilizationBreakdown(metrics);
    if (utilizationBreakdown.cpu > 0.8) {
      optimizations.push('Optimize CPU-intensive operations');
    }
    if (utilizationBreakdown.memory > 0.8) {
      optimizations.push('Implement memory caching strategies');
    }
    if (utilizationBreakdown.network > 0.8) {
      optimizations.push('Optimize network payload sizes');
    }

    return optimizations;
  }

  static getSimulationHistory(): SimulationMetrics[] {
    return LoomGroupSimulator.metrics;
  }
} 