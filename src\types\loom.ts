export enum GroupType {
  FOUR_JUMPS = 'FOUR_JUMPS',
  FULL_RIDE = 'FULL_RIDE'
}

export enum GroupTier {
  STARTER = 10,
  BASIC = 20,
  BRONZE = 50,
  SILVER = 100,
  GOLD = 500,
  PLATINUM = 1000
}

export enum SpotPosition {
  CENTER = 'CENTER',
  INNER = 'INNER',
  MIDDLE = 'MIDDLE',
  OUTER = 'OUTER'
}

export interface WaitingSpot {
  userId: string | null;
  joinedAt: number | null;
}

export interface LoomSpots {
  CENTER: string | null;
  INNER: (string | null)[];    // 2 spots
  MIDDLE: (string | null)[];   // 4 spots
  OUTER: (string | null)[];    // 8 spots
  WAITING: WaitingSpot[];      // 8 waiting spots
}

export interface LoomGroup {
  id: string;
  entryFee: number;
  spots: LoomSpots;
  currentPlayers: number;
  maxPlayers: number;
  maxWaitingPlayers: number;  // New field for waiting spots limit
  isActive: boolean;
  lastMoveTimestamp: number;
  timerDuration: number;
  playerTraffic: number;
  parentId: string | null;
  childIds: string[];
  generation: number;
  maxGeneration: number;
  houseFeePercentage: number;
  currentPrizePool: number;
  type: GroupType;
  createdAt: number;
  lastUpdatedAt: number;
  status: GroupStatus;
  minPlayersToStart: number;
  autoMergeThreshold: number;
  splitThreshold: number;
  waitingQueue: string[];      // Track order of waiting players
}

export enum GroupStatus {
  WAITING = 'WAITING',
  ACTIVE = 'ACTIVE',
  MERGING = 'MERGING',
  SPLITTING = 'SPLITTING',
  COMPLETED = 'COMPLETED',
  PAUSED = 'PAUSED'
}

export interface PlayerMove {
  userId: string;
  fromPosition: SpotPosition;
  toPosition: SpotPosition;
  timestamp: number;
  groupId: string;
}

export interface GroupReward {
  userId: string;
  groupId: string;
  amount: number;
  timestamp: number;
  type: GroupType;
  position: SpotPosition;
}

export interface GroupStats {
  totalPlayers: number;
  activeGroups: number;
  averageTimeToComplete: number;
  totalPrizePool: number;
  houseFeeCollected: number;
  playerWinnings: number;
  averagePlayersPerGroup: number;
  mostPopularTier: GroupTier;
  mostPopularType: GroupType;
}

export interface LoomDebitCard {
  id: string;
  userId: string;
  cardNumber: string;
  expiryMonth: string;
  expiryYear: string;
  status: 'PENDING' | 'ACTIVE' | 'BLOCKED' | 'EXPIRED';
  creditBuilderEnabled: boolean;
  monthlySpending: number;
  monthlyLimit: number;
  lastReportedDate?: number;
  lastUpdated?: number;
  previousStatus?: 'PENDING' | 'ACTIVE' | 'EXPIRED';
}

export interface CreditBuilderTransaction {
  id: string;
  userId: string;
  cardId: string;
  amount: number;
  merchantName: string;
  timestamp: number;
  reportedToBureau: boolean;
  reportedDate?: number;
}

export interface CreditReport {
  userId: string;
  month: number;
  year: number;
  totalSpent: number;
  paymentsMade: number;
  reportedDate: number;
  status: 'PENDING' | 'REPORTED' | 'FAILED';
}

export interface CurrencyBalance {
  currency: string;  // ISO 4217 currency code (e.g., 'USD', 'EUR', 'GBP')
  balance: number;
  lastUpdated: number;
}

export interface CurrencyConversion {
  id: string;
  userId: string;
  fromCurrency: string;
  toCurrency: string;
  fromAmount: number;
  toAmount: number;
  rate: number;
  timestamp: number;
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  ipAddress: string;
  location: {
    country: string;
    city: string;
    coordinates: {
      latitude: number;
      longitude: number;
    };
  };
}

export interface SecurityLog {
  id: string;
  userId: string;
  eventType: 'LOGIN' | 'LOGOUT' | 'PASSWORD_CHANGE' | 'CURRENCY_CONVERSION' | 
             'PAYMENT_METHOD_ADDED' | 'SUSPICIOUS_ACTIVITY' | 'LOCATION_CHANGE' |
             'DEVICE_CHANGE' | '2FA_ENABLED' | '2FA_DISABLED' | 'ACCOUNT_RECOVERY' |
             'CARD_LOCK' | 'CARD_UNLOCK';
  timestamp: number;
  ipAddress: string;
  deviceInfo: {
    type: string;
    browser: string;
    os: string;
    deviceId: string;
  };
  location: {
    country: string;
    city: string;
    coordinates: {
      latitude: number;
      longitude: number;
    };
  };
  status: 'SUCCESS' | 'FAILED' | 'BLOCKED';
  details: string;
  riskScore: number;
}

export interface SecuritySettings {
  userId: string;
  twoFactorEnabled: boolean;
  twoFactorMethod: '2FA_APP' | 'SMS' | 'EMAIL';
  loginNotifications: boolean;
  transactionNotifications: boolean;
  locationBasedAuth: boolean;
  trustedDevices: {
    deviceId: string;
    name: string;
    lastUsed: number;
    ipAddress: string;
  }[];
  maxDevices: number;
  passwordLastChanged: number;
  securityQuestions: {
    question: string;
    answerHash: string;
  }[];
  ipWhitelist: string[];
  suspiciousActivityThreshold: number;
}

export interface PeerTransfer {
  id: string;
  fromUserId: string;
  toUserId: string;
  amount: number;
  assetType: 'TOKEN' | 'LOOMCOIN' | 'CURRENCY';
  currency?: string;
  timestamp: number;
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  note?: string;
}

export interface PeerLoan {
  id: string;
  lenderId: string;
  borrowerId: string;
  amount: number;
  assetType: 'TOKEN' | 'LOOMCOIN' | 'CURRENCY';
  currency?: string;
  interestRate: number;
  dueDate: number;
  timestamp: number;
  status: 'PENDING' | 'ACTIVE' | 'REPAID' | 'DEFAULTED' | 'REJECTED';
  repaymentAmount: number;
  repaidAmount: number;
  lastRepaymentAttempt?: number;
  note?: string;
}

export interface PeerCreditScore {
  userId: string;
  score: number; // 300-850 range
  totalLoans: number;
  repaidLoans: number;
  defaultedLoans: number;
  averageRepaymentTime: number; // in days
  totalLent: number;
  activeLoans: number;
  lastUpdated: number;
}

export interface LoanRepayment {
  id: string;
  loanId: string;
  amount: number;
  timestamp: number;
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  isAutomatic: boolean;
} 