import React, { useEffect } from 'react';
import { playSound } from '../utils/SoundManager';
import { SOUNDS } from '../../constants';

// Types
interface User {
  name: string;
  iconUrl: string;
}

interface Story {
  id: string;
  user: User;
  content?: string;
  imageUrl?: string;
  timestamp?: string;
}

interface StoryModalProps {
  story: Story | null;
  onClose: () => void;
}

// Inline styles (can be moved to CSS modules later)
const modalStyles: React.CSSProperties = {
  position: 'fixed',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  backgroundColor: 'rgba(0, 0, 0, 0.8)',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  zIndex: 1001, // Ensure it's above other content
};

const modalContentStyles: React.CSSProperties = {
  backgroundColor: '#2a1a3e', // Dark purple background
  padding: '30px',
  borderRadius: '10px',
  textAlign: 'center',
  maxWidth: '80%',
  maxHeight: '80%',
  overflow: 'auto',
  position: 'relative',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  boxShadow: '0 5px 15px rgba(0, 0, 0, 0.5)',
};

const closeButtonStyles: React.CSSProperties = {
  position: 'absolute',
  top: '10px',
  right: '10px',
  background: 'none',
  border: 'none',
  color: '#aaa',
  fontSize: '1.5em',
  cursor: 'pointer',
  lineHeight: '1',
  transition: 'color 0.2s ease',
};

const userIconStyles: React.CSSProperties = {
  width: '100px',
  height: '100px',
  borderRadius: '50%',
  objectFit: 'cover',
  marginBottom: '15px',
  border: '3px solid #ff8c00', // Orange border like story reel
};

const userNameStyles: React.CSSProperties = {
  fontSize: '1.2em',
  fontWeight: 'bold',
  color: '#fff',
  marginBottom: '20px',
};

const placeholderTextStyles: React.CSSProperties = {
  color: '#ccc',
  fontSize: '0.9em',
  lineHeight: '1.5',
};

const storyImageStyles: React.CSSProperties = {
  maxWidth: '100%',
  maxHeight: '300px',
  borderRadius: '8px',
  marginBottom: '15px',
  objectFit: 'cover',
};

const StoryModal: React.FC<StoryModalProps> = ({ story, onClose }) => {
  if (!story) return null;

  // Handle closing modal with Escape key
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        playSound(SOUNDS.UI_CLICK_MENU_SELECT, 0.7);
        onClose();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [onClose]);

  const handleClose = () => {
    playSound(SOUNDS.UI_CLICK_MENU_SELECT, 0.7);
    onClose();
  };

  // Prevent clicks inside the modal content from closing it
  const handleContentClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const target = e.target as HTMLImageElement;
    // Fallback to a default avatar if the story image fails to load
    target.src = 'https://via.placeholder.com/100/888/fff?text=' + story.user.name.charAt(0);
  };

  const handleCloseButtonHover = () => {
    playSound(SOUNDS.UI_HOVER_BUTTON_01, 0.3);
  };

  return (
    <div style={modalStyles} onClick={handleClose} role="dialog" aria-modal="true" aria-labelledby="story-title">
      <div style={modalContentStyles} onClick={handleContentClick}>
        <button 
          style={closeButtonStyles} 
          onClick={handleClose}
          onMouseEnter={handleCloseButtonHover}
          aria-label="Close story"
          type="button"
        >
          ×
        </button>
        
        <img 
          src={story.user.iconUrl} 
          alt={`${story.user.name}'s avatar`} 
          style={userIconStyles}
          onError={handleImageError}
        />
        
        <div id="story-title" style={userNameStyles}>
          {story.user.name}'s Story
        </div>
        
        {story.imageUrl && (
          <img 
            src={story.imageUrl} 
            alt="Story content" 
            style={storyImageStyles}
          />
        )}
        
        {story.content ? (
          <p style={placeholderTextStyles}>{story.content}</p>
        ) : (
          <p style={placeholderTextStyles}>
            Full story content will be displayed here...
          </p>
        )}
        
        {story.timestamp && (
          <div style={{ ...placeholderTextStyles, fontSize: '0.8em', marginTop: '15px' }}>
            {story.timestamp}
          </div>
        )}
      </div>
    </div>
  );
};

export default StoryModal;
