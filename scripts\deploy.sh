#!/bin/bash

# Load environment variables
source ../.env.production

# Set deployment directory
DEPLOY_DIR="/opt/loomloot"
DATE=$(date +%Y%m%d_%H%M%S)

# Function to log messages
log_message() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# Function to check if command succeeded
check_status() {
    if [ $? -eq 0 ]; then
        log_message "✅ $1 succeeded"
    else
        log_message "❌ $1 failed"
        exit 1
    fi
}

# Create deployment report
REPORT_FILE="$DEPLOY_DIR/deploy_report_$DATE.txt"
exec 1> >(tee -a "$REPORT_FILE")
exec 2>&1

log_message "Starting deployment process..."

# Verify environment variables
log_message "Verifying environment variables..."
required_vars=("DATABASE_URL" "REDIS_URL" "NEXTAUTH_URL" "NEXTAUTH_SECRET" "BTC_API_KEY")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        log_message "Error: $var is not set"
        exit 1
    fi
done

# Create backup before deployment
log_message "Creating backup before deployment..."
./backup.sh
check_status "Pre-deployment backup"

# Pull latest code
log_message "Pulling latest code..."
cd $DEPLOY_DIR
git fetch origin
git checkout main
git pull origin main
check_status "Code pull"

# Install dependencies
log_message "Installing dependencies..."
npm ci
check_status "Dependencies installation"

# Build application
log_message "Building application..."
npm run build
check_status "Application build"

# Pull latest Docker images
log_message "Pulling latest Docker images..."
docker-compose -f docker-compose.prod.yml pull
check_status "Docker images pull"

# Stop current services
log_message "Stopping current services..."
docker-compose -f docker-compose.prod.yml down
check_status "Services shutdown"

# Start new services
log_message "Starting new services..."
docker-compose -f docker-compose.prod.yml up -d
check_status "Services startup"

# Run database migrations
log_message "Running database migrations..."
npx prisma migrate deploy
check_status "Database migration"

# Verify deployment
log_message "Verifying deployment..."

# Check container health
for service in app db redis nginx; do
    container_name="loomloot_${service}_1"
    health_status=$(docker inspect --format='{{.State.Health.Status}}' $container_name 2>/dev/null)
    if [ "$health_status" != "healthy" ]; then
        log_message "Error: $container_name is not healthy"
        exit 1
    fi
done

# Check application health
log_message "Checking application health..."
curl -f http://localhost:3000/api/health
check_status "Application health check"

# Check database connectivity
log_message "Checking database connectivity..."
docker exec loomloot_db_1 psql -U $DB_USER -d $DB_NAME -c "\dt"
check_status "Database connectivity check"

# Check Redis connectivity
log_message "Checking Redis connectivity..."
docker exec loomloot_redis_1 redis-cli ping
check_status "Redis connectivity check"

# Clean up old builds and images
log_message "Cleaning up..."
docker system prune -f
check_status "Cleanup"

# Update deployment status
echo $DATE > "$DEPLOY_DIR/last_deploy"
log_message "Deployment completed successfully!"

# Optional: Send deployment notification
# mail -s "LoomLoot Deployment Complete - $(date +%Y-%m-%d)" <EMAIL> < $REPORT_FILE

# Print deployment summary
log_message "Deployment Summary:"
echo "=================================================="
echo "Deployment Date: $(date)"
echo "Git Commit: $(git rev-parse HEAD)"
echo "Environment: Production"
echo "Application URL: $NEXTAUTH_URL"
echo "Report Location: $REPORT_FILE"
echo "==================================================" 