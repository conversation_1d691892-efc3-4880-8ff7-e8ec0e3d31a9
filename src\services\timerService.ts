import { LoomGroupManager } from './loomGroupManager';

export class TimerService {
  private static instances: Map<string, NodeJS.Timeout> = new Map();

  static async startGroupTimer(groupId: string): Promise<void> {
    // Clear existing timer if any
    this.stopGroupTimer(groupId);

    const checkAndMove = async () => {
      try {
        await LoomGroupManager.movePlayersUp(groupId);
        
        // Get updated group info to determine next timer duration
        const group = await LoomGroupManager.getGroup(groupId);
        if (group && group.isActive) {
          // Schedule next check based on current timer duration
          this.instances.set(groupId, setTimeout(
            checkAndMove,
            group.timerDuration
          ));
        } else {
          this.stopGroupTimer(groupId);
        }
      } catch (error) {
        console.error(`Timer error for group ${groupId}:`, error);
      }
    };

    // Start the timer loop
    await checkAndMove();
  }

  static stopGroupTimer(groupId: string): void {
    const timer = this.instances.get(groupId);
    if (timer) {
      clearTimeout(timer);
      this.instances.delete(groupId);
    }
  }
} 