import 'next-auth';
import { DefaultSession } from 'next-auth';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email?: string | null;
      name?: string | null;
      image?: string | null;
      isAdmin: boolean;
      isPremium: boolean;
    } & DefaultSession['user']
  }

  interface User {
    id: string;
    email?: string | null;
    name?: string | null;
    image?: string | null;
    isAdmin: boolean;
    isPremium: boolean;
  }
} 