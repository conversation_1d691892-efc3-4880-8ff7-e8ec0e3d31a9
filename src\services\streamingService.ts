import { redis } from '../utils/redis';
import { Server as SocketServer } from 'socket.io';

interface Stream {
  id: string;
  userId: string;
  title: string;
  description?: string;
  status: 'PREPARING' | 'LIVE' | 'ENDED';
  startTime?: number;
  endTime?: number;
  viewerCount: number;
  maxViewers: number;
  totalViews: number;
  chatEnabled: boolean;
}

interface StreamChat {
  streamId: string;
  userId: string;
  message: string;
  timestamp: number;
}

export class StreamingService {
  private static io: SocketServer;

  static initialize(io: SocketServer) {
    this.io = io;

    io.on('connection', (socket) => {
      const userId = socket.handshake.auth.userId;

      socket.on('stream:start', async (data) => {
        await this.startStream(userId, data);
      });

      socket.on('stream:end', async (data) => {
        await this.endStream(data.streamId);
      });

      socket.on('stream:join', async (data) => {
        await this.joinStream(userId, data.streamId);
        socket.join(`stream:${data.streamId}`);
      });

      socket.on('stream:leave', async (data) => {
        await this.leaveStream(userId, data.streamId);
        socket.leave(`stream:${data.streamId}`);
      });

      socket.on('stream:chat', async (data) => {
        await this.sendStreamChat(userId, data.streamId, data.message);
      });
    });
  }

  static async startStream(
    userId: string,
    data: { title: string; description?: string }
  ): Promise<Stream> {
    const stream: Stream = {
      id: `stream_${Date.now()}`,
      userId,
      title: data.title,
      description: data.description,
      status: 'PREPARING',
      viewerCount: 0,
      maxViewers: 0,
      totalViews: 0,
      chatEnabled: true
    };

    await redis.set(`stream:${stream.id}`, JSON.stringify(stream));
    await redis.sadd(`user:${userId}:streams`, stream.id);

    // Notify followers
    const followers = await redis.smembers(`user:${userId}:followers`);
    followers.forEach(followerId => {
      this.io.to(`user:${followerId}`).emit('stream:started', {
        streamerId: userId,
        stream
      });
    });

    return stream;
  }

  static async endStream(streamId: string): Promise<void> {
    const streamData = await redis.get(`stream:${streamId}`);
    if (!streamData) throw new Error('Stream not found');

    const stream: Stream = JSON.parse(streamData);
    stream.status = 'ENDED';
    stream.endTime = Date.now();

    await redis.set(`stream:${streamId}`, JSON.stringify(stream));
    
    // Notify viewers
    this.io.to(`stream:${streamId}`).emit('stream:ended', stream);
  }

  static async joinStream(userId: string, streamId: string): Promise<void> {
    const streamData = await redis.get(`stream:${streamId}`);
    if (!streamData) throw new Error('Stream not found');

    const stream: Stream = JSON.parse(streamData);
    stream.viewerCount++;
    stream.totalViews++;
    stream.maxViewers = Math.max(stream.maxViewers, stream.viewerCount);

    await redis.set(`stream:${streamId}`, JSON.stringify(stream));
    
    // Notify stream viewers
    this.io.to(`stream:${streamId}`).emit('stream:viewer_joined', {
      userId,
      viewerCount: stream.viewerCount
    });
  }

  static async leaveStream(userId: string, streamId: string): Promise<void> {
    const streamData = await redis.get(`stream:${streamId}`);
    if (!streamData) throw new Error('Stream not found');

    const stream: Stream = JSON.parse(streamData);
    stream.viewerCount = Math.max(0, stream.viewerCount - 1);

    await redis.set(`stream:${streamId}`, JSON.stringify(stream));
    
    // Notify stream viewers
    this.io.to(`stream:${streamId}`).emit('stream:viewer_left', {
      userId,
      viewerCount: stream.viewerCount
    });
  }

  static async sendStreamChat(
    userId: string,
    streamId: string,
    message: string
  ): Promise<void> {
    const chat: StreamChat = {
      streamId,
      userId,
      message,
      timestamp: Date.now()
    };

    await redis.lpush(`stream:${streamId}:chat`, JSON.stringify(chat));
    
    // Emit to stream viewers
    this.io.to(`stream:${streamId}`).emit('stream:chat', chat);
  }
} 