import { prisma } from '../lib/prisma';
import { AnnouncementData, AnnouncementViewData } from '../types/models';
import { Prisma } from '@prisma/client';

export class AnnouncementService {
  async createAnnouncement(data: AnnouncementData) {
    return await prisma.announcement.create({
      data: {
        title: data.title,
        content: data.content,
        type: data.type,
        priority: data.priority,
        startDate: data.startDate,
        endDate: data.endDate,
        isActive: data.isActive,
        conditions: data.conditions,
      },
    });
  }

  async getActiveAnnouncements(userId: string) {
    const now = new Date();
    return await prisma.announcement.findMany({
      where: {
        isActive: true,
        startDate: { lte: now },
        endDate: { gte: now },
        NOT: {
          views: {
            some: {
              userId,
            },
          },
        },
      },
      orderBy: {
        priority: 'desc',
      },
    });
  }

  async markAnnouncementAsViewed(data: AnnouncementViewData) {
    return await prisma.announcementView.create({
      data: {
        announcementId: data.announcementId,
        userId: data.userId,
        viewedAt: new Date(),
      },
    });
  }

  async updateAnnouncement(id: string, data: Partial<AnnouncementData>) {
    return await prisma.announcement.update({
      where: { id },
      data: {
        ...(data.title && { title: data.title }),
        ...(data.content && { content: data.content }),
        ...(data.type && { type: data.type }),
        ...(data.priority && { priority: data.priority }),
        ...(data.startDate && { startDate: data.startDate }),
        ...(data.endDate && { endDate: data.endDate }),
        ...(typeof data.isActive === 'boolean' && { isActive: data.isActive }),
        ...(data.conditions && { conditions: data.conditions }),
      },
    });
  }

  async deleteAnnouncement(id: string) {
    await prisma.$transaction([
      prisma.announcementView.deleteMany({
        where: { announcementId: id },
      }),
      prisma.announcement.delete({
        where: { id },
      }),
    ]);
  }
} 