# Loom Loot - Feature Requirements

## User Types
- Regular Users
- Premium Users
- Business Partners/Sponsors
- Administrators

## Product Types
1. Raffle Items
   - Items from sponsors/partners
   - Approval workflow required
   - Admin assignment to raffle sections

2. Digital Products (Premium Users)
   - Upload capability
   - Access control options:
     - All users
     - Subscribers only
   - File management system

3. Subscription Products
   - In-app payments
   - Tier system
   - Premium features access

## Social Features
- User following system
- User network creation
- Token transfer between users
- 1-on-1 game challenges

## Business Partner Features
- Dedicated login area
- Product upload system
- Approval workflow
- Communication with admin

## Token System
- Transfer capabilities
- Usage in raffles
- Gaming currency
- Challenge system integration

## Gaming Features
- 1-on-1 challenges
- Token wagering
- Mini-games

## Access Control
- Subscription-based access
- Tiered content visibility
- Partner/Admin restricted areas

## Payment Systems
- In-app subscription payments
- Token purchases
- Digital product sales

## Workflow Systems
- Partner product approval
- Raffle assignment
- Content moderation

## Partner System Details
1. Partner Types
   - Sponsors
   - Vendors
   - Creators
   - Brands

2. Partnership Workflow
   - Application process
   - Document verification
   - Admin approval system
   - Partnership tiers
   - Suspension/reactivation process

3. Partner Dashboard
   - Product management
   - Sales statistics
   - Account status
   - Communication center

4. Partner Features
   - Product upload system
   - Business profile management
   - Sales tracking
   - Document management
   - Social media integration

5. Admin Controls for Partners
   - Application review
   - Partner verification
   - Status management
   - Communication tools
   - Statistics monitoring

6. Partner Communication
   - Automated notifications
   - Status updates
   - Admin messaging
   - Product approval notifications [preious content...]

# Loom Groups System
# Loom Loot - Feature Requirements

[Previous sections remain...]

## Loom Groups System

### Core Mechanics
1. Group Structure
   - Center position (1 player)
   - Inner ring (multiple players)
   - Middle ring (multiple players)
   - Outer ring (join slots)
   - Timer display
   - Entry fee display

2. Timer System
   - Dynamic round timing:
     * 15 seconds (high activity)
     * 30 seconds (moderate activity)
     * 60 seconds (low activity)
   - Automatic progression on timer completion
   - Visual countdown indicator

3. Player Movement
   - Forward-only progression
   - Outer → Middle → Inner → Center
   - Center → Lobby/New Game
   - No backward movement allowed
   - Premium auto-placement

### Group Management
1. Splitting Mechanics
   - Timer-based splits each round
   - Center player payout and removal
   - Inner ring redistribution
   - New group formation
   - Player redistribution

2. Merging Logic
   - Underpopulated group detection
   - Priority-based merging
   - Minimum player thresholds
   - Position preservation
   - Efficient group consolidation

3. Player Distribution
   - New player placement
   - Premium player auto-placement
   - Position optimization
   - Group balance maintenance

### Economic System
1. Token Management
   - Entry fee collection
   - House fee (1/8th)
   - Payout calculations
   - Token balance tracking
   - LoomCoin integration

2. Premium Features
   - Infinite round play
   - Automatic placement
   - Enhanced rewards
   - Priority positioning

### UI/UX Elements
1. Visual Components
   - Octagonal board layout
   - Color-coded positions
   - Join slot indicators
   - Player avatars/names
   - Timer display

2. Casino-Style Features
   - Win animations
   - Sound effects
   - Visual feedback
   - Payout celebrations
   - Achievement displays

### Data Structures
1. LoomGroup Model
python
class LoomGroup:
id: str
name: str
entry_fee: int
spots: {
CENTER: Optional[str]
INNER: List[Optional[str]]
MIDDLE: List[Optional[str]]
OUTER: List[Optional[str]]
}
progress_timer: int
max_players: int
current_players: int
reward_multiplier: float
type: str
free_play: bool


2. User Gaming Model
python
class UserGaming:
id: str
loom_tokens: int
loom_coin: int
joined_loom_id: Optional[str]
premium_status: bool
auto_play: bool


### Required Services
1. GroupManagementService
   - Group creation/deletion
   - Player movement
   - Split/merge handling
   - Timer management

2. RewardService
   - Payout calculations
   - Token distribution
   - House fee processing
   - Bonus calculations

3. MatchmakingService
   - Player placement
   - Group assignment
   - Position optimization
   - Auto-play management

### Monitoring Requirements
1. Performance Metrics
   - Group counts
   - Player counts
   - Round timing
   - System load

2. Economic Metrics
   - Token flow
   - House earnings
   - Player winnings
   - Premium conversions

### Next Steps
1. Implementation Priority
   - Core group mechanics
   - Player movement system
   - Economic framework
   - UI components

2. Testing Requirements
   - Load testing
   - Economic balance
   - Edge cases
   - User experience

[previous content...]
 
 #   L o o m   G r o u p s   S y s t e m 
 [ n e w   c o n t e n t . . . ] 
 
 [ p r e v i o u s   c o n t e n t . . . ] 
 
 #   L o o m   G r o u p s   S y s t e m 
 [ n e w   c o n t e n t . . . ] 
 
 [ p r e v i o u s   c o n t e n t . . . ] 
 
 #   L o o m   G r o u p s   S y s t e m 
 [ n e w   c o n t e n t . . . ] 
 
 