import React from "react";

const VerifyID: React.FC = () => {
  return (
    <div className="p-6">
      <label className="block text-gray-700 font-bold mb-2" htmlFor="uploadID">
        Verify Your Identity
      </label>
      <div className="relative mt-2 rounded-md shadow-sm">
        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"></div>
        <input
          type="file"
          name="uploadID"
          id="uploadID"
          className="block w-full rounded-md border-0 py-1.5 pl-4 pr-20 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
        />
      </div>
      <button
        className="mt-4 bg-yellow-400 hover:bg-yellow-500 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        type="button"
      >
        FINISH
      </button>
    </div>
  );
};

export default VerifyID;
