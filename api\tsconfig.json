{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strict": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": true, "strictBindCallApply": true, "noFallthroughCasesInSwitch": true, "esModuleInterop": true}}