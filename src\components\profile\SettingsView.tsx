import React, { useState, useEffect } from 'react';
import { playSound } from '../utils/SoundManager';
import { SOUNDS } from '../../constants';
import useScreenSize from '../utils/UseScreenSize';

// Types
interface SettingsViewProps {
  onNavigate: (route: string) => void;
  onToggleSound: () => void;
}

interface AudioService {
  isSoundEnabled: () => boolean;
  setBackgroundMusicVolume: (volume: number) => void;
  getSoundEffectsVolume: () => number;
  setSoundEffectsVolume: (volume: number) => void;
}

// Mock AudioService for now - will be replaced with real implementation
const AudioService: AudioService = {
  isSoundEnabled: () => true,
  setBackgroundMusicVolume: (volume: number) => {
    console.log('Setting background music volume:', volume);
  },
  getSoundEffectsVolume: () => 0.7,
  setSoundEffectsVolume: (volume: number) => {
    console.log('Setting sound effects volume:', volume);
  },
};

const SettingsView: React.FC<SettingsViewProps> = ({ onNavigate, onToggleSound }) => {
  const screenSize = useScreenSize();
  const [soundEnabled, setSoundEnabled] = useState<boolean>(AudioService.isSoundEnabled());
  const [musicVolume, setMusicVolume] = useState<number>(0.3);
  const [effectsVolume, setEffectsVolume] = useState<number>(AudioService.getSoundEffectsVolume());
  
  // Layout class based on screen size
  const getLayoutClass = (): string => {
    if (screenSize.isMobile) {
      return 'mobile';
    } else if (screenSize.isTablet) {
      return 'tablet';
    } else {
      return 'desktop';
    }
  };

  // Handle sound toggle
  const handleSoundToggle = () => {
    playSound(SOUNDS.UI_CLICK_BUTTON_01, 0.5, true);
    onToggleSound();
    setSoundEnabled(!soundEnabled);
  };
  
  // Handle music volume change
  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setMusicVolume(newVolume);
    AudioService.setBackgroundMusicVolume(newVolume);
    playSound(SOUNDS.UI_CLICK_BUTTON_01, 0.5, true);
    
    // Add wave animation to the music volume slider
    const waveElement = e.target.parentNode?.querySelector('.volume-wave') as HTMLElement;
    animateVolumeWave(waveElement);
  };
  
  // Handle sound effects volume change
  const handleEffectsVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setEffectsVolume(newVolume);
    AudioService.setSoundEffectsVolume(newVolume);
    playSound(SOUNDS.UI_CLICK_BUTTON_01, newVolume, true);
    
    // Add wave animation to the effects volume slider
    const waveElement = e.target.parentNode?.querySelector('.volume-wave') as HTMLElement;
    animateVolumeWave(waveElement);
  };
  
  // Function to animate the volume wave
  const animateVolumeWave = (waveElement: HTMLElement | null) => {
    if (!waveElement || !soundEnabled) return;
    
    // Reset animation by removing and adding the class
    waveElement.classList.remove('animate-wave');
    
    // Force a reflow to restart the animation
    void waveElement.offsetWidth;
    
    // Add animation class
    waveElement.classList.add('animate-wave');
  };
  
  // Helper function to handle button animations and visual feedback
  const animateButton = (button: HTMLElement | null): boolean => {
    if (!button || !(button instanceof Element)) {
      console.warn('Invalid button element passed to animateButton');
      return false;
    }
    
    // Add animation class
    button.classList.add('button-flash');
    
    // Create and add a wave effect element
    const wave = document.createElement('div');
    wave.className = 'sound-wave';
    button.appendChild(wave);
    
    // Remove the class after animation completes
    setTimeout(() => {
      if (button && button.classList) {
        button.classList.remove('button-flash');
      }
    }, 300);
    
    // Remove the wave element after animation completes
    setTimeout(() => {
      if (button && button.contains(wave)) {
        button.removeChild(wave);
      }
    }, 1000);
    
    // Show sound disabled message if sound is disabled
    if (!soundEnabled) {
      if (button) {
        const message = document.createElement('div');
        message.className = 'sound-disabled-message';
        message.textContent = 'Sound is disabled';
        button.appendChild(message);
        
        setTimeout(() => {
          if (button && button.contains(message)) {
            button.removeChild(message);
          }
        }, 2000);
      }
      return false;
    }
    
    return true;
  };
  
  // Helper function to play test audio
  const playTestAudio = (audioType: 'effect' | 'music', soundName?: keyof typeof SOUNDS) => {
    const button = (event as any)?.currentTarget as HTMLElement | null;
    
    if (!animateButton(button)) {
      return;
    }
    
    if (audioType === 'effect' && soundName) {
      playSound(SOUNDS[soundName], effectsVolume, true);
    } else if (audioType === 'music') {
      const testAudio = new Audio('https://play.rosebud.ai/assets/music_game_casual_01.mp3?GhoS');
      testAudio.volume = musicVolume;
      
      if (soundEnabled) {
        testAudio.play();
      }
      
      setTimeout(() => {
        testAudio.pause();
        testAudio.currentTime = 0;
      }, 3000);
    }
  };
  
  // Test sound effects function
  const testSoundEffect = (soundName: keyof typeof SOUNDS) => {
    playTestAudio('effect', soundName);
  };
  
  // Test background music function
  const testBackgroundMusic = () => {
    playTestAudio('music');
  };
  
  return (
    <div className={`settings-view ${getLayoutClass()}`}>
      <h1 className="settings-title">SETTINGS</h1>

      <div className="settings-container">
        <div className="settings-section">
          <h2 className="section-title">Sound Settings</h2>

          <div className="setting-item">
            <div className="setting-label">Sound Effects</div>
            <div className="toggle-wrapper">
              <button
                className={`toggle-button ${soundEnabled ? 'active' : ''}`}
                onClick={handleSoundToggle}
              >
                <div className="toggle-slider"></div>
              </button>
              <span className="toggle-status">{soundEnabled ? 'ON' : 'OFF'}</span>
            </div>
          </div>

          <div className="setting-item">
            <div className="setting-label">Music Volume</div>
            <div className="volume-control">
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={musicVolume}
                onChange={handleVolumeChange}
                className="volume-slider"
                disabled={!soundEnabled}
              />
              <div className="volume-indicator-container">
                <div
                  className="volume-indicator"
                  style={{width: `${Math.round(musicVolume * 100)}%`}}
                />
                <div className="volume-wave"></div>
              </div>
              <span className="volume-value">{Math.round(musicVolume * 100)}%</span>
            </div>
          </div>

          <div className="setting-item">
            <div className="setting-label">Sound Effects Volume</div>
            <div className="volume-control">
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={effectsVolume}
                onChange={handleEffectsVolumeChange}
                className="volume-slider"
                disabled={!soundEnabled}
              />
              <div className="volume-indicator-container">
                <div
                  className="volume-indicator"
                  style={{width: `${Math.round(effectsVolume * 100)}%`}}
                />
                <div className="volume-wave"></div>
              </div>
              <span className="volume-value">{Math.round(effectsVolume * 100)}%</span>
            </div>
          </div>

          <div className="setting-item">
            <div className="setting-label">Test Audio</div>
            <div className="test-buttons">
              <button
                onClick={() => testSoundEffect('UI_CLICK_BUTTON_01')}
                className="test-button"
                disabled={!soundEnabled}
                title="Play a button click sound that you'll hear when interacting with buttons in the game"
              >
                Test UI Click
              </button>
              <button
                onClick={() => testSoundEffect('MOVE')}
                className="test-button"
                disabled={!soundEnabled}
                title="Play a piece movement sound that you'll hear when players move positions on the board"
              >
                Test Game Sound
              </button>
              <button
                onClick={testBackgroundMusic}
                className="test-button"
                disabled={!soundEnabled}
                title="Play a short preview of background music at your current volume setting"
              >
                Test Music
              </button>
              <button
                onClick={() => testSoundEffect('WIN')}
                className="test-button"
                disabled={!soundEnabled}
                title="Play the celebratory sound that plays when a player wins the game"
              >
                Test Win Sound
              </button>
            </div>
          </div>
        </div>

        <div className="settings-section">
          <h2 className="section-title">About</h2>
          <div className="setting-item">
            <div className="about-text">
              <p>Loom is a social gaming platform where players can connect, play games, and earn rewards.</p>
              <p>Version 1.0.0</p>
            </div>
          </div>
        </div>
      </div>

      {/* Note: Extensive CSS styling will be added to global styles */}
    </div>
  );
};

export default SettingsView;
