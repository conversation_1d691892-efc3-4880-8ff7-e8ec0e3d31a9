import Redis from 'ioredis';

let redisClient: Redis | null = null;

export function getRedisClient(): Redis {
  if (!redisClient) {
    redisClient = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

    redisClient.on('error', (error) => {
      console.error('Redis connection error:', error);
    });

    redisClient.on('connect', () => {
      console.log('Connected to Redis');
    });
  }

  return redisClient;
}

export async function cacheGet<T>(key: string): Promise<T | null> {
  try {
    const redis = getRedisClient();
    const value = await redis.get(key);
    return value ? JSON.parse(value) : null;
  } catch (error) {
    console.error('Redis cache get error:', error);
    return null;
  }
}

export async function cacheSet(key: string, value: any, expirySeconds?: number): Promise<void> {
  try {
    const redis = getRedisClient();
    const stringValue = JSON.stringify(value);
    
    if (expirySeconds) {
      await redis.setex(key, expirySeconds, stringValue);
    } else {
      await redis.set(key, stringValue);
    }
  } catch (error) {
    console.error('Redis cache set error:', error);
  }
}

export async function cacheDelete(key: string): Promise<void> {
  try {
    const redis = getRedisClient();
    await redis.del(key);
  } catch (error) {
    console.error('Redis cache delete error:', error);
  }
}

export async function cacheFlush(): Promise<void> {
  try {
    const redis = getRedisClient();
    await redis.flushall();
  } catch (error) {
    console.error('Redis cache flush error:', error);
  }
}

// Export the Redis instance for direct use
export const redis = redisClient; 