import React, { useState } from 'react';
import { playSound } from '../utils/SoundManager';
import { SOUNDS } from '../../constants';

interface ProfileViewProps {
  onNavigate: (route: string) => void;
}

const ProfileView: React.FC<ProfileViewProps> = ({ onNavigate }) => {
  // State for toggle switches
  const [facebookConnected, setFacebookConnected] = useState<boolean>(true);
  const [xConnected, setXConnected] = useState<boolean>(false);
  const [freePlay, setFreePlay] = useState<boolean>(true);

  const handleActionClick = (action: string) => {
    playSound(SOUNDS.UI_CLICK_BUTTON_01);
    console.log(`Settings action: ${action}`);
    // TODO: Implement actual action logic based on action type
    switch (action) {
      case 'logout':
        // TODO: Implement logout logic
        break;
      case 'update_email':
        // TODO: Navigate to email update page
        break;
      case 'change_password':
        // TODO: Navigate to password change page
        break;
      case 'edit_profile':
        // TODO: Navigate to profile edit page
        break;
      case 'wallet':
        // TODO: Navigate to wallet page
        break;
      case 'id_verification':
        // TODO: Navigate to ID verification page
        break;
      case 'leaderboard':
        // TODO: Navigate to leaderboard page
        break;
      default:
        console.log(`Unhandled action: ${action}`);
    }
  };

  const handleToggle = (
    setter: React.Dispatch<React.SetStateAction<boolean>>, 
    value: boolean
  ) => {
    playSound(SOUNDS.UI_CLICK_MENU_SELECT);
    setter(!value);
    console.log(`Toggled to ${!value}`);
    // TODO: Implement backend update for toggle
  };

  const handleBackClick = () => {
    playSound(SOUNDS.UI_CLICK_BUTTON_01);
    onNavigate('home');
  };

  return (
    <div className="profile-view">
      {/* Header-like section within the view */}
      <div className="profile-header">
        <button className="profile-header-button back-button" onClick={handleBackClick}>
          ⬅️
        </button>
        <img 
          src="https://play.rosebud.ai/assets/Loom%20Token.png?nJ70" 
          alt="Token" 
          className="profile-header-icon" 
        />
        <h2 className="profile-view-title">SETTINGS</h2>
        <button 
          className="profile-header-button logout-button" 
          onClick={() => handleActionClick('logout')}
        >
          <img 
            src="https://play.rosebud.ai/assets/LOADING.png?XuH5" 
            alt="Logout" 
            style={{ height: '25px', filter: 'brightness(0.8)' }}
          />
          LOG OUT
        </button>
      </div>

      {/* User Access Section */}
      <div className="profile-section user-access">
        <h3 className="profile-section-title">
          USER ACCESS
          <img 
            src="https://play.rosebud.ai/assets/bellicon-removebg-preview.png?fXH2" 
            alt="Notification Bell" 
            className="title-icon bell-icon"
          />
        </h3>
        <button 
          className="profile-action-item" 
          onClick={() => handleActionClick('update_email')}
        >
          <span className="action-icon">📧</span>
          <span>UPDATE EMAIL ADDRESS</span>
        </button>
        <button 
          className="profile-action-item" 
          onClick={() => handleActionClick('change_password')}
        >
          <span className="action-icon">🔑</span>
          <span>CHANGE PASSWORD</span>
        </button>
      </div>

      {/* Connected Accounts Section */}
      <div className="profile-section connected-accounts">
        <div className="profile-section-header">
          <h3 className="profile-section-title">CONNECTED ACCOUNTS</h3>
          <div className="free-play-toggle">
            <span>FREE PLAY</span>
            <button
              className={`toggle-switch ${freePlay ? 'on' : 'off'}`}
              onClick={() => handleToggle(setFreePlay, freePlay)}
              aria-pressed={freePlay}
            >
              <span className="toggle-slider"></span>
            </button>
          </div>
        </div>
        
        <div className="profile-action-item social-account">
          <img 
            src="https://play.rosebud.ai/assets/FACEBOOKICON-removebg-preview.png?42pG" 
            alt="Facebook" 
            className="social-icon"
          />
          <span>FACEBOOK ACCOUNT</span>
          <button
            className={`toggle-switch ${facebookConnected ? 'on' : 'off'}`}
            onClick={() => handleToggle(setFacebookConnected, facebookConnected)}
            aria-pressed={facebookConnected}
          >
            <span className="toggle-slider"></span>
          </button>
        </div>
        
        <div className="profile-action-item social-account">
          <span 
            className="social-icon" 
            style={{ fontSize: '1.5em', color: '#ccc' }}
          >
            X
          </span>
          <span>X ACCOUNT</span>
          <button
            className={`toggle-switch ${xConnected ? 'on' : 'off'}`}
            onClick={() => handleToggle(setXConnected, xConnected)}
            aria-pressed={xConnected}
          >
            <span className="toggle-slider"></span>
          </button>
        </div>
      </div>

      {/* Main Actions Section */}
      <div className="profile-section main-actions">
        <button 
          className="profile-action-item" 
          onClick={() => handleActionClick('edit_profile')}
        >
          <img 
            src="https://play.rosebud.ai/assets/profile_logo-preview.png?HEqQ" 
            alt="Edit Profile" 
            className="action-icon main-action-icon"
          />
          <span>EDIT PROFILE</span>
          <img 
            src="https://play.rosebud.ai/assets/settinggear-removebg-preview.png?S7O4" 
            alt="Settings" 
            className="action-setting-icon"
          />
        </button>
        
        <button 
          className="profile-action-item" 
          onClick={() => handleActionClick('wallet')}
        >
          <img 
            src="https://rosebud.ai/assets/download.jpg?Lk57" 
            alt="Wallet" 
            className="action-icon main-action-icon"
          />
          <span>WALLET</span>
          <img 
            src="https://play.rosebud.ai/assets/settinggear-removebg-preview.png?S7O4" 
            alt="Settings" 
            className="action-setting-icon"
          />
        </button>
        
        <button 
          className="profile-action-item" 
          onClick={() => handleActionClick('id_verification')}
        >
          <img 
            src="https://play.rosebud.ai/assets/ID_VERIFICATION_BUTTON-.png?AbTy" 
            alt="ID Verification" 
            className="action-icon main-action-icon"
          />
          <span>ID VERIFICATION</span>
          <img 
            src="https://play.rosebud.ai/assets/settinggear-removebg-preview.png?S7O4" 
            alt="Settings" 
            className="action-setting-icon"
          />
        </button>
        
        <button 
          className="profile-action-item" 
          onClick={() => handleActionClick('leaderboard')}
        >
          <img 
            src="https://rosebud.ai/assets/ASSET%20FACE%205.png?dNC6" 
            alt="Leaderboard" 
            className="action-icon main-action-icon"
          />
          <span>LEADERBOARD</span>
          <img 
            src="https://play.rosebud.ai/assets/settinggear-removebg-preview.png?S7O4" 
            alt="Settings" 
            className="action-setting-icon"
          />
        </button>
      </div>
    </div>
  );
};

export default ProfileView;
