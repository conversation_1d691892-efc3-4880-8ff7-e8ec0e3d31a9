interface EmailOptions {
  to: string;
  subject: string;
  template: string;
  data: Record<string, any>;
}

const templates = {
  'account-approved': (data: any) => `
    Hello ${data.firstName},

    Your account has been approved! You can now log in at ${data.loginUrl}.

    Welcome to our platform!

    Best regards,
    The Team
  `,

  'registration-rejected': (data: any) => `
    Hello ${data.firstName},

    We regret to inform you that your registration request has been declined.

    Reason: ${data.reason}

    If you believe this is an error, please contact our support team.

    Best regards,
    The Team
  `,

  'registration-pending': (data: any) => `
    Hello ${data.firstName},

    Your registration is currently under review. We'll notify you once a decision has been made.

    Thank you for your patience.

    Best regards,
    The Team
  `
};

export async function sendEmail({ to, subject, template, data }: EmailOptions) {
  // Implement your email sending logic here
  // Example using AWS SES, SendGrid, or other email service
  console.log('Sending email:', {
    to,
    subject,
    body: templates[template](data)
  });

  // For development, just log the email
  if (process.env.NODE_ENV === 'development') {
    return Promise.resolve();
  }

  // TODO: Implement actual email sending
  // Example with SendGrid:
  // return sendgrid.send({
  //   to,
  //   from: process.env.EMAIL_FROM,
  //   subject,
  //   text: templates[template](data)
  // });
} 