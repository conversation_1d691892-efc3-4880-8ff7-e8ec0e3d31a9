import { <PERSON>ricContainer, StartedTestContainer, Wait } from 'testcontainers';
import { CoderBotService } from '../services/ai/coderbot';
import { RedisService } from '../services/ai/utils/redis';

describe('CoderBotService', () => {
  let redisContainer: StartedTestContainer;
  let redis: RedisService;
  let coderBot: CoderBotService;

  beforeAll(async () => {
    // Start Redis container
    redisContainer = await new GenericContainer("redis:5.0.3-alpine")
      .withExposedPorts(6379)
      .withWaitStrategy(Wait.forLogMessage("Ready to accept connections"))
      .start();

    // Initialize Redis service with container connection
    const redisPort = redisContainer.getMappedPort(6379);
    redis = new RedisService();
    await redis.connect(`redis://localhost:${redisPort}`);

    // Initialize CoderBot service
    coderBot = new CoderBotService();
  }, 60000); // 60 second timeout for container startup

  afterAll(async () => {
    // Cleanup resources
    await coderBot.cleanup();
    await redis.cleanup();
    await redisContainer.stop();
  });

  beforeEach(async () => {
    // Clear Redis cache before each test
    await redis.cleanup();
    await redis.connect(`redis://localhost:${redisContainer.getMappedPort(6379)}`);
  });

  it('should generate a response', async () => {
    const prompt = 'Write a simple hello world function in TypeScript';
    const response = await coderBot.query(prompt);

    expect(response).toBeDefined();
    expect(response.text).toBeDefined();
    expect(response.tokens).toBeGreaterThan(0);
    expect(response.duration).toBeGreaterThanOrEqual(0);
  });

  it('should respect rate limits', async () => {
    const userId = 'test-user';
    const promises = Array(11).fill(null).map(() => 
      coderBot.query('Test prompt', {}, userId)
    );

    await expect(Promise.all(promises)).rejects.toThrow('Request rate limit exceeded');
  });

  it('should cache responses', async () => {
    const prompt = 'Write a function to add two numbers';
    const options = { temperature: 0.7 };

    // First call should hit the API
    const response1 = await coderBot.query(prompt, options);

    // Second call should hit the cache
    const response2 = await coderBot.query(prompt, options);

    expect(response1).toEqual(response2);
  });

  it('should handle concurrent requests', async () => {
    const userId = 'test-user';
    const maxConcurrent = 3;
    const prompts = Array(maxConcurrent).fill('Test prompt');

    const responses = await Promise.all(
      prompts.map(prompt => coderBot.query(prompt, {}, userId))
    );

    expect(responses).toHaveLength(maxConcurrent);
    responses.forEach(response => {
      expect(response.text).toBeDefined();
      expect(response.tokens).toBeGreaterThan(0);
    });
  });

  it('should track metrics', async () => {
    const prompt = 'Write a simple test function';
    await coderBot.query(prompt);

    const metrics = await coderBot.getMetrics(3600);
    expect(metrics).toBeDefined();
    expect(metrics.length).toBeGreaterThan(0);
    expect(metrics[0]).toHaveProperty('totalRequests');
    expect(metrics[0]).toHaveProperty('averageDuration');
    expect(metrics[0]).toHaveProperty('averageTokens');
  });
}); 