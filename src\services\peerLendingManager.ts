import { getRedisClient } from '../utils/redis';
import { LoomWalletManager } from './loomWalletManager';
import { LoomCoinManager } from './loomCoinManager';
import {
  PeerTransfer,
  PeerLoan,
  PeerCreditScore,
  LoanRepayment
} from '../types/loom';

export class PeerLendingManager {
  private static readonly MIN_INTEREST_RATE = 2; // 2%
  private static readonly MAX_INTEREST_RATE = 20; // 20%
  private static readonly MIN_LOAN_DURATION = 24 * 60 * 60 * 1000; // 1 day
  private static readonly MAX_LOAN_DURATION = 365 * 24 * 60 * 60 * 1000; // 1 year
  private static readonly DEFAULT_CREDIT_SCORE = 650;
  private static readonly CREDIT_SCORE_RANGE = {
    MIN: 300,
    MAX: 850
  };

  private static readonly REDIS_KEYS = {
    TRANSFERS: 'peer_transfers',
    LOANS: 'peer_loans',
    CREDIT_SCORES: 'peer_credit_scores',
    REPAYMENTS: 'loan_repayments',
    USER_LOANS: (userId: string) => `user_loans:${userId}`,
    USER_TRANSFERS: (userId: string) => `user_transfers:${userId}`
  };

  static async transferFunds(
    fromUserId: string,
    toUserId: string,
    amount: number,
    assetType: PeerTransfer['assetType'],
    currency?: string
  ): Promise<PeerTransfer> {
    const redis = await getRedisClient();

    // Validate transfer
    if (amount <= 0) {
      throw new Error('Invalid transfer amount');
    }

    // Check balance based on asset type
    let hasBalance = false;
    switch (assetType) {
      case 'TOKEN':
        const tokenBalance = await LoomWalletManager.getWallet(fromUserId);
        hasBalance = tokenBalance.tokenBalance >= amount;
        break;
      case 'LOOMCOIN':
        const coinBalance = await LoomCoinManager.getInGameBalance(fromUserId);
        hasBalance = coinBalance >= amount;
        break;
      case 'CURRENCY':
        if (!currency) throw new Error('Currency type required for currency transfers');
        const balances = await LoomWalletManager.getUserCurrencyBalances(fromUserId);
        const currencyBalance = balances.find(b => b.currency === currency);
        hasBalance = currencyBalance ? currencyBalance.balance >= amount : false;
        break;
    }

    if (!hasBalance) {
      throw new Error('Insufficient balance');
    }

    // Create transfer record
    const transfer: PeerTransfer = {
      id: `transfer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      fromUserId,
      toUserId,
      amount,
      assetType,
      currency,
      timestamp: Date.now(),
      status: 'PENDING'
    };

    // Process transfer based on asset type
    try {
      switch (assetType) {
        case 'TOKEN':
          await LoomWalletManager.moveTokensInGame(fromUserId, toUserId, amount, 'TRANSFER');
          break;
        case 'LOOMCOIN':
          await LoomCoinManager.moveTokensInGame(fromUserId, toUserId, amount, 'TRANSFER');
          break;
        case 'CURRENCY':
          if (!currency) throw new Error('Currency type required');
          await LoomWalletManager.convertCurrency(
            fromUserId,
            currency,
            currency,
            amount,
            '',
            ''
          );
          break;
      }

      transfer.status = 'COMPLETED';
    } catch (error) {
      transfer.status = 'FAILED';
      throw error;
    }

    // Store transfer record
    await redis.hset(
      this.REDIS_KEYS.TRANSFERS,
      transfer.id,
      JSON.stringify(transfer)
    );

    // Add to user's transfer lists
    await Promise.all([
      redis.zadd(
        this.REDIS_KEYS.USER_TRANSFERS(fromUserId),
        transfer.timestamp,
        transfer.id
      ),
      redis.zadd(
        this.REDIS_KEYS.USER_TRANSFERS(toUserId),
        transfer.timestamp,
        transfer.id
      )
    ]);

    return transfer;
  }

  static async createLoanOffer(
    lenderId: string,
    borrowerId: string,
    amount: number,
    assetType: PeerLoan['assetType'],
    interestRate: number,
    dueDate: number,
    currency?: string,
    note?: string
  ): Promise<PeerLoan> {
    const redis = await getRedisClient();

    // Validate loan parameters
    if (amount <= 0) throw new Error('Invalid loan amount');
    if (interestRate < this.MIN_INTEREST_RATE || interestRate > this.MAX_INTEREST_RATE) {
      throw new Error(`Interest rate must be between ${this.MIN_INTEREST_RATE}% and ${this.MAX_INTEREST_RATE}%`);
    }

    const duration = dueDate - Date.now();
    if (duration < this.MIN_LOAN_DURATION || duration > this.MAX_LOAN_DURATION) {
      throw new Error('Invalid loan duration');
    }

    // Check lender's balance
    let hasBalance = false;
    switch (assetType) {
      case 'TOKEN':
        const tokenBalance = await LoomWalletManager.getWallet(lenderId);
        hasBalance = tokenBalance.tokenBalance >= amount;
        break;
      case 'LOOMCOIN':
        const coinBalance = await LoomCoinManager.getInGameBalance(lenderId);
        hasBalance = coinBalance >= amount;
        break;
      case 'CURRENCY':
        if (!currency) throw new Error('Currency type required for currency loans');
        const balances = await LoomWalletManager.getUserCurrencyBalances(lenderId);
        const currencyBalance = balances.find(b => b.currency === currency);
        hasBalance = currencyBalance ? currencyBalance.balance >= amount : false;
        break;
    }

    if (!hasBalance) {
      throw new Error('Insufficient balance');
    }

    // Calculate repayment amount with interest
    const repaymentAmount = amount * (1 + (interestRate / 100));

    // Create loan record
    const loan: PeerLoan = {
      id: `loan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      lenderId,
      borrowerId,
      amount,
      assetType,
      currency,
      interestRate,
      dueDate,
      timestamp: Date.now(),
      status: 'PENDING',
      repaymentAmount,
      repaidAmount: 0,
      note
    };

    // Store loan record
    await redis.hset(
      this.REDIS_KEYS.LOANS,
      loan.id,
      JSON.stringify(loan)
    );

    // Add to users' loan lists
    await Promise.all([
      redis.zadd(
        this.REDIS_KEYS.USER_LOANS(lenderId),
        loan.timestamp,
        loan.id
      ),
      redis.zadd(
        this.REDIS_KEYS.USER_LOANS(borrowerId),
        loan.timestamp,
        loan.id
      )
    ]);

    return loan;
  }

  static async acceptLoanOffer(loanId: string): Promise<PeerLoan> {
    const redis = await getRedisClient();
    
    // Get loan details
    const loanData = await redis.hget(this.REDIS_KEYS.LOANS, loanId);
    if (!loanData) throw new Error('Loan not found');
    
    const loan: PeerLoan = JSON.parse(loanData);
    if (loan.status !== 'PENDING') throw new Error('Loan is not pending');

    // Transfer funds to borrower
    try {
      await this.transferFunds(
        loan.lenderId,
        loan.borrowerId,
        loan.amount,
        loan.assetType,
        loan.currency
      );

      // Update loan status
      loan.status = 'ACTIVE';
      await redis.hset(
        this.REDIS_KEYS.LOANS,
        loan.id,
        JSON.stringify(loan)
      );

      return loan;
    } catch (error) {
      loan.status = 'REJECTED';
      await redis.hset(
        this.REDIS_KEYS.LOANS,
        loan.id,
        JSON.stringify(loan)
      );
      throw error;
    }
  }

  static async repayLoan(
    loanId: string,
    amount: number,
    isAutomatic: boolean = false
  ): Promise<LoanRepayment> {
    const redis = await getRedisClient();
    
    // Get loan details
    const loanData = await redis.hget(this.REDIS_KEYS.LOANS, loanId);
    if (!loanData) throw new Error('Loan not found');
    
    const loan: PeerLoan = JSON.parse(loanData);
    if (loan.status !== 'ACTIVE') throw new Error('Loan is not active');

    const remainingAmount = loan.repaymentAmount - loan.repaidAmount;
    if (amount > remainingAmount) {
      throw new Error('Repayment amount exceeds remaining balance');
    }

    // Create repayment record
    const repayment: LoanRepayment = {
      id: `repayment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      loanId,
      amount,
      timestamp: Date.now(),
      status: 'PENDING',
      isAutomatic
    };

    try {
      // Transfer repayment
      await this.transferFunds(
        loan.borrowerId,
        loan.lenderId,
        amount,
        loan.assetType,
        loan.currency
      );

      // Update loan
      loan.repaidAmount += amount;
      loan.lastRepaymentAttempt = Date.now();
      
      if (loan.repaidAmount >= loan.repaymentAmount) {
        loan.status = 'REPAID';
        await this.updateCreditScore(loan.borrowerId, true);
      }

      repayment.status = 'COMPLETED';
    } catch (error) {
      repayment.status = 'FAILED';
      loan.lastRepaymentAttempt = Date.now();
      
      if (Date.now() > loan.dueDate) {
        loan.status = 'DEFAULTED';
        await this.updateCreditScore(loan.borrowerId, false);
      }
      
      throw error;
    }

    // Store updated records
    await Promise.all([
      redis.hset(
        this.REDIS_KEYS.LOANS,
        loan.id,
        JSON.stringify(loan)
      ),
      redis.hset(
        this.REDIS_KEYS.REPAYMENTS,
        repayment.id,
        JSON.stringify(repayment)
      )
    ]);

    return repayment;
  }

  static async getCreditScore(userId: string): Promise<PeerCreditScore> {
    const redis = await getRedisClient();
    
    const scoreData = await redis.hget(this.REDIS_KEYS.CREDIT_SCORES, userId);
    if (!scoreData) {
      // Create default credit score for new users
      const defaultScore: PeerCreditScore = {
        userId,
        score: this.DEFAULT_CREDIT_SCORE,
        totalLoans: 0,
        repaidLoans: 0,
        defaultedLoans: 0,
        averageRepaymentTime: 0,
        totalLent: 0,
        activeLoans: 0,
        lastUpdated: Date.now()
      };

      await redis.hset(
        this.REDIS_KEYS.CREDIT_SCORES,
        userId,
        JSON.stringify(defaultScore)
      );

      return defaultScore;
    }

    return JSON.parse(scoreData);
  }

  private static async updateCreditScore(
    userId: string,
    isSuccessful: boolean
  ): Promise<void> {
    const redis = await getRedisClient();
    
    const creditScore = await this.getCreditScore(userId);
    
    if (isSuccessful) {
      creditScore.repaidLoans++;
      creditScore.score = Math.min(
        creditScore.score + 10,
        this.CREDIT_SCORE_RANGE.MAX
      );
    } else {
      creditScore.defaultedLoans++;
      creditScore.score = Math.max(
        creditScore.score - 50,
        this.CREDIT_SCORE_RANGE.MIN
      );
    }

    creditScore.totalLoans++;
    creditScore.lastUpdated = Date.now();

    await redis.hset(
      this.REDIS_KEYS.CREDIT_SCORES,
      userId,
      JSON.stringify(creditScore)
    );
  }

  static async getUserLoans(userId: string): Promise<PeerLoan[]> {
    const redis = await getRedisClient();
    const loans = await redis.hgetall(`${this.REDIS_KEYS.LOANS}:${userId}`);
    return Object.values(loans).map(loan => JSON.parse(loan));
  }

  static async getBorrowedLoans(userId: string): Promise<PeerLoan[]> {
    const loans = await this.getUserLoans(userId);
    return loans.filter(loan => loan.borrowerId === userId);
  }

  static async getLentLoans(userId: string): Promise<PeerLoan[]> {
    const loans = await this.getUserLoans(userId);
    return loans.filter(loan => loan.lenderId === userId);
  }

  static async getAllLoans(userId: string): Promise<PeerLoan[]> {
    const [borrowedLoans, lentLoans] = await Promise.all([
      this.getBorrowedLoans(userId),
      this.getLentLoans(userId)
    ]);
    return [...borrowedLoans, ...lentLoans];
  }

  static async getUserTransfers(userId: string): Promise<PeerTransfer[]> {
    const redis = await getRedisClient();
    
    const transferIds = await redis.zrange(
      this.REDIS_KEYS.USER_TRANSFERS(userId),
      0,
      -1
    );

    const transfers = await Promise.all(
      transferIds.map(async (id) => {
        const transferData = await redis.hget(this.REDIS_KEYS.TRANSFERS, id);
        return transferData ? JSON.parse(transferData) : null;
      })
    );

    return transfers.filter((transfer): transfer is PeerTransfer => transfer !== null);
  }

  static async createLoanRequest(
    borrowerId: string,
    amount: number,
    assetType: PeerLoan['assetType'],
    interestRate: number,
    dueDate: number,
    currency?: string,
    note?: string
  ): Promise<PeerLoan> {
    const redis = await getRedisClient();

    // Validate interest rate
    if (interestRate < this.MIN_INTEREST_RATE || interestRate > this.MAX_INTEREST_RATE) {
      throw new Error(`Interest rate must be between ${this.MIN_INTEREST_RATE}% and ${this.MAX_INTEREST_RATE}%`);
    }

    // Validate due date
    const duration = dueDate - Date.now();
    if (duration < this.MIN_LOAN_DURATION || duration > this.MAX_LOAN_DURATION) {
      throw new Error('Invalid loan duration');
    }

    // Create loan request
    const loan: PeerLoan = {
      id: `loan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      borrowerId,
      lenderId: '', // Will be set when someone accepts the request
      amount,
      assetType,
      currency,
      interestRate,
      dueDate,
      timestamp: Date.now(),
      status: 'PENDING',
      repaymentAmount: amount * (1 + interestRate / 100),
      repaidAmount: 0,
      note
    };

    // Store the loan request
    await redis.hset(
      `${this.REDIS_KEYS.LOANS}:${borrowerId}`,
      loan.id,
      JSON.stringify(loan)
    );

    return loan;
  }

  static async acceptLoanRequest(
    lenderId: string,
    loanId: string
  ): Promise<PeerLoan> {
    const redis = await getRedisClient();
    
    // Get all loan requests
    const loans = await this.getAllLoans(lenderId);
    const loan = loans.find(l => l.id === loanId && l.status === 'PENDING');

    if (!loan) {
      throw new Error('Loan request not found or already processed');
    }

    // Update loan with lender info
    loan.lenderId = lenderId;
    loan.status = 'ACTIVE';

    // Transfer funds to borrower
    await this.transferFunds(
      lenderId,
      loan.borrowerId,
      loan.amount,
      loan.assetType,
      loan.currency
    );

    // Update loan in storage
    await redis.hset(
      `${this.REDIS_KEYS.LOANS}:${loan.borrowerId}`,
      loan.id,
      JSON.stringify(loan)
    );

    return loan;
  }

  static async getLoanRequests(): Promise<PeerLoan[]> {
    const redis = await getRedisClient();
    const keys = await redis.keys(`${this.REDIS_KEYS.LOANS}:*`);
    const loans: PeerLoan[] = [];

    for (const key of keys) {
      const userLoans = await redis.hgetall(key);
      const parsedLoans = Object.values(userLoans)
        .map(loan => JSON.parse(loan))
        .filter(loan => loan.status === 'PENDING');
      loans.push(...parsedLoans);
    }

    return loans;
  }
} 