import { NextApiRequest } from 'next';
import crypto from 'crypto';
import getRawBody from 'raw-body';

export async function verifyWebhookSignature(req: NextApiRequest): Promise<boolean> {
  const signature = req.headers['x-webhook-signature'];
  if (!signature || Array.isArray(signature)) {
    return false;
  }

  try {
    const rawBody = await getRawBody(req);
    const expectedSignature = crypto
      .createHmac('sha256', process.env.WEBHOOK_SECRET!)
      .update(rawBody)
      .digest('hex');

    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );
  } catch (error) {
    console.error('Webhook signature verification error:', error);
    return false;
  }
} 