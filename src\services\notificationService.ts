import { redis } from '../utils/redis';
import { sendEmail } from '../utils/email';
import { sendPushNotification } from '../utils/pushNotifications';

interface WinningNotification {
  userId: string;
  type: string;
  prize: number;
  drawingId: string;
  timestamp: number;
}

export class NotificationService {
  static async notifyWinners(drawingResult: {
    id: string;
    type: string;
    winners: string[];
    totalPrize: number;
  }) {
    for (const userId of drawingResult.winners) {
      try {
        // Create notification
        const notification: WinningNotification = {
          userId,
          type: drawingResult.type,
          prize: drawingResult.totalPrize / drawingResult.winners.length,
          drawingId: drawingResult.id,
          timestamp: Date.now()
        };

        // Store notification
        await redis.lpush(
          `user:${userId}:notifications`,
          JSON.stringify(notification)
        );

        // Get user preferences
        const prefsData = await redis.get(`user:${userId}:preferences`);
        const prefs = prefsData ? JSON.parse(prefsData) : {};

        // Send email if enabled
        if (prefs.emailNotifications) {
          const userData = await redis.get(`user:${userId}`);
          const user = userData ? JSON.parse(userData) : {};
          
          await sendEmail({
            to: user.email,
            subject: 'You Won!',
            template: 'winning-notification',
            data: {
              prize: notification.prize,
              gameType: notification.type,
              drawingDate: new Date().toLocaleDateString()
            }
          });
        }

        // Send push notification if enabled
        if (prefs.pushNotifications) {
          const pushToken = await redis.get(`user:${userId}:push_token`);
          if (pushToken) {
            await sendPushNotification({
              token: pushToken,
              title: 'Congratulations! 🎉',
              body: `You won $${notification.prize} in the ${notification.type}!`,
              data: {
                type: 'WINNING',
                drawingId: notification.drawingId
              }
            });
          }
        }
      } catch (error) {
        console.error(`Failed to notify winner ${userId}:`, error);
      }
    }
  }

  static async getUnreadNotifications(userId: string): Promise<WinningNotification[]> {
    const notifications = await redis.lrange(`user:${userId}:notifications`, 0, 9);
    return notifications.map(n => JSON.parse(n));
  }

  static async markNotificationsAsRead(userId: string): Promise<void> {
    await redis.del(`user:${userId}:notifications`);
  }
} 