import React from 'react';
import { playSound } from '../utils/SoundManager';
import { SOUNDS } from '../../constants';

// Types
interface User {
  name: string;
  iconUrl: string;
}

interface Story {
  id: string;
  user: User;
}

interface StoryItemProps {
  story: Story;
  onStoryClick?: (story: Story) => void;
}

// Simple component for a single story item
const StoryItem: React.FC<StoryItemProps> = ({ story, onStoryClick }) => {
  const handleClick = () => {
    playSound(SOUNDS.UI_CLICK_MENU_SELECT, 0.8); // Play a slightly different click sound
    console.log(`Story clicked: ${story.user.name} (ID: ${story.id}) - Open modal/view`);
    
    // Call the optional callback if provided
    if (onStoryClick) {
      onStoryClick(story);
    }
    
    // TODO: Implement modal opening or navigation logic here
    // This could open a story viewer modal or navigate to a story detail page
  };

  const handleHover = () => {
    playSound(SOUNDS.UI_HOVER_MENU_01);
  };

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const target = e.target as HTMLImageElement;
    // Fallback to a default avatar if the story image fails to load
    target.src = 'https://via.placeholder.com/55/888/fff?text=' + story.user.name.charAt(0);
  };

  return (
    <button 
      className="story-item" 
      onClick={handleClick} 
      onMouseEnter={handleHover}
      aria-label={`View ${story.user.name}'s story`}
      type="button"
    >
      <img 
        src={story.user.iconUrl} 
        alt={`${story.user.name}'s story`} 
        className="story-item-icon"
        onError={handleImageError}
      />
      <span className="story-item-name">{story.user.name}</span>
    </button>
  );
};

export default StoryItem;
