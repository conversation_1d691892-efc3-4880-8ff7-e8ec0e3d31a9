import { redis } from '../utils/redis';
import { NotificationService } from './notification';

export interface GameRoom {
  id: string;
  gameType: string;
  hostId: string;
  betAmount: number;
  status: 'WAITING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  players: {
    id: string;
    username: string;
    ready: boolean;
  }[];
  maxPlayers: number;
  createdAt: number;
  rules: Record<string, any>;
}

export class GameRoomService {
  static async createRoom(params: {
    hostId: string;
    gameType: string;
    betAmount: number;
    maxPlayers: number;
    rules?: Record<string, any>;
  }): Promise<GameRoom> {
    const roomId = `room_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const room: GameRoom = {
      id: roomId,
      gameType: params.gameType,
      hostId: params.hostId,
      betAmount: params.betAmount,
      status: 'WAITING',
      players: [],
      maxPlayers: params.maxPlayers,
      createdAt: Date.now(),
      rules: params.rules || {}
    };

    // Store room data
    await redis.set(`gameroom:${roomId}`, JSON.stringify(room));
    await redis.zadd('gamerooms:active', Date.now(), roomId);
    await redis.sadd(`user:${params.hostId}:hosting`, roomId);

    return room;
  }

  static async joinRoom(roomId: string, userId: string, username: string): Promise<GameRoom> {
    const roomData = await redis.get(`gameroom:${roomId}`);
    if (!roomData) throw new Error('Room not found');

    const room: GameRoom = JSON.parse(roomData);
    if (room.status !== 'WAITING') throw new Error('Room is not accepting players');
    if (room.players.length >= room.maxPlayers) throw new Error('Room is full');
    if (room.players.some(p => p.id === userId)) throw new Error('Already in room');

    // Add player to room
    room.players.push({ id: userId, username, ready: false });
    await redis.set(`gameroom:${roomId}`, JSON.stringify(room));

    // Notify host
    await NotificationService.create({
      type: 'GAME_JOIN',
      priority: 'HIGH',
      title: 'Player Joined Your Game',
      message: `${username} has joined your ${room.gameType} game`,
      metadata: { roomId, gameType: room.gameType }
    });

    return room;
  }

  static async leaveRoom(roomId: string, userId: string): Promise<void> {
    const roomData = await redis.get(`gameroom:${roomId}`);
    if (!roomData) throw new Error('Room not found');

    const room: GameRoom = JSON.parse(roomData);
    
    if (room.hostId === userId) {
      // Host leaving cancels the room
      room.status = 'CANCELLED';
      await redis.zrem('gamerooms:active', roomId);
      
      // Notify all players
      for (const player of room.players) {
        if (player.id !== userId) {
          await NotificationService.create({
            type: 'GAME_CANCELLED',
            priority: 'HIGH',
            title: 'Game Cancelled',
            message: 'The host has left the game',
            metadata: { roomId, gameType: room.gameType }
          });
        }
      }
    } else {
      // Remove player from room
      room.players = room.players.filter(p => p.id !== userId);
    }

    await redis.set(`gameroom:${roomId}`, JSON.stringify(room));
  }

  static async getActiveRooms(filters?: {
    gameType?: string;
    minBet?: number;
    maxBet?: number;
  }): Promise<GameRoom[]> {
    const roomIds = await redis.zrange('gamerooms:active', 0, -1);
    const rooms = await Promise.all(
      roomIds.map(async (id) => {
        const data = await redis.get(`gameroom:${id}`);
        return data ? JSON.parse(data) : null;
      })
    );

    return rooms.filter((room): room is GameRoom => {
      if (!room) return false;
      if (filters?.gameType && room.gameType !== filters.gameType) return false;
      if (filters?.minBet && room.betAmount < filters.minBet) return false;
      if (filters?.maxBet && room.betAmount > filters.maxBet) return false;
      return room.status === 'WAITING';
    });
  }
} 