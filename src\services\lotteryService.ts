import { redis } from '../utils/redis';

interface LotteryTicket {
  id: string;
  userId: string;
  numbers: number[];
  megaNumber: number;
  drawingDate: number;
  status: 'PENDING' | 'WON' | 'LOST';
  prize?: number;
}

interface DrawingResult {
  id: string;
  numbers: number[];
  megaNumber: number;
  drawingDate: number;
  totalPrize: number;
  winners: string[];
}

export class LotteryService {
  static async buyTicket(
    userId: string,
    numbers: number[],
    megaNumber: number
  ): Promise<LotteryTicket> {
    // Validate numbers
    if (numbers.length !== 5 || megaNumber < 1 || megaNumber > 25) {
      throw new Error('Invalid ticket numbers');
    }

    // Create ticket
    const ticket: LotteryTicket = {
      id: `ticket_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      numbers: numbers.sort((a, b) => a - b),
      megaNumber,
      drawingDate: this.getNextDrawingDate(),
      status: 'PENDING'
    };

    // Save ticket
    await redis.set(`lottery:ticket:${ticket.id}`, JSON.stringify(ticket));
    await redis.sadd(`user:${userId}:lottery_tickets`, ticket.id);

    return ticket;
  }

  static async getTickets(userId: string): Promise<LotteryTicket[]> {
    const ticketIds = await redis.smembers(`user:${userId}:lottery_tickets`);
    const tickets = await Promise.all(
      ticketIds.map(async (id) => {
        const data = await redis.get(`lottery:ticket:${id}`);
        return data ? JSON.parse(data) : null;
      })
    );

    return tickets.filter(Boolean);
  }

  private static getNextDrawingDate(): number {
    const now = new Date();
    const nextDrawing = new Date();
    
    // Set next drawing to Wednesday/Saturday at 7:30 PM
    nextDrawing.setHours(19, 30, 0, 0);
    
    while (
      nextDrawing <= now ||
      ![3, 6].includes(nextDrawing.getDay())
    ) {
      nextDrawing.setDate(nextDrawing.getDate() + 1);
    }

    return nextDrawing.getTime();
  }
} 