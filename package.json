{"name": "coderbot-test", "version": "1.0.0", "description": "AI-powered coding assistant tests", "main": "dist/index.js", "scripts": {"build": "tsc", "test": "jest --config jest.config.js", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/bitcoinjs-lib": "^4.0.1", "@types/dockerode": "^3.3.34", "@types/fluent-ffmpeg": "^2.1.27", "@types/geoip-lite": "^1.4.4", "@types/ioredis": "^5.0.0", "@types/jest": "^29.5.0", "@types/js-yaml": "^4.0.9", "@types/jsonschema": "^1.1.1", "@types/next": "^8.0.7", "@types/node": "^20.2.5", "@types/nodemailer": "^6.4.17", "@types/raw-body": "^2.1.4", "@types/react": "^19.0.8", "@types/react-icons": "^2.2.7", "@types/sharp": "^0.31.1", "@types/socket.io": "^3.0.1", "@types/stripe": "^8.0.416", "@types/tiny-secp256k1": "^2.0.0", "@types/ua-parser-js": "^0.7.39", "@types/web-push": "^3.6.4", "@types/webrtc": "^0.0.44", "@types/winston": "^2.4.4", "@types/ws": "^8.5.14", "axios": "^1.7.9", "ecpair": "^3.0.0-rc.0", "fluent-ffmpeg": "^2.1.3", "jest": "^29.5.0", "node-mocks-http": "^1.16.2", "testcontainers": "^10.0.0", "ts-jest": "^29.1.0", "typescript": "^5.1.3"}, "dependencies": {"@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.22.0", "@tanstack/react-query": "^5.66.0", "@types/next-auth": "^3.13.0", "bcryptjs": "^3.0.0", "dockerode": "^4.0.4", "ioredis": "^5.5.0", "js-yaml": "^4.1.0", "jsonschema": "^1.5.0", "next": "^15.1.7", "next-auth": "^4.24.11", "nodemailer": "^6.10.0", "qrcode": "^1.5.4", "react": "^19.0.0", "react-hot-toast": "^2.5.1", "sharp": "^0.33.5", "speakeasy": "^2.0.0", "twilio": "^5.4.4", "web-push": "^3.6.7", "ws": "^8.18.0", "yaml-language-server-parser": "^0.1.3", "firebase": "^10.8.0", "@supabase/supabase-js": "^2.39.0", "@types/node": "^20.0.0"}}