import { prisma } from '../lib/prisma';
import { SurveyData, SurveyResponseData } from '../types/models';
import { Prisma } from '@prisma/client';

export class SurveyService {
  async createSurvey(data: SurveyData) {
    return await prisma.survey.create({
      data: {
        title: data.title,
        description: data.description,
        type: data.type,
        questions: data.questions,
        isActive: data.isActive,
        startDate: data.startDate,
        endDate: data.endDate,
        conditions: data.conditions,
      },
    });
  }

  async getActiveSurveys(userId: string) {
    const now = new Date();
    return await prisma.survey.findMany({
      where: {
        isActive: true,
        startDate: { lte: now },
        endDate: { gte: now },
        NOT: {
          responses: {
            some: {
              userId,
            },
          },
        },
      },
    });
  }

  async submitSurveyResponse(data: SurveyResponseData) {
    return await prisma.surveyResponse.create({
      data: {
        surveyId: data.surveyId,
        userId: data.userId,
        answers: data.answers,
        score: data.score,
      },
    });
  }

  async updateSurvey(id: string, data: Partial<SurveyData>) {
    return await prisma.survey.update({
      where: { id },
      data: {
        ...(data.title && { title: data.title }),
        ...(data.description && { description: data.description }),
        ...(data.type && { type: data.type }),
        ...(data.questions && { questions: data.questions }),
        ...(typeof data.isActive === 'boolean' && { isActive: data.isActive }),
        ...(data.startDate && { startDate: data.startDate }),
        ...(data.endDate && { endDate: data.endDate }),
        ...(data.conditions && { conditions: data.conditions }),
      },
    });
  }

  async deleteSurvey(id: string) {
    await prisma.$transaction([
      prisma.surveyResponse.deleteMany({
        where: { surveyId: id },
      }),
      prisma.survey.delete({
        where: { id },
      }),
    ]);
  }

  async getSurveyAnalytics(id: string) {
    const responses = await prisma.surveyResponse.findMany({
      where: { surveyId: id },
      select: {
        answers: true,
        score: true,
      },
    });

    return {
      totalResponses: responses.length,
      averageScore: responses.reduce((acc, curr) => acc + (curr.score || 0), 0) / responses.length,
      responses: responses.map(r => r.answers),
    };
  }
} 