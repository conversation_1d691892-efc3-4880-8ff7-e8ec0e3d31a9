import React, { useState } from 'react';

const VerificationCode: React.FC = () => {
  const [verificationCode, setVerificationCode] = useState('');
  const [error, setError] = useState('');
    const [message, setMessage] = useState('');


  const handleVerificationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setVerificationCode(e.target.value);
    setError('');
    setMessage('');
  };

  const handleVerify = () => {
        if (!verificationCode) {
            setError('Verification code is required');
          return;
      }
        if (verificationCode.length !== 6) {
              setError('Verification code must be 6 digits long.');
            return;
        }
      // Logic for verification (for now, a simple placeholder):
     console.log("Verification code entered:", verificationCode);
       setMessage("Code Verified");

   };

    const handleResend = () => {
      // Add code for resending the verification code
         setMessage("New Code Sent");
     };

    return (
      <div className="p-6">
          <label
             className="block text-gray-700 font-bold mb-2"
            htmlFor="verificationCode"
          >
              Please type verification code
          </label>
       <div className="relative mt-2 rounded-md shadow-sm">
             <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
           </div>
          <input
              type="text"
             name="verificationCode"
              id="verificationCode"
             value={verificationCode}
             onChange={handleVerificationChange}
              maxLength={6}
           className="block w-full rounded-md border border-gray-300 py-2 px-4 text-gray-900 placeholder-gray-400 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            placeholder="123456"
             aria-describedby={error ? "error-message" : undefined}
            />
        </div>
      {error && (
        <p id="error-message" className="text-red-500 text-sm mt-2">
             {error}
            </p>
        )}
      {message && (
            <p className="text-green-500 text-sm mt-2">
                {message}
             </p>
       )}
      <button
          className="mt-4 bg-yellow-400 hover:bg-yellow-500 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
         type="button"
          onClick={handleVerify}
         >
             Verify
       </button>
      <button className="mt-2 text-sm text-gray-500" type="button" onClick={handleResend}>
           Resend OTP
         </button>
       </div>
  );
};

export default VerificationCode;