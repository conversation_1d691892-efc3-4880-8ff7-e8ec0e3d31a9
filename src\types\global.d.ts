declare module 'uuid' {
  export function v4(): string;
}

declare module 'dockerode' {
  export class Docker {
    constructor(options?: any);
    createContainer(options: any): Promise<Container>;
    pull(image: string, callback: (err: any, stream: any) => void): void;
    modem: {
      followProgress(stream: any, onFinished: (err: any) => void): void;
    };
  }

  export interface Container {
    start(): Promise<void>;
    stop(): Promise<void>;
    remove(): Promise<void>;
    exec(options: any): Promise<Exec>;
    logs(options: any): Promise<Buffer>;
  }

  export interface Exec {
    start(options: any): Promise<any>;
  }
}

declare module '@google/generative-ai' {
  export class GoogleGenerativeAI {
    constructor(apiKey: string);
    getGenerativeModel(options: { model: string }): GenerativeModel;
  }

  export interface GenerativeModel {
    generateContent(prompt: string): Promise<GenerateContentResult>;
  }

  export interface GenerateContentResult {
    response: {
      text(): string;
    };
  }
}

declare module '@azure/openai' {
  export class OpenAIClient {
    constructor(apiKey: string);
  }
}

declare module 'tesseract.js' {
  export function recognize(image: string): Promise<{
    data: {
      text: string;
    };
  }>;
}

declare module 'sharp' {
  interface Sharp {
    resize(width: number, height: number, options?: any): Sharp;
    toFormat(format: string): Sharp;
    toBuffer(): Promise<Buffer>;
  }

  interface SharpStatic {
    (input: Buffer | string): Sharp;
    FormatEnum: Record<string, string>;
  }

  const sharp: SharpStatic;
  export = sharp;
}

declare module 'xterm' {
  export class Terminal {
    constructor(options?: ITerminalOptions);
    open(container: HTMLElement): void;
    write(data: string): void;
    writeln(data: string): void;
    onData(callback: (data: string) => void): void;
    dispose(): void;
    loadAddon(addon: any): void;
  }

  export interface ITerminalOptions {
    cursorBlink?: boolean;
    fontSize?: number;
    fontFamily?: string;
    theme?: ITheme;
  }

  export interface ITheme {
    background?: string;
    foreground?: string;
    cursor?: string;
    [key: string]: string | undefined;
  }
}

declare module 'xterm-addon-fit' {
  export class FitAddon {
    fit(): void;
  }
}

declare module 'xterm-addon-web-links' {
  export class WebLinksAddon {
    constructor();
  }
}

declare module 'xterm-addon-search' {
  export class SearchAddon {
    constructor();
    findNext(term: string): boolean;
    findPrevious(term: string): boolean;
  }
} 