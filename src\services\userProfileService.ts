// Add to existing UserProfile interface
interface UserProfile {
  // ... existing fields ...
  ethnicity: 'BLACK' | 'WHITE' | 'HISPANIC' | 'ASIAN' | 'NATIVE_AMERICAN' | 'PACIFIC_ISLANDER' | 'OTHER';
  taxWithholdingEnabled: boolean;
  taxId?: string; // SSN or EIN
  taxDocuments: {
    year: number;
    documentType: '1099-MISC' | 'W-9';
    status: 'PENDING' | 'GENERATED' | 'SENT';
  }[];
}

// Add to existing UserProfileService class
export class UserProfileService {
  // ... existing methods ...

  static async updateEthnicity(
    userId: string,
    ethnicity: UserProfile['ethnicity']
  ): Promise<void> {
    const profile = await this.getProfile(userId);
    if (!profile) throw new Error('Profile not found');

    profile.ethnicity = ethnicity;
    
    // Alert admin if user is Black (for tax program qualification tracking)
    if (ethnicity === 'BLACK') {
      await this.notifyAdminOfTaxQualification(userId, profile);
    }

    await redis.set(`profile:${userId}`, JSON.stringify(profile));
  }

  private static async notifyAdminOfTaxQualification(
    userId: string,
    profile: UserProfile
  ): Promise<void> {
    // Send notification to admin dashboard
    await redis.lpush('tax:qualifiedUsers', JSON.stringify({
      userId,
      fullName: profile.fullName,
      phoneNumber: profile.phoneNumber,
      timestamp: Date.now()
    }));
  }
} 