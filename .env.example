# Base Configuration
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_WEBSITE_URL=http://localhost:3000
NODE_ENV=development

# Authentication
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=http://localhost:3000
JWT_SECRET=your-jwt-secret

# Database
DATABASE_URL="postgresql://user:password@localhost:5432/loomloot?schema=public"
REDIS_URL="redis://localhost:6379"

# SMS Verification (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=your-twilio-phone-number

# Blockchain Configuration
ETHEREUM_NETWORK=testnet
NEXT_PUBLIC_NETWORK_ID=1337
NEXT_PUBLIC_RPC_URL=http://localhost:8545
WALLET_PRIVATE_KEY=your_wallet_private_key
CONTRACT_ADDRESS=your_contract_address
LOOMCOIN_CONTRACT_ADDRESS=your_loomcoin_address
INFURA_PROJECT_ID=your_infura_project_id
INFURA_PROJECT_SECRET=your_infura_project_secret

# Storage
AWS_ACCESS_KEY_ID=your_aws_key
AWS_SECRET_ACCESS_KEY=your_aws_secret
AWS_REGION=us-east-1
S3_BUCKET=loomloot-dev

# Payment Processing
STRIPE_PUBLIC_KEY=your-stripe-public-key
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# AI Services
AI_VERIFICATION_API_KEY=your_ai_key
LOCAL_AI_URL=http://localhost:8080

# Monitoring
SENTRY_DSN=your_sentry_dsn

# Feature Flags
ENABLE_PREMIUM_FEATURES=false
ENABLE_MAINTENANCE_MODE=false
ENABLE_AI_FEATURES=false
ENABLE_BLOCKCHAIN_FEATURES=true
ENABLE_CRYPTO_PAYMENTS=false
ENABLE_BANK_TRANSFERS=false
ENABLE_REFERRAL_SYSTEM=true

# API Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000

# Game Settings
DEFAULT_GAME_TIMEOUT_MS=300000
MAX_PLAYERS_PER_GROUP=50
HOUSE_EDGE_PERCENTAGE=2.5
MIN_BET_AMOUNT=10
MAX_BET_AMOUNT=1000

# Security
SESSION_SECRET=your_session_secret
ENCRYPTION_KEY=your-encryption-key
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com

# Development
DEBUG=false
LOG_LEVEL=info
ENABLE_API_LOGS=true
TEST_MODE=false

# Authentication Providers
GOOGLE_ID=your-google-client-id
GOOGLE_SECRET=your-google-client-secret
FACEBOOK_ID=your-facebook-client-id
FACEBOOK_SECRET=your-facebook-client-secret
APPLE_ID=your-apple-client-id
APPLE_SECRET=your-apple-client-secret

# Limits & Configurations
MAX_LOGIN_ATTEMPTS=5
SESSION_DURATION_HOURS=24
WITHDRAWAL_DELAY_DAYS=14
MAX_DAILY_WITHDRAWAL=10000
MAX_WEEKLY_WITHDRAWAL=50000
MAX_MONTHLY_WITHDRAWAL=250000

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>
SUPPORT_PHONE="+*********0"

# Crypto Integration (for future updates)
BTC_NETWORK="mainnet"
BTC_API_KEY=your-btc-api-key

# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your-measurement-id

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key 