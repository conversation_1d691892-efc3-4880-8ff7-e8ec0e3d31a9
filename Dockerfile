# Stage 1: Dependencies
FROM node:16-alpine AS deps

WORKDIR /app

# Install dependencies needed for node-gyp, Python, and other native modules
RUN apk add --no-cache python3 python3-dev py3-pip make g++ libc6-compat git

# Copy package files
COPY package*.json requirements.txt ./

# Install dependencies
RUN npm ci
RUN pip3 install --no-cache-dir -r requirements.txt

# Stage 2: Development
FROM node:16-alpine AS development

WORKDIR /app

# Install Python and dependencies
RUN apk add --no-cache python3 python3-dev py3-pip

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /usr/lib/python3.9/site-packages /usr/lib/python3.9/site-packages
COPY . .

# Install development dependencies
RUN npm install

# Expose ports
EXPOSE 3000 9229

# Start development server
CMD ["npm", "run", "dev"]

# Stage 3: Builder
FROM node:16-alpine AS builder

WORKDIR /app

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Set environment variables
ENV NODE_ENV=production \
    NEXT_TELEMETRY_DISABLED=1

# Build application
RUN npm run build

# Stage 4: Production
FROM node:16-alpine AS production

WORKDIR /app

# Install Python and PM2
RUN apk add --no-cache python3 python3-dev py3-pip && \
    npm install -g pm2

# Set environment variables
ENV NODE_ENV=production \
    NEXT_TELEMETRY_DISABLED=1

# Create non-root user
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# Copy necessary files
COPY --from=builder /app/next.config.js ./
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/node_modules ./node_modules
COPY --from=deps /usr/lib/python3.9/site-packages /usr/lib/python3.9/site-packages
COPY ecosystem.config.js ./

# Copy healthcheck script
COPY scripts/docker-healthcheck.sh ./scripts/
RUN chmod +x ./scripts/docker-healthcheck.sh

# Set correct permissions
RUN chown -R nextjs:nodejs /app

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 3000

# Add healthcheck
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD bash ./scripts/docker-healthcheck.sh

# Start the application with PM2
CMD ["pm2-runtime", "start", "ecosystem.config.js"] 