#!/bin/bash

# Load environment variables
source ../.env.production

# Set monitoring directory
MONITOR_DIR="/opt/loomloot/monitoring"
DATE=$(date +%Y%m%d_%H%M%S)

# Create monitoring directory if it doesn't exist
mkdir -p $MONITOR_DIR

# Function to check container health
check_container_health() {
    container=$1
    health=$(docker inspect --format='{{.State.Health.Status}}' $container 2>/dev/null)
    if [ $? -eq 0 ] && [ "$health" = "healthy" ]; then
        echo "✅ $container is healthy"
        return 0
    else
        echo "❌ $container is not healthy"
        return 1
    fi
}

# Function to check service connectivity
check_service() {
    service=$1
    port=$2
    if nc -z localhost $port; then
        echo "✅ $service is accessible on port $port"
        return 0
    else
        echo "❌ $service is not accessible on port $port"
        return 1
    fi
}

# Start monitoring report
REPORT_FILE="$MONITOR_DIR/monitor_report_$DATE.txt"
echo "Monitoring Report - $(date)" > $REPORT_FILE
echo "=========================" >> $REPORT_FILE

# Check container status
echo -e "\nChecking container status..." | tee -a $REPORT_FILE
docker-compose -f ../docker-compose.prod.yml ps >> $REPORT_FILE

# Check container health
echo -e "\nChecking container health..." | tee -a $REPORT_FILE
check_container_health "loomloot_app_1" >> $REPORT_FILE
check_container_health "loomloot_db_1" >> $REPORT_FILE
check_container_health "loomloot_redis_1" >> $REPORT_FILE
check_container_health "loomloot_nginx_1" >> $REPORT_FILE

# Check service connectivity
echo -e "\nChecking service connectivity..." | tee -a $REPORT_FILE
check_service "Next.js" 3000 >> $REPORT_FILE
check_service "PostgreSQL" 5432 >> $REPORT_FILE
check_service "Redis" 6379 >> $REPORT_FILE
check_service "Nginx" 80 >> $REPORT_FILE
check_service "Nginx SSL" 443 >> $REPORT_FILE

# Check disk space
echo -e "\nChecking disk space..." | tee -a $REPORT_FILE
df -h >> $REPORT_FILE

# Check memory usage
echo -e "\nChecking memory usage..." | tee -a $REPORT_FILE
free -h >> $REPORT_FILE

# Check CPU usage
echo -e "\nChecking CPU usage..." | tee -a $REPORT_FILE
top -b -n 1 | head -n 5 >> $REPORT_FILE

# Check Docker resource usage
echo -e "\nChecking Docker resource usage..." | tee -a $REPORT_FILE
docker stats --no-stream >> $REPORT_FILE

# Check application logs for errors
echo -e "\nChecking application logs for errors..." | tee -a $REPORT_FILE
docker logs --since 24h loomloot_app_1 2>&1 | grep -i error >> $REPORT_FILE

# Check database size
echo -e "\nChecking database size..." | tee -a $REPORT_FILE
docker exec loomloot_db_1 psql -U $DB_USER -d $DB_NAME -c "\l+" >> $REPORT_FILE

# Check Redis info
echo -e "\nChecking Redis info..." | tee -a $REPORT_FILE
docker exec loomloot_redis_1 redis-cli info | grep -E "used_memory|connected_clients|total_connections_received" >> $REPORT_FILE

# Check SSL certificate expiry
echo -e "\nChecking SSL certificate expiry..." | tee -a $REPORT_FILE
openssl x509 -enddate -noout -in /etc/nginx/ssl/fullchain.pem >> $REPORT_FILE

# Send report (uncomment and configure as needed)
# mail -s "LoomLoot Monitoring Report - $(date +%Y-%m-%d)" <EMAIL> < $REPORT_FILE

echo "Monitoring completed. Report saved to $REPORT_FILE" 