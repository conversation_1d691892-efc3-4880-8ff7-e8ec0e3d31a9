import { redis } from '../utils/redis';
import { Server as SocketServer } from 'socket.io';
import { WebRTCService } from './webRTCService';

interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  type: 'TEXT' | 'IMAGE' | 'VIDEO' | 'FILE';
  timestamp: number;
  read: boolean;
}

interface Call {
  id: string;
  callerId: string;
  receiverId: string;
  type: 'VOICE' | 'VIDEO';
  status: 'RINGING' | 'CONNECTED' | 'ENDED';
  startTime?: number;
  endTime?: number;
}

export class CommunicationService {
  private static io: SocketServer;
  private static webRTC: WebRTCService;

  static initialize(io: SocketServer) {
    this.io = io;
    this.webRTC = new WebRTCService(io);
    
    io.on('connection', (socket) => {
      const userId = socket.handshake.auth.userId;
      
      // Join user's personal room
      socket.join(`user:${userId}`);

      // Handle messaging
      socket.on('message:send', async (data) => {
        await this.sendMessage(userId, data.receiverId, data.content, data.type);
      });

      // Handle calls
      socket.on('call:initiate', async (data) => {
        await this.initiateCall(userId, data.receiverId, data.type);
      });

      socket.on('call:answer', async (data) => {
        await this.answerCall(data.callId, 'CONNECTED');
      });

      socket.on('call:end', async (data) => {
        await this.endCall(data.callId);
      });
    });
  }

  static async sendMessage(
    senderId: string,
    receiverId: string,
    content: string,
    type: Message['type'] = 'TEXT'
  ): Promise<Message> {
    const message: Message = {
      id: `msg_${Date.now()}`,
      senderId,
      receiverId,
      content,
      type,
      timestamp: Date.now(),
      read: false
    };

    // Store message
    await redis.lpush(`chat:${senderId}:${receiverId}`, JSON.stringify(message));
    await redis.lpush(`chat:${receiverId}:${senderId}`, JSON.stringify(message));

    // Emit to receiver
    this.io.to(`user:${receiverId}`).emit('message:received', message);

    return message;
  }

  static async getMessages(
    userId1: string,
    userId2: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<Message[]> {
    const messages = await redis.lrange(
      `chat:${userId1}:${userId2}`,
      offset,
      offset + limit - 1
    );
    
    return messages.map(msg => JSON.parse(msg));
  }

  static async initiateCall(
    callerId: string,
    receiverId: string,
    type: Call['type']
  ): Promise<Call> {
    const call: Call = {
      id: `call_${Date.now()}`,
      callerId,
      receiverId,
      type,
      status: 'RINGING'
    };

    await redis.set(`call:${call.id}`, JSON.stringify(call));
    
    // Emit to receiver
    this.io.to(`user:${receiverId}`).emit('call:incoming', call);
    
    return call;
  }

  static async answerCall(callId: string, status: Call['status']): Promise<void> {
    const callData = await redis.get(`call:${callId}`);
    if (!callData) throw new Error('Call not found');

    const call: Call = JSON.parse(callData);
    call.status = status;
    call.startTime = Date.now();

    await redis.set(`call:${callId}`, JSON.stringify(call));
    
    // Emit to both parties
    this.io.to(`user:${call.callerId}`).emit('call:status', call);
    this.io.to(`user:${call.receiverId}`).emit('call:status', call);
  }

  static async endCall(callId: string): Promise<void> {
    const callData = await redis.get(`call:${callId}`);
    if (!callData) throw new Error('Call not found');

    const call: Call = JSON.parse(callData);
    call.status = 'ENDED';
    call.endTime = Date.now();

    await redis.set(`call:${callId}`, JSON.stringify(call));
    
    // Emit to both parties
    this.io.to(`user:${call.callerId}`).emit('call:ended', call);
    this.io.to(`user:${call.receiverId}`).emit('call:ended', call);
  }
} 