import { redis } from '../utils/redis';
import { sendEmail } from '../utils/email';
import WebPush from 'web-push';

// Configure WebPush
WebPush.setVapidDetails(
  'mailto:<EMAIL>',
  process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY!,
  process.env.VAPID_PRIVATE_KEY!
);

export interface Notification {
  id: string;
  type: 'PENDING_USER' | 'SUSPICIOUS_ACTIVITY' | 'SYSTEM_ALERT' | 'ID_VERIFICATION';
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  title: string;
  message: string;
  metadata: Record<string, any>;
  createdAt: number;
  readAt?: number;
  actioned?: boolean;
}

export class NotificationService {
  static async create(notification: Omit<Notification, 'id' | 'createdAt'>) {
    const id = `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const fullNotification: Notification = {
      ...notification,
      id,
      createdAt: Date.now()
    };

    // Store notification
    await redis.set(
      `notification:${id}`,
      JSON.stringify(fullNotification)
    );
    await redis.zadd(
      'notifications:all',
      Date.now(),
      id
    );

    // Add to unread set
    await redis.sadd('notifications:unread', id);

    // Send real-time notification if applicable
    await this.sendRealTimeNotification(fullNotification);

    // Send email for high priority notifications
    if (notification.priority === 'HIGH') {
      await this.sendEmailNotification(fullNotification);
    }

    return fullNotification;
  }

  static async markAsRead(id: string, adminId: string) {
    const notificationData = await redis.get(`notification:${id}`);
    if (!notificationData) return null;

    const notification: Notification = JSON.parse(notificationData);
    const updatedNotification = {
      ...notification,
      readAt: Date.now()
    };

    await redis.set(
      `notification:${id}`,
      JSON.stringify(updatedNotification)
    );
    await redis.srem('notifications:unread', id);

    return updatedNotification;
  }

  static async getUnread(limit = 50) {
    const unreadIds = await redis.smembers('notifications:unread');
    const notifications = await Promise.all(
      unreadIds.slice(0, limit).map(async (id) => {
        const data = await redis.get(`notification:${id}`);
        return data ? JSON.parse(data) : null;
      })
    );

    return notifications.filter(Boolean);
  }

  private static async sendRealTimeNotification(notification: Notification) {
    // Get all admin push subscriptions
    const subscriptions = await redis.smembers('push:subscriptions:admin');
    
    const payload = JSON.stringify({
      title: notification.title,
      body: notification.message,
      data: {
        id: notification.id,
        type: notification.type,
        url: `/admin/notifications/${notification.id}`
      }
    });

    // Send to all subscribed admins
    const pushPromises = subscriptions.map(async (subscription) => {
      try {
        await WebPush.sendNotification(
          JSON.parse(subscription),
          payload
        );
      } catch (error) {
        console.error('Push notification failed:', error);
        // Remove invalid subscriptions
        if (error.statusCode === 410) {
          await redis.srem('push:subscriptions:admin', subscription);
        }
      }
    });

    await Promise.allSettled(pushPromises);
  }

  private static async sendEmailNotification(notification: Notification) {
    // Get admin email preferences
    const adminEmails = await redis.smembers('admin:emails');

    const emailPromises = adminEmails.map((email) =>
      sendEmail({
        to: email,
        subject: `[URGENT] ${notification.title}`,
        template: 'admin-notification',
        data: {
          title: notification.title,
          message: notification.message,
          type: notification.type,
          metadata: notification.metadata,
          actionUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/admin/notifications/${notification.id}`
        }
      })
    );

    await Promise.allSettled(emailPromises);
  }
} 