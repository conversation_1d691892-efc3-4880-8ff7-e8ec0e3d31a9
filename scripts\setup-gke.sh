#!/bin/bash

# Exit on error
set -e

# Configuration
PROJECT_ID="loom-efbeb"
CLUSTER_NAME="loomloot-cluster"
REGION="us-central1"
CLUSTER_VERSION="1.27"  # Use latest stable version

# Colors for output
GREEN='\033[0;32m'
NC='\033[0m'

echo -e "${GREEN}Setting up GKE cluster for LoomLoot...${NC}"

# Set GCP project
gcloud config set project $PROJECT_ID

# Create GKE Autopilot cluster
echo -e "${GREEN}Creating GKE Autopilot cluster...${NC}"
gcloud container clusters create-auto $CLUSTER_NAME \
    --region=$REGION \
    --release-channel=regular \
    --network=default \
    --enable-master-authorized-networks \
    --master-authorized-networks=0.0.0.0/0

# Get credentials for kubectl
echo -e "${GREEN}Getting cluster credentials...${NC}"
gcloud container clusters get-credentials $CLUSTER_NAME --region=$REGION

# Create namespace
echo -e "${GREEN}Creating Kubernetes namespace...${NC}"
kubectl apply -f k8s/base/namespace.yaml

# Create secrets
echo -e "${GREEN}Creating secrets...${NC}"
kubectl apply -f k8s/base/postgres-secret.yaml

# Deploy Redis
echo -e "${GREEN}Deploying Redis...${NC}"
kubectl apply -f k8s/base/redis-deployment.yaml

# Deploy API
echo -e "${GREEN}Deploying API...${NC}"
kubectl apply -f k8s/base/api-deployment.yaml

# Setup Ingress
echo -e "${GREEN}Setting up Ingress...${NC}"
kubectl apply -f k8s/base/ingress.yaml

# Setup HPA
echo -e "${GREEN}Setting up Horizontal Pod Autoscaler...${NC}"
kubectl apply -f k8s/base/hpa.yaml

# Wait for deployments to be ready
echo -e "${GREEN}Waiting for deployments to be ready...${NC}"
kubectl wait --for=condition=available --timeout=300s deployment/api -n loomloot
kubectl wait --for=condition=available --timeout=300s deployment/redis -n loomloot

echo -e "${GREEN}Setup complete! Your cluster is ready.${NC}"
echo -e "${GREEN}Next steps:${NC}"
echo "1. Update your DNS settings to point to the Ingress IP"
echo "2. Monitor the cluster with: kubectl get pods -n loomloot"
echo "3. View logs with: kubectl logs -f deployment/api -n loomloot" 