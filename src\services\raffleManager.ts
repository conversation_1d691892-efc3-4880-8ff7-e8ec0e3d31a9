import { Redis } from 'ioredis';
import { v4 as uuidv4 } from 'uuid';
import {
  Ra<PERSON>,
  RaffleStatus,
  RaffleTicket,
  RafflePrize,
  BrandPartner,
  TicketSource,
  PrizeSource,
  RaffleAnalytics
} from '../types/raffle';
import { getRedisClient } from '../utils/redis';

export class RaffleManager {
  private static readonly REDIS_KEYS = {
    RAFFLES: 'raffles',
    TICKETS: 'raffle_tickets',
    BRAND_PARTNERS: 'brand_partners',
    PRIZES: 'raffle_prizes',
    ANALYTICS: 'raffle_analytics'
  };

  private static redis: Redis;

  static async initialize() {
    this.redis = await getRedisClient();
  }

  // Brand Partner Management
  static async createBrandPartner(data: Omit<BrandPartner, 'id' | 'createdAt' | 'lastLogin' | 'verified' | 'salesStats'>): Promise<BrandPartner> {
    const partner: BrandPartner = {
      ...data,
      id: `partner_${uuidv4()}`,
      verified: false,
      createdAt: Date.now(),
      lastLogin: Date.now(),
      salesStats: {
        totalSales: 0,
        totalRaffles: 0,
        successRate: 0,
        revenue: 0
      }
    };

    await this.redis.hset(
      this.REDIS_KEYS.BRAND_PARTNERS,
      partner.id,
      JSON.stringify(partner)
    );

    return partner;
  }

  static async getBrandPartner(id: string): Promise<BrandPartner | null> {
    const partner = await this.redis.hget(this.REDIS_KEYS.BRAND_PARTNERS, id);
    return partner ? JSON.parse(partner) : null;
  }

  static async updateBrandPartner(id: string, updates: Partial<BrandPartner>): Promise<BrandPartner | null> {
    const partner = await this.getBrandPartner(id);
    if (!partner) return null;

    const updatedPartner = {
      ...partner,
      ...updates,
      updatedAt: Date.now()
    };

    await this.redis.hset(
      this.REDIS_KEYS.BRAND_PARTNERS,
      id,
      JSON.stringify(updatedPartner)
    );

    return updatedPartner;
  }

  // Raffle Management
  static async createRaffle(data: {
    title: string;
    description: string;
    prize: RafflePrize;
    ticketPrice: number;
    tokenEntryPrice: number;
    totalTickets: number;
    rules: string[];
    createdBy: string;
  }): Promise<Raffle> {
    const raffle: Raffle = {
      id: `raffle_${uuidv4()}`,
      ...data,
      status: RaffleStatus.PENDING,
      startTime: Date.now(),
      soldTickets: 0,
      remainingTickets: data.totalTickets,
      participants: [],
      featured: false,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      stats: {
        totalRevenue: 0,
        tokenEntries: 0,
        paidEntries: 0,
        conversionRate: 0
      }
    };

    await this.redis.hset(
      this.REDIS_KEYS.RAFFLES,
      raffle.id,
      JSON.stringify(raffle)
    );

    return raffle;
  }

  static async getRaffle(id: string): Promise<Raffle | null> {
    const raffle = await this.redis.hget(this.REDIS_KEYS.RAFFLES, id);
    return raffle ? JSON.parse(raffle) : null;
  }

  static async updateRaffle(id: string, updates: Partial<Raffle>): Promise<Raffle | null> {
    const raffle = await this.getRaffle(id);
    if (!raffle) return null;

    const updatedRaffle = {
      ...raffle,
      ...updates,
      updatedAt: Date.now()
    };

    await this.redis.hset(
      this.REDIS_KEYS.RAFFLES,
      id,
      JSON.stringify(updatedRaffle)
    );

    return updatedRaffle;
  }

  // Ticket Management
  static async purchaseTickets(
    raffleId: string,
    userId: string,
    quantity: number,
    source: TicketSource,
    tokensSpent?: number
  ): Promise<RaffleTicket | null> {
    const raffle = await this.getRaffle(raffleId);
    if (!raffle || raffle.remainingTickets < quantity) return null;

    const startNumber = raffle.soldTickets + 1;
    const ticketNumbers = Array.from(
      { length: quantity },
      (_, i) => startNumber + i
    );

    const ticket: RaffleTicket = {
      id: `ticket_${uuidv4()}`,
      raffleId,
      userId,
      ticketNumbers,
      source,
      tokensSpent,
      purchasePrice: source === TicketSource.PURCHASED ? raffle.ticketPrice * quantity : undefined,
      purchasedAt: Date.now()
    };

    // Update raffle stats
    const updates: Partial<Raffle> = {
      soldTickets: raffle.soldTickets + quantity,
      remainingTickets: raffle.remainingTickets - quantity,
      participants: [
        ...raffle.participants,
        { userId, tickets: ticketNumbers }
      ],
      stats: {
        ...raffle.stats,
        totalRevenue: raffle.stats.totalRevenue + (ticket.purchasePrice || 0),
        tokenEntries: source === TicketSource.TOKEN_EXCHANGE ? raffle.stats.tokenEntries + 1 : raffle.stats.tokenEntries,
        paidEntries: source === TicketSource.PURCHASED ? raffle.stats.paidEntries + 1 : raffle.stats.paidEntries
      }
    };

    // Check if raffle is complete
    if (updates.remainingTickets === 0) {
      updates.status = RaffleStatus.COMPLETED;
      updates.endTime = Date.now();
      
      // Select winner
      const winningNumber = Math.floor(Math.random() * raffle.totalTickets) + 1;
      updates.winningTicketNumber = winningNumber;
      
      const winner = raffle.participants.find(p => 
        p.tickets.includes(winningNumber)
      );
      if (winner) {
        updates.winnerId = winner.userId;
        await this.notifyWinner(raffle, winner.userId, winningNumber);
      }
    }

    await this.updateRaffle(raffleId, updates);

    // Store ticket
    await this.redis.hset(
      this.REDIS_KEYS.TICKETS,
      ticket.id,
      JSON.stringify(ticket)
    );

    return ticket;
  }

  // Analytics
  static async getAnalytics(): Promise<RaffleAnalytics> {
    const raffles = await this.getAllRaffles();
    const partners = await this.getAllBrandPartners();

    const analytics: RaffleAnalytics = {
      totalRevenue: 0,
      activeRaffles: 0,
      completedRaffles: 0,
      totalParticipants: 0,
      averageTicketsSold: 0,
      popularPrizes: [],
      brandPartnerPerformance: [],
      tokenUsageStats: {
        totalTokensSpent: 0,
        averageTokensPerEntry: 0,
        tokenToTicketRatio: 0
      }
    };

    // Calculate metrics
    raffles.forEach(raffle => {
      analytics.totalRevenue += raffle.stats.totalRevenue;
      if (raffle.status === RaffleStatus.ACTIVE) analytics.activeRaffles++;
      if (raffle.status === RaffleStatus.COMPLETED) analytics.completedRaffles++;
      analytics.totalParticipants += raffle.participants.length;
    });

    // Calculate averages
    if (raffles.length > 0) {
      analytics.averageTicketsSold = raffles.reduce((acc, r) => acc + r.soldTickets, 0) / raffles.length;
    }

    // Popular prizes
    const prizeStats = new Map<string, { count: number; revenue: number }>();
    raffles.forEach(raffle => {
      const current = prizeStats.get(raffle.prize.id) || { count: 0, revenue: 0 };
      prizeStats.set(raffle.prize.id, {
        count: current.count + raffle.participants.length,
        revenue: current.revenue + raffle.stats.totalRevenue
      });
    });

    analytics.popularPrizes = Array.from(prizeStats.entries())
      .map(([prizeId, stats]) => ({
        prizeId,
        participantCount: stats.count,
        revenue: stats.revenue
      }))
      .sort((a, b) => b.participantCount - a.participantCount)
      .slice(0, 10);

    // Brand partner performance
    partners.forEach(partner => {
      const partnerRaffles = raffles.filter(r => r.prize.brandPartnerId === partner.id);
      if (partnerRaffles.length > 0) {
        analytics.brandPartnerPerformance.push({
          partnerId: partner.id,
          rafflesHosted: partnerRaffles.length,
          totalRevenue: partnerRaffles.reduce((acc, r) => acc + r.stats.totalRevenue, 0),
          averageParticipation: partnerRaffles.reduce((acc, r) => acc + r.participants.length, 0) / partnerRaffles.length
        });
      }
    });

    return analytics;
  }

  // Helper methods
  private static async getAllRaffles(): Promise<Raffle[]> {
    const raffles = await this.redis.hgetall(this.REDIS_KEYS.RAFFLES);
    return Object.values(raffles).map(r => JSON.parse(r));
  }

  private static async getAllBrandPartners(): Promise<BrandPartner[]> {
    const partners = await this.redis.hgetall(this.REDIS_KEYS.BRAND_PARTNERS);
    return Object.values(partners).map(p => JSON.parse(p));
  }

  private static async notifyWinner(raffle: Raffle, userId: string, winningNumber: number) {
    // Implement winner notification logic here
    // This could include email, push notifications, etc.
    console.log(`Winner selected for raffle ${raffle.id}: User ${userId} with ticket ${winningNumber}`);
  }
} 