import { supabase } from '../config/supabase';

// Game State Management
export const saveGameStateSupabase = async (gameId: string, gameData: any) => {
  const { data, error } = await supabase
    .from('games')
    .upsert({ id: gameId, ...gameData, updated_at: new Date() });
  
  if (error) throw error;
  return data;
};

// Player Management
export const updatePlayerStatus = async (userId: string, status: string) => {
  const { data, error } = await supabase
    .from('players')
    .update({ status, updated_at: new Date() })
    .eq('user_id', userId);
  
  if (error) throw error;
  return data;
};

// Token Management
export const updateTokenBalanceSupabase = async (userId: string, amount: number) => {
  const { data, error } = await supabase.rpc('update_token_balance', {
    user_id: userId,
    amount: amount
  });
  
  if (error) throw error;
  return data;
};

// Game History
export const getPlayerGameHistory = async (userId: string) => {
  const { data, error } = await supabase
    .from('game_history')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });
  
  if (error) throw error;
  return data;
}; 