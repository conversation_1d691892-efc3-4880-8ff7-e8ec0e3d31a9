{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020", "DOM", "DOM.Iterable"], "jsx": "react-jsx", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "outDir": "./dist", "baseUrl": "./src", "paths": {"@/*": ["*"]}, "allowJs": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "types": ["node", "jest", "react"], "typeRoots": ["./node_modules/@types"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}