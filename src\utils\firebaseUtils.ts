import { auth, db, storage } from '../config/firebase';
import { 
  collection, 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  query, 
  where, 
  getDocs 
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';

// User Profile Operations
export const createUserProfile = async (userId: string, userData: any) => {
  const userRef = doc(db, 'users', userId);
  await setDoc(userRef, {
    ...userData,
    createdAt: new Date(),
    updatedAt: new Date()
  });
};

export const updateUserProfile = async (userId: string, userData: any) => {
  const userRef = doc(db, 'users', userId);
  await updateDoc(userRef, {
    ...userData,
    updatedAt: new Date()
  });
};

export const getUserProfile = async (userId: string) => {
  const userRef = doc(db, 'users', userId);
  const userSnap = await getDoc(userRef);
  return userSnap.exists() ? userSnap.data() : null;
};

// Storage Operations
export const uploadFile = async (file: File, path: string) => {
  const storageRef = ref(storage, path);
  await uploadBytes(storageRef, file);
  return await getDownloadURL(storageRef);
};

// Game Data Operations
export const saveGameState = async (gameId: string, gameData: any) => {
  const gameRef = doc(db, 'games', gameId);
  await setDoc(gameRef, {
    ...gameData,
    updatedAt: new Date()
  });
};

export const getGameState = async (gameId: string) => {
  const gameRef = doc(db, 'games', gameId);
  const gameSnap = await getDoc(gameRef);
  return gameSnap.exists() ? gameSnap.data() : null;
};

// Token Operations
export const updateTokenBalance = async (userId: string, amount: number) => {
  const userRef = doc(db, 'users', userId);
  const userSnap = await getDoc(userRef);
  
  if (userSnap.exists()) {
    const currentBalance = userSnap.data().tokenBalance || 0;
    await updateDoc(userRef, {
      tokenBalance: currentBalance + amount,
      updatedAt: new Date()
    });
  }
}; 