# CoderBot AI Assistant

An advanced AI-powered coding assistant for VS Code that helps you write, analyze, and optimize code. Built with Claude 3.5 Sonnet, it provides intelligent code generation, project management, and development workflow automation.

## Features

### 🤖 AI-Powered Code Generation
- Smart code completion and suggestions
- Function and class generation
- Component scaffolding
- Test case generation
- Documentation generation

### 📊 Code Analysis
- Security vulnerability scanning
- Performance optimization suggestions
- Code complexity analysis
- Best practices enforcement
- Automated code reviews

### 🧪 Testing
- Automated test case generation
- Unit test scaffolding
- Integration test setup
- Test coverage analysis
- Test optimization suggestions

### 📦 Project Management
- Project initialization and setup
- Dependency management
- Git integration
- Task tracking
- Progress monitoring

### 🔧 Development Workflow
- Code optimization
- Automated refactoring
- Documentation updates
- Git commit message generation
- PR review assistance

## Requirements

- VS Code 1.60.0 or higher
- Node.js 14.0.0 or higher
- Redis server (for caching and state management)

## Installation

1. Install the extension from the VS Code Marketplace
2. Set up your Anthropic API key:
   ```
   ANTHROPIC_API_KEY=your-api-key
   ```
3. Configure Redis connection (optional):
   ```
   REDIS_URL=redis://localhost:6379
   ```

## Usage

### Creating a New Project
1. Press `Ctrl+Shift+P` (Windows/Linux) or `Cmd+Shift+P` (macOS)
2. Type "CoderBot: Create New Project"
3. Follow the prompts to configure your project

### Analyzing Code
1. Open a file you want to analyze
2. Press `Ctrl+Shift+P` and type "CoderBot: Analyze Code"
3. Choose the type of analysis to perform

### Generating Tests
1. Open the file you want to generate tests for
2. Press `Ctrl+Shift+P` and type "CoderBot: Generate Tests"
3. Tests will be generated in the appropriate test directory

### Optimizing Code
1. Open the file you want to optimize
2. Press `Ctrl+Shift+P` and type "CoderBot: Optimize Code"
3. Review and accept the suggested optimizations

## Configuration

You can customize CoderBot's behavior through VS Code settings:

```json
{
  "coderbot.ai.model": "claude-3.5-sonnet",
  "coderbot.ai.maxTokens": 200000,
  "coderbot.ai.temperature": 0.7,
  "coderbot.project.autoSave": true,
  "coderbot.project.autoFormat": true,
  "coderbot.project.gitIntegration": true
}
```

## Development

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Build the extension:
   ```bash
   npm run compile
   ```
4. Launch the extension in debug mode:
   - Press F5 in VS Code
   - Select "Extension Development Host"

## Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

If you encounter any issues or have questions, please:
1. Check the [FAQ](docs/FAQ.md)
2. Search existing [Issues](https://github.com/your-repo/coderbot-vscode/issues)
3. Create a new issue if needed

## Acknowledgments

- Built with [Claude 3.5 Sonnet](https://www.anthropic.com/claude)
- Inspired by various coding assistants and AI tools
- Thanks to the VS Code extension development community 