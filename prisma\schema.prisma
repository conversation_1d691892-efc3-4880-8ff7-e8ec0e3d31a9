datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id              String    @id @default(cuid())
  email           String    @unique
  username        String    @unique
  password        String
  name            String?
  image           String?
  phoneNumber     String?   @unique
  dateOfBirth     DateTime
  location        String
  isAd<PERSON>   @default(false)
  isVerified      Boolean   @default(false)
  twoFactorSecret String?
  referralCode    String?   @unique
  referredBy      String?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  wallet          Wallet?
  transactions    Transaction[]
  withdrawals     Withdrawal[]
  accounts        Account[]
  sessions        Session[]
  verificationDocs VerificationDocument[]
}

model VerificationDocument {
  id            String    @id @default(cuid())
  userId        String
  type          String    // GOVERNMENT_ID, UTILITY_BILL, SELFIE
  documentUrl   String
  status        String    // PENDING, APPROVED, REJECTED
  reviewedAt    DateTime?
  reviewedBy    String?
  expiresAt     DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Wallet {
  id              String    @id @default(cuid())
  userId          String    @unique
  tokenBalance    Float     @default(0)
  loomCoinBalance Float     @default(0)
  lockedAmount    Float     @default(0)
  totalDeposited  Float     @default(0)
  totalWithdrawn  Float     @default(0)
  isLocked        Boolean   @default(false)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Transaction {
  id              String    @id @default(cuid())
  userId          String
  type            String    // DEPOSIT, WITHDRAWAL, GAME_ENTRY, GAME_WIN, GAME_LOSS
  amount          Float
  currency        String    // TOKEN, LOOMCOIN
  status          String    // PENDING, COMPLETED, FAILED
  paymentMethod   String?
  paymentDetails  Json?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Withdrawal {
  id              String    @id @default(cuid())
  userId          String
  amount          Float
  currency        String    // TOKEN, LOOMCOIN
  method          String    // BANK_TRANSFER, CRYPTO
  status          String    // PENDING, COMPLETED, FAILED
  payoutDetails   Json?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Account {
  id                 String  @id @default(cuid())
  userId             String
  type               String
  provider           String
  providerAccountId  String
  refresh_token      String?  @db.Text
  access_token       String?  @db.Text
  expires_at         Int?
  token_type         String?
  scope              String?
  id_token           String?  @db.Text
  session_state      String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
} 