import { redis } from '../utils/redis';
import { 
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  ListObjectsV2Command,
  DeleteObjectsCommand
} from '@aws-sdk/client-s3';
import { PrismaClient } from '@prisma/client';
import { createGzip } from 'zlib';
import { promisify } from 'util';
import { pipeline } from 'stream';
import { createReadStream, createWriteStream } from 'fs';
import { ReadStream } from 'fs';
import winston from 'winston';
import { execSync } from 'child_process';
import { stat } from 'fs/promises';
import { Readable } from 'stream';

interface S3ListObject {
  Key?: string;
  LastModified?: Date;
}

const pipe = promisify(pipeline);
const prisma = new PrismaClient();

// Configure logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'backup-service' },
  transports: [
    new winston.transports.File({ filename: 'logs/backup-error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/backup.log' }),
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

export class BackupService {
  private static instance: BackupService;
  private backupInProgress: boolean = false;
  private s3Client: S3Client;
  private lastBackupStats: {
    startTime: Date;
    endTime?: Date;
    type: 'full' | 'incremental';
    status: 'success' | 'failed';
    error?: string;
    filesBackedUp: number;
    totalSize: number;
  } | null = null;

  private constructor() {
    this.s3Client = new S3Client({
      region: process.env.AWS_REGION || 'us-east-1',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!
      }
    });
  }

  public static getInstance(): BackupService {
    if (!BackupService.instance) {
      BackupService.instance = new BackupService();
    }
    return BackupService.instance;
  }

  async performBackup(type: 'full' | 'incremental' = 'incremental'): Promise<void> {
    if (this.backupInProgress) {
      logger.warn('Backup already in progress');
      return;
    }

    this.backupInProgress = true;
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

    try {
      logger.info(`Starting ${type} backup`, { timestamp });
      this.lastBackupStats = {
        startTime: new Date(),
        type,
        status: 'success',
        filesBackedUp: 0,
        totalSize: 0
      };

      // Backup database
      await this.backupDatabase(timestamp, type);
      logger.info('Database backup completed');

      // Backup Redis
      await this.backupRedis(timestamp);
      logger.info('Redis backup completed');

      // Upload to S3
      await this.uploadToS3(timestamp, type);
      logger.info('S3 upload completed');

      // Clean up old backups
      await this.cleanupOldBackups();
      logger.info('Old backups cleaned up');

      this.lastBackupStats.endTime = new Date();
      await this.logBackupStats();
      
      logger.info(`${type} backup completed successfully`);
    } catch (error) {
      logger.error('Backup failed:', error);
      if (this.lastBackupStats) {
        this.lastBackupStats.status = 'failed';
        this.lastBackupStats.error = error instanceof Error ? error.message : 'Unknown error';
        this.lastBackupStats.endTime = new Date();
        await this.logBackupStats();
      }
      throw error;
    } finally {
      this.backupInProgress = false;
    }
  }

  private async logBackupStats(): Promise<void> {
    if (!this.lastBackupStats) return;

    try {
      await redis.lpush(
        'backup:history',
        JSON.stringify({
          ...this.lastBackupStats,
          startTime: this.lastBackupStats.startTime.toISOString(),
          endTime: this.lastBackupStats.endTime?.toISOString()
        })
      );

      // Keep only last 100 backup records
      await redis.ltrim('backup:history', 0, 99);

      logger.info('Backup stats logged successfully', this.lastBackupStats);
    } catch (error) {
      logger.error('Failed to log backup stats:', error);
    }
  }

  private async backupDatabase(timestamp: string, type: 'full' | 'incremental'): Promise<void> {
    try {
      const backupPath = `/tmp/db-backup-${timestamp}.sql`;
      
      // Use pg_dump for PostgreSQL backup
      execSync(`pg_dump ${process.env.DATABASE_URL} > ${backupPath}`);

      const stats = await stat(backupPath);
      if (this.lastBackupStats) {
        this.lastBackupStats.filesBackedUp++;
        this.lastBackupStats.totalSize += stats.size;
      }

      logger.info('Database backup created successfully', { timestamp, type });
    } catch (error) {
      logger.error('Database backup failed:', error);
      throw error;
    }
  }

  private async backupRedis(timestamp: string): Promise<void> {
    try {
      const backupPath = `/tmp/redis-backup-${timestamp}.rdb`;
      
      // Save Redis data
      await redis.save();

      // Copy Redis dump file
      execSync(`cp $(redis-cli config get dir | awk 'NR==2')/dump.rdb ${backupPath}`);

      const stats = await stat(backupPath);
      if (this.lastBackupStats) {
        this.lastBackupStats.filesBackedUp++;
        this.lastBackupStats.totalSize += stats.size;
      }

      logger.info('Redis backup created successfully', { timestamp });
    } catch (error) {
      logger.error('Redis backup failed:', error);
      throw error;
    }
  }

  private async uploadToS3(timestamp: string, type: string): Promise<void> {
    try {
      const files = [
        `/tmp/db-backup-${timestamp}.sql`,
        `/tmp/redis-backup-${timestamp}.rdb`
      ];

      for (const file of files) {
        const fileStream = createReadStream(file);
        
        await this.s3Client.send(new PutObjectCommand({
          Bucket: process.env.S3_BUCKET_NAME,
          Key: `backups/${type}/${timestamp}/${file.split('/').pop()}`,
          Body: fileStream
        }));

        logger.info('File uploaded to S3 successfully', { file });
      }
    } catch (error) {
      logger.error('S3 upload failed:', error);
      throw error;
    }
  }

  private async cleanupOldBackups(): Promise<void> {
    try {
      const response = await this.s3Client.send(new ListObjectsV2Command({
        Bucket: process.env.S3_BUCKET_NAME,
        Prefix: 'backups/'
      }));

      const objects = response.Contents;
      if (!objects || objects.length === 0) return;

      // Keep last 7 daily backups and last 4 weekly backups
      const dailyBackups = objects
        .filter(obj => obj.Key?.includes('/incremental/'))
        .sort((a, b) => (b.LastModified?.getTime() || 0) - (a.LastModified?.getTime() || 0))
        .slice(7);

      const weeklyBackups = objects
        .filter(obj => obj.Key?.includes('/full/'))
        .sort((a, b) => (b.LastModified?.getTime() || 0) - (a.LastModified?.getTime() || 0))
        .slice(4);

      const objectsToDelete = [...dailyBackups, ...weeklyBackups]
        .map(obj => ({ Key: obj.Key! }));

      if (objectsToDelete.length > 0) {
        await this.s3Client.send(new DeleteObjectsCommand({
          Bucket: process.env.S3_BUCKET_NAME,
          Delete: { Objects: objectsToDelete }
        }));

        logger.info('Old backups cleaned up successfully', {
          deletedCount: objectsToDelete.length
        });
      }
    } catch (error) {
      logger.error('Cleanup of old backups failed:', error);
      throw error;
    }
  }

  async restoreFromBackup(timestamp: string): Promise<void> {
    if (this.backupInProgress) {
      throw new Error('Cannot restore while backup is in progress');
    }

    try {
      logger.info('Starting backup restoration', { timestamp });

      const files = await this.downloadBackupFiles(timestamp);
      
      // Restore database
      await this.restoreDatabase(files.database);
      logger.info('Database restored successfully');

      // Restore Redis
      await this.restoreRedis(files.redis);
      logger.info('Redis restored successfully');

      logger.info('Backup restoration completed successfully');
    } catch (error) {
      logger.error('Backup restoration failed:', error);
      throw error;
    }
  }

  private async downloadBackupFiles(timestamp: string): Promise<{ database: string; redis: string }> {
    try {
      const dbFile = `/tmp/restore-db-${timestamp}.sql`;
      const redisFile = `/tmp/restore-redis-${timestamp}.rdb`;

      // Download database backup
      const dbResponse = await this.s3Client.send(new GetObjectCommand({
        Bucket: process.env.S3_BUCKET_NAME,
        Key: `backups/full/${timestamp}/db-backup-${timestamp}.sql`
      }));

      // Download Redis backup
      const redisResponse = await this.s3Client.send(new GetObjectCommand({
        Bucket: process.env.S3_BUCKET_NAME,
        Key: `backups/full/${timestamp}/redis-backup-${timestamp}.rdb`
      }));

      // Save files locally
      if (dbResponse.Body && redisResponse.Body) {
        await Promise.all([
          new Promise<void>((resolve, reject) => {
            const writeStream = createWriteStream(dbFile);
            writeStream.on('finish', () => resolve());
            writeStream.on('error', (err) => reject(err));
            if (dbResponse.Body instanceof Readable) {
              (dbResponse.Body as Readable).pipe(writeStream);
            } else {
              reject(new Error('Database backup response body is not a readable stream'));
            }
          }),
          new Promise<void>((resolve, reject) => {
            const writeStream = createWriteStream(redisFile);
            writeStream.on('finish', () => resolve());
            writeStream.on('error', (err) => reject(err));
            if (redisResponse.Body instanceof Readable) {
              (redisResponse.Body as Readable).pipe(writeStream);
            } else {
              reject(new Error('Redis backup response body is not a readable stream'));
            }
          })
        ]);
      }

      return { database: dbFile, redis: redisFile };
    } catch (error) {
      logger.error('Failed to download backup files:', error);
      throw error;
    }
  }

  private async restoreDatabase(file: string): Promise<void> {
    try {
      execSync(`psql ${process.env.DATABASE_URL} < ${file}`);
    } catch (error) {
      logger.error('Database restoration failed:', error);
      throw error;
    }
  }

  private async restoreRedis(file: string): Promise<void> {
    try {
      // Stop Redis server
      execSync('redis-cli shutdown');

      // Replace Redis dump file
      execSync(`cp ${file} $(redis-cli config get dir | awk 'NR==2')/dump.rdb`);

      // Start Redis server
      execSync('redis-server --daemonize yes');
    } catch (error) {
      logger.error('Redis restoration failed:', error);
      throw error;
    }
  }
} 