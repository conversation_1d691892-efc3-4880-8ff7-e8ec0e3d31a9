const winston = require('winston');
const { createLogger, format, transports } = winston;

// Custom format for structured logging
const customFormat = format.combine(
  format.timestamp(),
  format.metadata({ fillExcept: ['message', 'level', 'timestamp'] }),
  format.json()
);

// Create the logger instance
const logger = createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: customFormat,
  defaultMeta: { service: 'loomloot' },
  transports: [
    new transports.File({ filename: 'logs/error.log', level: 'error' }),
    new transports.File({ filename: 'logs/combined.log' }),
    new transports.Console({
      format: format.combine(
        format.colorize(),
        format.simple()
      )
    })
  ]
});

// Add monitoring metrics
const metrics = {
  requestCount: 0,
  errorCount: 0,
  activeUsers: 0,
  responseTime: [],
  lastError: null
};

// Monitoring middleware
const monitoringMiddleware = (req, res, next) => {
  const start = Date.now();
  
  metrics.requestCount++;
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    metrics.responseTime.push(duration);
    
    // Keep only last 1000 response times
    if (metrics.responseTime.length > 1000) {
      metrics.responseTime.shift();
    }
    
    // Log request details
    logger.info('Request completed', {
      method: req.method,
      path: req.path,
      statusCode: res.statusCode,
      duration
    });
  });
  
  next();
};

// Error monitoring
const errorHandler = (err, req, res, next) => {
  metrics.errorCount++;
  metrics.lastError = {
    message: err.message,
    stack: err.stack,
    timestamp: new Date()
  };
  
  logger.error('Application error', {
    error: err.message,
    stack: err.stack,
    path: req.path,
    method: req.method
  });
  
  next(err);
};

// Health check endpoint data
const getHealthMetrics = () => {
  const avgResponseTime = metrics.responseTime.length > 0
    ? metrics.responseTime.reduce((a, b) => a + b, 0) / metrics.responseTime.length
    : 0;
    
  return {
    uptime: process.uptime(),
    requestCount: metrics.requestCount,
    errorCount: metrics.errorCount,
    activeUsers: metrics.activeUsers,
    avgResponseTime,
    lastError: metrics.lastError,
    memoryUsage: process.memoryUsage(),
    timestamp: new Date()
  };
};

module.exports = {
  logger,
  metrics,
  monitoringMiddleware,
  errorHandler,
  getHealthMetrics
}; 