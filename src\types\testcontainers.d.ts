declare module 'testcontainers' {
  export class GenericContainer {
    constructor(image: string);
    withExposedPorts(...ports: number[]): this;
    withEnvironment(env: Record<string, string>): this;
    withWaitStrategy(strategy: WaitStrategy): this;
    start(): Promise<StartedTestContainer>;
  }

  export class StartedTestContainer {
    getMappedPort(port: number): number;
    stop(): Promise<void>;
  }

  export class Wait {
    static forLogMessage(message: string): WaitStrategy;
  }

  interface WaitStrategy {
    // Base interface for wait strategies
  }
} 