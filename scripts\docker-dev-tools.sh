#!/bin/bash

# Set colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Function to check Python environment
check_python_env() {
    if [ ! -d "venv" ]; then
        echo -e "${YELLOW}Creating Python virtual environment...${NC}"
        python3 -m venv venv
        source venv/bin/activate
        pip install -r tools/requirements.txt
    else
        source venv/bin/activate
    fi
}

# Function to take screenshot
take_screenshot() {
    local url=$1
    local output=${2:-"screenshot.png"}
    local width=${3:-1920}
    local height=${4:-1080}
    
    echo -e "${YELLOW}Taking screenshot of $url...${NC}"
    venv/bin/python tools/screenshot_utils.py "$url" --output "$output" --width "$width" --height "$height"
}

# Function to verify screenshot with LLM
verify_screenshot() {
    local prompt=$1
    local image=$2
    local provider=${3:-"anthropic"}
    
    echo -e "${YELLOW}Verifying screenshot with $provider...${NC}"
    venv/bin/python tools/llm_api.py --prompt "$prompt" --provider "$provider" --image "$image"
}

# Function to scrape web content
scrape_web() {
    local urls=("$@")
    local max_concurrent=3
    
    echo -e "${YELLOW}Scraping web content...${NC}"
    venv/bin/python tools/web_scraper.py --max-concurrent "$max_concurrent" "${urls[@]}"
}

# Function to search web
search_web() {
    local query=$1
    
    echo -e "${YELLOW}Searching web for: $query${NC}"
    venv/bin/python tools/search_engine.py "$query"
}

# Function to query LLM
query_llm() {
    local prompt=$1
    local provider=${2:-"anthropic"}
    
    echo -e "${YELLOW}Querying LLM ($provider)...${NC}"
    venv/bin/python tools/llm_api.py --prompt "$prompt" --provider "$provider"
}

# Function to display usage
usage() {
    echo "Usage: $0 [command] [args...]"
    echo
    echo "Commands:"
    echo "  screenshot <url> [output] [width] [height]  Take a screenshot of a URL"
    echo "  verify <prompt> <image> [provider]         Verify screenshot with LLM"
    echo "  scrape <url...>                           Scrape web content"
    echo "  search <query>                            Search web"
    echo "  llm <prompt> [provider]                   Query LLM"
    echo "  help                                      Show this help message"
}

# Main script
case "$1" in
    screenshot)
        check_python_env
        take_screenshot "${@:2}"
        ;;
    verify)
        check_python_env
        verify_screenshot "${@:2}"
        ;;
    scrape)
        check_python_env
        scrape_web "${@:2}"
        ;;
    search)
        check_python_env
        search_web "${@:2}"
        ;;
    llm)
        check_python_env
        query_llm "${@:2}"
        ;;
    help)
        usage
        ;;
    *)
        usage
        exit 1
        ;;
esac

exit 0 